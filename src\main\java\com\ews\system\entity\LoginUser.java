package com.ews.system.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Transient;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;

public class LoginUser {

	private Long userId;
	private String username;
	private String nickName;
	private int isAvailable;
	private String roleName;
	
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date gmtCreateDate;
	
	/**
	 * 创建日期的long值 列表显示
	 */
	private Long gmtCreateDateLong;
	
	
	
	/**
	 * 所属店铺名称
	 */
	private String storeName;
	/**
	 * 所属部门名称
	 */
	private String depaName;
	
	/**
	 * 联系电话
	 */
	private String mobile;
	
	/**
	 * 列表显示的序号
	 */
	private Integer sort;
	
	/**
	 * 权限标识
	 */
	private List roles;
	
	/**
	 * 用户头像
	 */
	private String userIcon;
	
	/**
	 * 用户角色名称
	 */
	private List rolesNameArr;
	
	
	/**
	 * 所属店铺id  
	 */
	protected Long storeId;
	/**
	 * 所属部门id  
	 */
	protected Long departmentId;
	
	

	protected Integer roleType;
	
	
	protected Long reId;
	
	
	
	protected String saleSerial;
	
	
	@Transient
	protected  List ruleList;
	
	
	/**
	 * 代理数
	 */
	@Transient
	protected  Integer agentQty;
	
	/**
	 * CRM用户数
	 */
	@Transient
	protected  Integer userInfoQty;
	/**
	 * 交易用户数
	 */
	@Transient
	protected  Integer tradeAccountQty;
	
	/**
	 * 交易账号余额
	 */
	@Transient
	protected  Double tradeAccountAmount;
	
	/**
	 * 入金总额
	 * @return
	 */
	@Transient
	protected  Double fundAmountDepositTotal;
	
	/**
	 * 出金总额
	 * @return
	 */
	@Transient
	protected  Double fundAmountWithdrawTotal;
	
	
	public Integer getAgentQty() {
		return agentQty;
	}
	public void setAgentQty(Integer agentQty) {
		this.agentQty = agentQty;
	}
	public Integer getUserInfoQty() {
		return userInfoQty;
	}
	public void setUserInfoQty(Integer userInfoQty) {
		this.userInfoQty = userInfoQty;
	}
	public Integer getTradeAccountQty() {
		return tradeAccountQty;
	}
	public void setTradeAccountQty(Integer tradeAccountQty) {
		this.tradeAccountQty = tradeAccountQty;
	}
	public Double getTradeAccountAmount() {
		return tradeAccountAmount;
	}
	public void setTradeAccountAmount(Double tradeAccountAmount) {
		this.tradeAccountAmount = tradeAccountAmount;
	}
	public List getRuleList() {
		return ruleList;
	}
	public void setRuleList(List ruleList) {
		this.ruleList = ruleList;
	}
	public String getSaleSerial() {
		return saleSerial;
	}
	public void setSaleSerial(String saleSerial) {
		this.saleSerial = saleSerial;
	}
	public Integer getRoleType() {
		return roleType;
	}
	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}
	public Long getReId() {
		return reId;
	}
	public void setReId(Long reId) {
		this.reId = reId;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getStoreName() {
		return storeName;
	}
	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}
	public String getDepaName() {
		return depaName;
	}
	public void setDepaName(String depaName) {
		this.depaName = depaName;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public String getNickName() {
		return nickName;
	}
	public void setNickName(String nickName) {
		this.nickName = nickName;
	}
	public int getIsAvailable() {
		return isAvailable;
	}
	public void setIsAvailable(int isAvailable) {
		this.isAvailable = isAvailable;
	}
	
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public Date getGmtCreateDate() {
		return gmtCreateDate;
	}
	public void setGmtCreateDate(Date gmtCreateDate) {
		this.gmtCreateDate = gmtCreateDate;
	}
	public Long getGmtCreateDateLong() {
		return gmtCreateDateLong;
	}
	public void setGmtCreateDateLong(Long gmtCreateDateLong) {
		this.gmtCreateDateLong = gmtCreateDateLong;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	
	public List getRoles() {
		return roles;
	}
	public void setRoles(List roles) {
		this.roles = roles;
	}
	public String getUserIcon() {
		return userIcon;
	}
	public void setUserIcon(String userIcon) {
		this.userIcon = userIcon;
	}
	public List getRolesNameArr() {
		return rolesNameArr;
	}
	public void setRolesNameArr(List rolesNameArr) {
		this.rolesNameArr = rolesNameArr;
	}
	public Long getStoreId() {
		return storeId;
	}
	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}
	public Long getDepartmentId() {
		return departmentId;
	}
	public void setDepartmentId(Long departmentId) {
		this.departmentId = departmentId;
	}
	public Double getFundAmountDepositTotal() {
		return fundAmountDepositTotal;
	}
	public void setFundAmountDepositTotal(Double fundAmountDepositTotal) {
		this.fundAmountDepositTotal = fundAmountDepositTotal;
	}
	public Double getFundAmountWithdrawTotal() {
		return fundAmountWithdrawTotal;
	}
	public void setFundAmountWithdrawTotal(Double fundAmountWithdrawTotal) {
		this.fundAmountWithdrawTotal = fundAmountWithdrawTotal;
	}
	
	
	
}

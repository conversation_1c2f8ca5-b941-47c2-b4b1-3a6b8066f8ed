package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;


public class MyWebsocketClient4TransferIn extends WebSocketClient {
	private TradeAccountService tradeAccountService;
	

	
	
	private TransferInfoService transferInfoService;
	
	
	
	
	private TransferInfo transferInfo;
	
	
	
	public TransferInfo getTransferInfo() {
		return transferInfo;
	}

	public void setTransferInfo(TransferInfo transferInfo) {
		this.transferInfo = transferInfo;
	}

	public TransferInfoService getTransferInfoService() {
		return transferInfoService;
	}

	public void setTransferInfoService(TransferInfoService transferInfoService) {
		this.transferInfoService = transferInfoService;
	}

	
	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}

	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}

	public MyWebsocketClient4TransferIn(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
			TransferInfo transferInfo_temp=this.getTransferInfoService().findById(new Long(jsStr.get("reqid").toString().split("___")[1]));
               if(jsStr.get("error").toString().equals("")) {
				
            	   transferInfo_temp.setInStatus(1);
            	   transferInfo_temp.setTransferStatus(2);
            	   transferInfo_temp.setInOrderId(jsStr.get("orderid").toString());
				this.transferInfoService.saveOrUpdate(transferInfo_temp);
				
				}else {
					transferInfo_temp.setInStatus(2);
					transferInfo_temp.setTransferStatus(3);
					this.transferInfoService.saveOrUpdate(transferInfo_temp);
					
						//  close();
				}
			
		}
		new Thread(){
    		public void run(){
    			try {
    			Thread.sleep(30000L);
    			     close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

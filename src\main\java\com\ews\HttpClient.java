package com.ews;
import okhttp3.*;
import org.json.JSONObject;
import javax.net.ssl.*;
import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.util.Arrays;

public class HttpClient {
    private final String HTTPS = "https://";
    private String server;
    private OkHttpClient client;

    
    public HttpClient(String server) {
       // this.client = new OkHttpClient();
        this.client = HttpClient.trustAllSslClient(new OkHttpClient());
        this.server = HTTPS + server;
    }

  
    public static byte[] copyBuffer(byte[] ar1, byte[] ar2) {
        byte[] result = new byte[ar1.length + ar2.length];
        // ---
        try {
            System.arraycopy(ar1, 0, result, 0, ar1.length);
            System.arraycopy(ar2, 0, result, ar1.length, ar2.length);
        } catch(Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
        // ---
        return result;
    }

   
    public boolean sendAuth(String login, String password, String build, String agent) {
        String response = "";
        if(server.isEmpty()) {
            return (false);
        }
        // ---
        String path = "/auth_start?version=" + build + "&agent=" + agent + "&login=" + login + "&type=manager";
        // ---
        try {
            response = sendGet(path);
        } catch(Exception ex) {
            System.out.println(ex.toString());
            return false;
        }
        // ---
        if(response != null && !response.isEmpty()) {
            JSONObject json_obj = new JSONObject(response);
            String retcode = json_obj.getString("retcode");
            if(retcode.equals("0 Done")) {
                String srv_rand = json_obj.getString("srv_rand");
                byte[] byte_array = DatatypeConverter.parseHexBinary(srv_rand);
                printByteResult(byte_array);
                byte[] cli_rand_buf = getRandomHex();
                printByteResult(cli_rand_buf);
                String cli_rand_string = DatatypeConverter.printHexBinary(cli_rand_buf);
                String srv_rand_answer = getHashFromPassword(password, byte_array);
                String auth_response = "/auth_answer?srv_rand_answer=" + srv_rand_answer + "&cli_rand=" + cli_rand_string;
                try {
                    sendGet(auth_response);
                } catch(Exception ex) {
                    System.out.println(ex.toString());
                }
            }
            return (true);
        } else {
            return (false);
        }
    }

  
    
    
    public JSONObject getHistoryOrder(String login,String from,String to) {
      
        String request = "/api/deal/get_batch?login="+login+"&from="+from+"&to="+to;
        String response = "";
        try {
            response = sendGet(request);
        } catch(Exception ex) {
            System.out.println(ex.toString());
        }
        //---
        if(response != null && !response.isEmpty()) {
          //  System.out.println(response);
            JSONObject json_obj = new JSONObject(response);
            return json_obj;
        } else {
            return new JSONObject();
        }
    }
    
    
    public JSONObject getUserInfoByGroup(String group) {
        
        String request = "/api/user/get_batch?group="+group ;
        String response = "";
        try {
            response = sendGet(request);
        } catch(Exception ex) {
            System.out.println(ex.toString());
        }
        //---
        if(response != null && !response.isEmpty()) {
           // System.out.println(response);
            JSONObject json_obj = new JSONObject(response);
            return json_obj;
        } else {
            return  new JSONObject();
        }
    }
    
    
    
  public JSONObject getTradeInfoByGroup(String group) {
        
        String request = "/api/user/account/get_batch?group="+group ;
        String response = "";
        try {
            response = sendGet(request);
        } catch(Exception ex) {
            System.out.println(ex.toString());
        }
        //---
        if(response != null && !response.isEmpty()) {
           // System.out.println(response);
            JSONObject json_obj = new JSONObject(response);
            return json_obj;
        } else {
            return new JSONObject();
        }
    }
  
  public JSONObject getTradeInfoByLoginid(String loginid) {
      
      String request = "/api/user/account/get?login="+loginid ;
      String response = "";
      try {
          response = sendGet(request);
      } catch(Exception ex) {
          System.out.println(ex.toString());
      }
      //---
      if(response != null && !response.isEmpty()) {
         // System.out.println(response);
          JSONObject json_obj = new JSONObject(response);
          return json_obj;
      } else {
          return new JSONObject();
      }
  }
    
    
    public JSONObject balanceCharge(String loginid,String type,String balance,String comment) {
        
        String request = "/api/trade/balance?login="+loginid+"&type="+type+"&balance="+balance+"&comment="+comment ;
        String response = "";
        try {
            response = sendGet(request);
        } catch(Exception ex) {
            System.out.println(ex.toString());
        }
        //---
        if(response != null && !response.isEmpty()) {
            System.out.println(response);
            JSONObject json_obj = new JSONObject(response);
            return json_obj;
        } else {
            return new JSONObject();
        }
    }
    
    
      public JSONObject getProds() {
        
        String request = "/symbol_list" ;
        String response = "";
        try {
            response = sendGet(request);
        } catch(Exception ex) {
            System.out.println(ex.toString());
        }
        if(response != null && !response.isEmpty()) {
            //System.out.println(response);
            JSONObject json_obj = new JSONObject(response);
            return json_obj;
        } else {
            return new JSONObject();
        }
    }
      
      public JSONObject getGroupTotal() {
          
          String request = "/api/group/total" ;
          String response = "";
          try {
              response = sendGet(request);
          } catch(Exception ex) {
              System.out.println(ex.toString());
          }
          //---
          if(response != null && !response.isEmpty()) {
             // System.out.println(response);
              JSONObject json_obj = new JSONObject(response);
              return json_obj;
          } else {
              return new JSONObject();
          }
      }
      
      public JSONObject getGroupInfoByIndex(int i) {
          
          String request = "/api/group/next?index="+i ;
          String response = "";
          try {
              response = sendGet(request);
          } catch(Exception ex) {
              System.out.println(ex.toString());
          }
          //---
          if(response != null && !response.isEmpty()) {
             // System.out.println(response);
              JSONObject json_obj = new JSONObject(response);
              return json_obj;
          } else {
              return  new JSONObject();
          }
      }

       public JSONObject addNewLogin(String login,String pwd,String pwd2,String group,String name,String leverage,String email,String country) {
          
          String request = "/api/user/add?login="+login+"&pass_main="+pwd+"&pass_investor="+pwd2+"&group="+group+"&name="+name+"&leverage="+leverage+"&email="+email+"&country="+country ;
          String response = "";
          try {
              response = sendGet(request);
          } catch(Exception ex) {
              System.out.println(ex.toString());
          }
          //---
          if(response != null && !response.isEmpty()) {
           //   System.out.println(response);
              JSONObject json_obj = new JSONObject(response);
              return json_obj;
          } else {
              return new JSONObject();
          }
      }
       
       public JSONObject updateLogin(String login,String group,String leverage) {
           
           String request = "/api/user/update?login="+login+"&group="+group+"&leverage="+leverage ;
           String response = "";
           try {
               response = sendGet(request);
           } catch(Exception ex) {
               System.out.println(ex.toString());
           }
           //---
           if(response != null && !response.isEmpty()) {
            //   System.out.println(response);
               JSONObject json_obj = new JSONObject(response);
               return json_obj;
           } else {
               return new JSONObject();
           }
       }
       
       public JSONObject updateLoginLeverage(String login,String leverage) {
           
           String request = "/api/user/update?login="+login+"&leverage="+leverage ;
           String response = "";
           try {
               response = sendGet(request);
           } catch(Exception ex) {
               System.out.println(ex.toString());
           }
           //---
           if(response != null && !response.isEmpty()) {
              // System.out.println(response);
               JSONObject json_obj = new JSONObject(response);
               return json_obj;
           } else {
               return new JSONObject();
           }
       }
       
       public JSONObject updateLoginPwd(String login,String pwd) {
           
           String request = "/api/user/change_password?login="+login+"&type=main&password="+pwd ;
           String response = "";
           try {
               response = sendGet(request);
           } catch(Exception ex) {
               System.out.println(ex.toString());
           }
           //---
           if(response != null && !response.isEmpty()) {
             //  System.out.println(response);
               JSONObject json_obj = new JSONObject(response);
               return json_obj;
           } else {
               return new JSONObject();
           }
       }
  
    public String sendGet(String path) {
        String url = server + path;
        Request request = new Request.Builder().url(url).build();
        //---
        return newCall(request);
    }

   
    public String sendPost(String path, String json) {
        String url = server + path;
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder().url(url).post(requestBody).build();
        return newCall(request);
    }

   
    private String newCall(Request request) {
        try(Response response = client.newCall(request).execute()) {
            if(response.code() != 200) return null;
            else {
                if(response.body() != null)
                    return response.body().string();
                else
                    return null;
            }
        } catch(IOException e) {
            System.out.println("client.newCall failed"); //e.printStackTrace();
            return null;
        }
    }

   
    private byte[] getRandomHex() {
        byte[] base_byte = new byte[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15};
        // ---
        SecureRandom random = new SecureRandom();
        random.nextBytes(base_byte);
        // ---
        return base_byte;
    }

   
    private void printByteResult(byte[] array_to_print) {
        if(array_to_print != null) {
            StringBuilder sb_unsigned_bytes = new StringBuilder();
            for(byte b: array_to_print) sb_unsigned_bytes.append((b & 0xff));
        } else {
            System.out.println("array_to_print is null");
        }
    }

   
    private String getHEXResult(byte[] array_to_print) {
        if(array_to_print != null) {
            StringBuilder sb = new StringBuilder();
            for(byte b: array_to_print) sb.append(String.format("%02X", b));
            return (sb.toString());
        } else {
            System.out.println("array_to_print is null");
            return null;
        }
    }

   
    private String getHashFromPassword(String password, byte[] randCode) {
        if(password.isEmpty() || randCode == null) return null;
        // ---
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] password_bytes_unicode = md.digest(password.getBytes(StandardCharsets.UTF_16LE));
            printByteResult(password_bytes_unicode);
            String api_word_str = "WebAPI";
            byte[] apiWord = api_word_str.getBytes(StandardCharsets.UTF_8);
            // ---
            byte[] hashContains = copyBuffer(password_bytes_unicode, apiWord);
            printByteResult(hashContains);
            byte[] final_hash = md.digest(hashContains);
            printByteResult(final_hash);
            byte[] final_hash_plus_rand = copyBuffer(final_hash, randCode);
            byte[] final_hash_plus_rand_md5 = md.digest(final_hash_plus_rand);
            return getHEXResult(final_hash_plus_rand_md5);
        } catch(Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

   
    private static final TrustManager[] trustAllCerts =
            new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(
                                java.security.cert.X509Certificate[] chain, String authType)
                                throws CertificateException {
                        }

                        @Override
                        public void checkServerTrusted(
                                java.security.cert.X509Certificate[] chain, String authType)
                                throws CertificateException {
                        }

                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[]{};
                        }
                    }
            };
    private static final SSLContext trustAllSslContext;

    static {
        try {
            trustAllSslContext = SSLContext.getInstance("SSL");
            trustAllSslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        } catch(NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException(e);
        }
    }

    private static final SSLSocketFactory trustAllSslSocketFactory = trustAllSslContext.getSocketFactory();

    public static OkHttpClient trustAllSslClient(OkHttpClient client) {
      
        OkHttpClient.Builder builder = client.newBuilder();
        builder.sslSocketFactory(trustAllSslSocketFactory, (X509TrustManager) trustAllCerts[0]);
        builder.hostnameVerifier(
                new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                });
        return builder.build();
    }
}


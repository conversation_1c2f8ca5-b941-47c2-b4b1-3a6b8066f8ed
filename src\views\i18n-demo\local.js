
export default {
  zh: {
    i18nView: {
      title: '切换语言',
      note: '本项目国际化基于 vue-i18n',
      datePlaceholder: '请选择日期',
      selectPlaceholder: '请选择',
      tableDate: '日期',
      tableName: '姓名',
      tableAddress: '地址',
      default: '默认按钮',
      primary: '主要按钮',
      success: '成功按钮',
      info: '信息按钮',
      warning: '警告按钮',
      danger: '危险按钮',
      one: '一',
      two: '二',
      three: '三'
    }
  },
  en: {
    i18nView: {
      title: 'Switch Language',
      note: 'The internationalization of this project is based on vue-i18n',
      datePlaceholder: 'Pick a day',
      selectPlaceholder: 'Select',
      tableDate: 'tableDate',
      tableName: 'tableName',
      tableAddress: 'tableAddress',
      default: 'default:',
      primary: 'primary',
      success: 'success',
      info: 'info',
      warning: 'warning',
      danger: 'danger',
      one: 'One',
      two: 'Two',
      three: 'Three'
    }
  },
  es: {
    i18nView: {
      title: 'Switch Language',
      note: 'The internationalization of this project is based on vue-i18n',
      datePlaceholder: 'Pick a day',
      selectPlaceholder: 'Select',
      tableDate: 'tableDate',
      tableName: 'tableName',
      tableAddress: 'tableAddress',
      default: 'default:',
      primary: 'primary',
      success: 'success',
      info: 'info',
      warning: 'warning',
      danger: 'danger',
      one: 'One',
      two: 'Two',
      three: 'Three'
    }
  }
}

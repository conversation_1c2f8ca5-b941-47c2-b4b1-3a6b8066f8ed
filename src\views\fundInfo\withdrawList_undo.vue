<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.crmAccount" :placeholder="$t('withdrawalList.label3')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tradeId" :placeholder="$t('withdrawalList.label4')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label1')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label2')" prop="gmtModified" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtModified | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('withdrawalList.label3')" prop="crmAccount" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label4')" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label5')" prop="amount" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label10')" prop="backup3" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup3==2?'Fail':scope.row.backup3==1?'Success':'' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label11')" prop="backup4" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup4==2?'Fail':scope.row.backup4==1?'Success':'' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label12')" prop="currencyType" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.currencyType==2?'Fail':scope.row.currencyType==1?'Success':'unexecuted' }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('withdrawalList.label6')" prop="auditStatus" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditStatus==0?'Pending':scope.row.auditStatus==1?'Success':'reject' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label13')" prop="operStatus" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.operStatus==0?'Pending':scope.row.operStatus==1?'Success':'Failed' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label8')" prop="backup5" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup5 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalList.label9')" prop="orderId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.orderId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="cxzx(scope.row)">{{ $t('qt.qt1') }}</el-button>
          <el-button size="small" type="danger" @click="qxzx(scope.row)">{{ $t('qt.qt2') }}</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchWithdrawList, fetchFundInfo, createFundInfo, updateFundInfo, updateIsAvailable, removeFundInfo, exportWithdrawExcel, qxzxs, cxzxs } from '@/api/fundInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'
export default {
  name: 'FundInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        userName: undefined,
        crmAccount: undefined,
        tradeId: undefined,
        depositBankId: undefined,
        withdrawBankId: undefined,
        auditStatus: undefined,
        operStatus: 99,
        auditId: undefined,
        orderId: undefined,
        bankName: undefined,
        bankNum: undefined,
        accountName: undefined,
        mobile: undefined,
        bankAddress: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      },
      dqryType: undefined,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        userName: [
        ],
        crmAccount: [
        ],
        tradeId: [
        ],
        amount: [
        ],
        depositBankId: [
        ],
        withdrawBankId: [
        ],
        type: [
        ],
        allocationAmount: [
        ],
        auditStatus: [
        ],
        operStatus: [
        ],
        auditId: [
        ],
        orderId: [
        ],
        bankName: [
        ],
        bankNum: [
        ],
        accountName: [
        ],
        mobile: [
        ],
        bankAddress: [
        ],
        remark: [
        ],
        annex: [
        ],
        fee: [
        ],
        actualAmount: [
        ],
        originalAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ],
        rate: [
        ],
        rateRmb: [
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })

    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchWithdrawList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      }
    }, handleExport() {
					 exportWithdrawExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeFundInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }, cxzx(row) {
      this.$confirm('Are you sure you want to re execute?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await cxzxs(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'execute success',
                type: 'success',
                duration: 2000
              })
						    this.getList()
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }, qxzx(row) {
      this.$confirm('Are you sure you want to cancel the execution?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await qxzxs(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'cancel success',
                type: 'success',
                duration: 2000
              })
						    this.getList()
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

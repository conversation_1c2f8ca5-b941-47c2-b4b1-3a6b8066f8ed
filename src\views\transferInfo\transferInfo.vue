<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.transferOut" :placeholder="$t('transferInfo.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.transferIn" :placeholder="$t('transferInfo.label3')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button v-waves class="filter-item" type="primary" @click="handleExport">导出报表</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('transferInfo.label1')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label8')" prop="gmtModified" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtModified | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label2')" prop="transferOut" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferOut }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label3')" prop="transferIn" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferIn }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label4')" prop="fullname" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.fullname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label5')" prop="userName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label6')" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label7')" prop="transferAmount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfo.label9')" prop="auditStatus" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditStatus==0?'Audit':scope.row.auditStatus==1?'Success':'Reject' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import {exportExcel, fetchList, fetchTransferInfo, createTransferInfo, updateTransferInfo, updateIsAvailable, removeTransferInfo } from '@/api/transferInfo'
import { parseTime } from '@/utils'
import Setting from '@/settings'
import Pagination from '@/components/Pagination'
export default {
  name: 'TransferInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        transferOut: undefined,
        transferIn: undefined,
        outStatus: undefined,
        inStatus: undefined,
        transferStatus: undefined,
        auditStatus: undefined,
        outOrderId: undefined,
        inOrderId: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        transferOut: '',
        transferIn: '',
        transferAmount: '',
        outStatus: '',
        inStatus: '',
        transferStatus: '',
        auditStatus: '',
        outOrderId: '',
        inOrderId: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        transferOut: [
        ],
        transferIn: [
        ],
        transferAmount: [
        ],
        outStatus: [
        ],
        inStatus: [
        ],
        transferStatus: [
        ],
        auditStatus: [
        ],
        remark: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        outOrderId: [
        ],
        inOrderId: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleExport() {
					 exportExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        transferOut: '',
        transferIn: '',
        transferAmount: '',
        outStatus: '',
        inStatus: '',
        transferStatus: '',
        auditStatus: '',
        outOrderId: '',
        inOrderId: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTransferInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateTransferInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeTransferInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

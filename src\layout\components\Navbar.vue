<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <svg-icon icon-class="user" />
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span style="display:block;" @click="changeInfo()">  {{ $t('navbar.userInfo') }}</span>
          </el-dropdown-item>
          <el-dropdown-item>
            <span style="display:block;" @click="changePass()"> {{ $t('navbar.changePass') }}</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span style="display:block;" @click="logout"> {{ $t('navbar.logOut') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!--修改个人信息弹窗-->
      <el-dialog :title="$t('navbar.userInfo')" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false" class="customWidth">
        <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 550px; margin-left:10px;">
          <el-form-item :label="$t('navbar.userName')" prop="username">
            <span style="display:block;">{{ temp.username }}</span>
          </el-form-item>
          <el-form-item :label="$t('navbar.nickName')" prop="nickName">
            <el-input v-model="temp.nickName" :placeholder="$t('navbar.nickName')" />
          </el-form-item>
          <el-form-item :label="$t('navbar.mobile')" prop="mobile">
            <el-input v-model="temp.mobile" :placeholder="$t('navbar.mobile')" />
          </el-form-item>
          <el-form-item v-if="(temp.roleType !=0&&temp.roleType !=999)?true:false" :label="$t('navbar.invitationLink')" prop="saleSerial">
            <span style="display:inline-block;width:350px;">{{ tradeUrl }}/trade/register?tj={{ temp.saleSerial }}</span>
            <span style="display:inline-block;width:100px;"> <el-button
              v-clipboard:copy="copymessage"
              v-clipboard:success="onCopy"
              v-clipboard:error="onError"
              class="filter-item"
              type="button"
              icon="el-icon-document-copy"
              inline="true"
            /></span>
          </el-form-item>
          <el-form-item v-if="(temp.roleType !=0&&temp.roleType !=999)?true:false" :label="$t('navbar.qrcode')" prop="saleSerial">
            <div ref="qrCodeUrl" class="qrcode" />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormEditVisible = false">
            {{ $t('table.cancel') }}
          </el-button>
          <el-button type="primary" @click="updateData()">
            {{ $t('table.confirm') }}
          </el-button>
        </div>
      </el-dialog>
      <!--重置密码弹窗-->
      <el-dialog :title="$t('navbar.changePass')" :visible.sync="dialogFormResetPasswordVisible" :close-on-click-modal="false">
        <el-form ref="dataFormOfPass" :rules="passRules" :model="pass" label-position="right" label-width="140px" style="max-width: 400px; margin-left:10px;">
          <el-form-item :label="$t('navbar.password')" prop="password">
            <el-input v-model="pass.password" type="password" :placeholder="$t('navbar.password')" />
          </el-form-item>
          <el-form-item :label="$t('navbar.newPassword')" prop="newPassword">
            <el-input v-model="pass.newPassword" type="password" :placeholder="$t('navbar.newPassword')" />
          </el-form-item>
          <el-form-item :label="$t('navbar.againPassword')" prop="againPassword">
            <el-input v-model="pass.againPassword" type="password" :placeholder="$t('navbar.againPassword')" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormResetPasswordVisible = false">
            {{ $t('table.cancel') }}
          </el-button>
          <el-button type="primary" @click="resetPassword()">
            {{ $t('table.confirm') }}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import Setting from '@/settings'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import ErrorLog from '@/components/ErrorLog'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import LangSelect from '@/components/LangSelect'
import Search from '@/components/HeaderSearch'
import QRCode from 'qrcodejs2'
import Vue from 'vue'
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)
import { getInfo, updateUser, changePassword } from '@/api/navbar'
export default {
  components: {
    Breadcrumb,
    Hamburger,
    ErrorLog,
    Screenfull,
    SizeSelect,
    LangSelect,
    Search,
    QRCode
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'name',
      'avatar',
      'device'
    ])
  },
  data() {
    return {
      copymessage: '',
			 temp: {
			  userId: undefined,
			  username: '',
			  nickName: '',
			  mobile: '',
        password: '',
			  isAvailable: '',
        userIcon: '',
        saleSerial: '',
        isShow: 0
      },
      codes: '',
      tradeUrl: '',
      pass: {
        userId: undefined,
        password: '',
        newPassword: '',
        againPassword: ''
      },
      dialogFormEditVisible: false, // 用户编辑信息弹窗
      dialogFormResetPasswordVisible: false, // 用户修改密码弹窗
			 rules: {// 验证消息
			  nickName: [

        ],
			  mobile: [

        ]
      },
      passRules: {// 验证消息
			  password: [
          { required: true, message: 'Please enter the password', trigger: 'change' },
          { min: 6, max: 30, message: 'Please enter 6 to 20 characters', trigger: 'change' }
        ],
			  newPassword: [
          { required: true, message: 'Please enter the new password', trigger: 'change' },
          { min: 6, max: 30, message: 'Please enter 6 to 20 characters', trigger: 'change' }
        ],
        againPassword: [
          { required: true, message: 'Please enter the new password again', trigger: 'change' },
          { min: 6, max: 30, message: 'Please enter 6 to 20 characters', trigger: 'change' },
          { validator: (rule, value, callback) => {
            if (value !== this.pass.newPassword) {
              callback(new Error('The two password inputs are inconsistent'))
            } else {
              callback()
            }
          }, trigger: 'change'
          }
        ]
      }
    }
  },
	 created() {
    this.tradeUrl = Setting.trade_url
    this.temp.isShow = 0
	  getInfo().then(response => {
	  	console.log('userInfo:' + response)
	  	if (response.code == 20000) {
	  		this.temp.userId = response.data.userId
	  		this.temp.nickName = response.data.nickName
	  		this.temp.mobile = response.data.mobile
	  		this.temp.userIcon = response.data.userIcon
	  		this.temp.username = response.data.username
        this.temp.saleSerial = response.data.saleSerial
        this.temp.roleType = response.data.roleType
        this.copymessage = this.tradeUrl + '/trade/register?tj=' + response.data.saleSerial
	  	} else {
	  		 this.$message({
	  		  message: response.msg,
	  		  type: 'error'
	  		})
	  	}
	  })
  },
  methods: {
    onCopy: function(e) {
      this.$notify({
        title: 'success',
        message: 'copy success',
        type: 'success',
        duration: 1000
      })
    },
    onError: function(e) {
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
			 this.$confirm('Are you sure you want to log out?', 'INFO', {
						  confirmButtonText: 'OK',
						  cancelButtonText: 'CANCEL',
						  type: 'warning'
      })
						 .then(async() => {
						   await this.$store.dispatch('user/logout')
						    this.$router.push(`/login`)
        })
    },
    changePass(id) {
      this.dialogFormResetPasswordVisible = true
    },
    changeInfo() {
      this.dialogFormEditVisible = true
      this.$nextTick(() => { this.useqrcode() })
    },
    // 更新数据
    updateData() {
					  this.$refs['dataForm'].validate((valid) => {
					    if (valid) {
					      const tempData = Object.assign({}, this.temp)

					      tempData.timestamp = +new Date(tempData.timestamp) // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
					      updateUser(tempData).then(res => {
            if (res.code == 20000) {
										  this.$notify({
										  title: 'success',
										  message: 'update success',
										  type: 'success',
										  duration: 1000
              })
										 this.dialogFormEditVisible = false
            } else {
              this.$message.error(res.msg)
            }
					      })
					    }
					  })
    },
    resetPassword() {
						 this.$refs['dataFormOfPass'].validate((valid) => {
						  if (valid) {
						    const tempData = Object.assign({}, this.pass)

						    tempData.timestamp = +new Date(tempData.timestamp) // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
						    changePassword(tempData).then(res => {
            if (res.code == 20000) {
										  this.$notify({
										  title: 'success',
										  message: 'Password modification successful, please log in again!',
										  type: 'success',
										  duration: 2000,
                onClose: async() => {
												 await this.$store.dispatch('user/logout')
												 this.$router.push(`/login?redirect=${this.$route.fullPath}`)
                }
              })
										 this.dialogFormResetPasswordVisible = false
            } else {
              this.$message.error(res.msg)
            }
						    })
						  }
      })
    }, useqrcode() {
      if (this.temp.isShow == 0) {
        var qrcode = new QRCode(this.$refs.qrCodeUrl, {
          text: this.tradeUrl + '/trade/register?tj=' + this.temp.saleSerial,
          width: 100,
          height: 100,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        })
        this.temp.isShow = 1
      } else {

      }
    }

  }
}
</script>

<style lang="scss" scoped>

 .customWidth{
        width:73%;
        margin-left:200px;
    }
  .qrcode{
      display: inline-block;
      img {
          width: 132px;
          height: 132px;
          background-color: #fff; //设置白色背景色
          padding: 6px; // 利用padding的特性，挤出白边
          box-sizing: border-box;
      }
  }

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

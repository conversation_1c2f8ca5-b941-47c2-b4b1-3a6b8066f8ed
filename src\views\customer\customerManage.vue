<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.customerCode" placeholder="客户编号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.customerName" placeholder="客户名称" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.customerParent" placeholder="上级客户" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="30px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户编号" prop="customerCode" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.customerCode }}</span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" prop="customerName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.customerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上级客户" prop="customerParent" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.customerParent }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电话" prop="tel" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地址" prop="address" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.address }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" prop="createTime" sortable="custom" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系人" prop="linkerParent" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.linkerParent }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="220" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button type="primary" size="mini" @click="handleUpdate2(scope.row)">联系人</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">Disable</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false" width="50%" style=" margin-left:10px;" top="15vh;">
      <el-form ref="dataForm" :rules="rules" :model="temp" :inline="true" label-position="right" label-width="100px" size="mini" style="min-width: 1000px; margin-left:10px;">

        <el-form-item label="客户编号" prop="customerCode">

          <el-input v-model="temp.customerCode" placeholder="自动生成" readonly="" />

        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="temp.customerName" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="上级客户" prop="customerParent">
          <el-select v-model="temp.customerParent" :placeholder="$t('userTable.placeholder3')" clearable filterable remote reserve-keyword :remote-method="getCustomer" style="width:500px;">
            <el-option
              v-for="customerId in customerParents"
              :key="customerId.value"
              :label="customerId.label"
              :value="customerId.value"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="联系人" prop="linkerParent">
          <el-input v-model="temp.linkerParent" />
        </el-form-item>
        <el-form-item label="电话" prop="tel">
          <el-input v-model="temp.tel" style="width:225px;" />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input v-model="temp.address" style="width:500px;" />
        </el-form-item>
        <br>
        <el-form-item label="对公开户行" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankNumber">
          <el-input v-model="temp.bankNumber" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="接口人" prop="liaisonOfficer">
          <el-input v-model="temp.liaisonOfficer" />
        </el-form-item>
        <el-form-item label="电话" prop="liaisonTel">
          <el-input v-model="temp.liaisonTel" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="创建人" prop="creater">
          <el-input v-model="temp.creater" />
        </el-form-item>
        <el-form-item label="所有人" prop="owner">
          <el-input v-model="temp.owner" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="创建日期" prop="createTime">
          <el-date-picker
            v-model="temp.createTime"
            type="date"
            placeholder="选择创建日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="temp.remark" style="width:225px;" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false" width="50%" style="min-width: 1000px; margin-left:10px;" top="15vh;">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" :inline="true" label-position="right" label-width="100px" size="mini" style=" margin-left:10px;">
        <el-form-item label="客户编号" prop="customerCode">
          <el-input v-model="temp.customerCode" placeholder="自动生成" readonly="" />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="temp.customerName" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="上级客户" prop="customerParent">
          <el-select v-model="temp.customerParent" :placeholder="$t('userTable.placeholder3')" clearable filterable remote reserve-keyword :remote-method="getCustomer" style="width:500px;">
            <el-option
              v-for="customerId in customerParents"
              :key="customerId.value"
              :label="customerId.label"
              :value="customerId.value"
            />
          </el-select>
        </el-form-item>
        <br>
        <el-form-item label="联系人" prop="linkerParent">
          <el-input v-model="temp.linkerParent" />
        </el-form-item>
        <el-form-item label="电话" prop="tel">
          <el-input v-model="temp.tel" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="地址" prop="address">
          <el-input v-model="temp.address" style="width:500px;" />
        </el-form-item>
        <br>
        <el-form-item label="对公开户行" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankNumber">
          <el-input v-model="temp.bankNumber" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="接口人" prop="liaisonOfficer">
          <el-input v-model="temp.liaisonOfficer" />
        </el-form-item>
        <el-form-item label="电话" prop="liaisonTel">
          <el-input v-model="temp.liaisonTel" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="创建人" prop="creater">
          <el-input v-model="temp.creater" />
        </el-form-item>
        <el-form-item label="所有人" prop="owner">
          <el-input v-model="temp.owner" style="width:225px;" />
        </el-form-item>
        <br>
        <el-form-item label="创建日期" prop="createTime">
          <el-date-picker
            v-model="temp.createTime"
            type="date"
            placeholder="选择创建日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="temp.remark" style="width:225px;" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, createCustomer, updateCustomer, updateIsAvailable, removeCustomer } from '@/api/customer'
import { parseTime } from '@/utils'
import { fetchCustomer } from '@/api/saleOrder'
import { getInfo } from '@/api/navbar'
import Pagination from '@/components/Pagination'
export default {
  name: 'CustomerTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        customerCode: undefined,
        customerName: undefined,
        customerParent: undefined
      },
      temp: {
        id: undefined,
        customerCode: '',
        customerName: '',
        customerParent: '',
        linkerParent: '',
        tel: '',
        zip: '',
        address: '',
        bankName: '',
        bankNumber: '',
        liaisonOfficer: '',
        liaisonTel: '',
        creater: '',
        owner: '',
        createTime: '',
        remark: '',
        linkMan: '',
        linkMobile: '',
        linkTitle: '',
        linkBank: '',
        linkBanknumber: '',
        linkRemark: ''
      },
      customerParents: [],
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        customerCode: [,

        ],
        customerName: [
          { required: true, message: '客户名称不能为空', trigger: 'change' },,
        ],
        customerParent: [
        ],
        linkerParent: [
        ],
        tel: [
        ],
        zip: [
        ],
        address: [
        ],
        bankName: [
        ],
        bankNumber: [
        ],
        liaisonOfficer: [
        ],
        liaisonTel: [
        ],
        creater: [
        ],
        owner: [
        ],
        createTime: [
        ],
        remark: [
        ],
        linkMan: [
        ],
        linkMobile: [
        ],
        linkTitle: [
        ],
        linkBank: [
        ],
        linkBanknumber: [
        ],
        linkRemark: [
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getCustomer('')
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    }, getCustomer(query) {
					  this.customerParents = []

						  fetchCustomer(query).then(res => {
							 this.customerParents = []
							 var data = res.data
							 	for (var j = 0; j < data.length; j++) {
							 		var id = data[j].customerName
							 		var name = data[j].customerName
							 		var per = []
							 		per.value = id
							 		per.label = name
							 		this.customerParents.push(per)
							 }
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        customerCode: '',
        customerName: '',
        customerParent: '',
        linkerParent: '',
        tel: '',
        zip: '',
        address: '',
        bankName: '',
        bankNumber: '',
        liaisonOfficer: '',
        liaisonTel: '',
        creater: '',
        owner: '',
        createTime: '',
        remark: '',
        linkMan: '',
        linkMobile: '',
        linkTitle: '',
        linkBank: '',
        linkBanknumber: '',
        linkRemark: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.createTime = parseTime(new Date(), '{y}-{m}-{d}')
      getInfo().then(response => {
        console.log('userInfo:' + response)
        if (response.code == 20000) {
          this.temp.creater = response.data.nickName
          this.temp.liaisonOfficer = response.data.nickName
          this.temp.owner = response.data.nickName
        } else {
								 this.$message({
								  message: response.msg,
								  type: 'error'
          })
        }
						  })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.createTime = parseTime(this.temp.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          createCustomer(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.getCustomer('')
              this.dialogFormAddVisible = false
            } else {
              this.$message.error('您没有添加权限')
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.createTime = parseTime(this.temp.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          updateCustomer(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error('您没有修改权限')
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    }, handleUpdate2(row) {
					  this.$router.push({ path: '/saleMange/second_linker2/' + row.id })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeCustomer(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error('您没有删除权限')
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

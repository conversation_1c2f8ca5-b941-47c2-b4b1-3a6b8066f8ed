package com.ews.system.service;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import com.ews.common.Result;
import com.ews.system.entity.Enclosure;

@Component
public interface EnclosureService {

	public static final Integer USER = 1;

	/**
	 * 分页查询方法
	 * 
	 * @param page
	 * @param size
	 * @param sortName
	 * @param sortOrder
	 * @param enclosure
	 * @return
	 */
	Page<Enclosure> findAll(Integer page, Integer size, String sortName, String sortOrder, final Enclosure enclosure);

	/**
	 * 通过Id查询
	 * 
	 * @param id
	 * @return
	 */
	Enclosure findById(Long id);

	/**
	 * 通过Id 删除实体 (逻辑删除)
	 * 
	 * @param Id
	 * @return
	 */
	Result removeEntityOfLogicalById(Long id);

	/**
	 * 通过Id 删除实体 (物理删除)
	 * 
	 * @param Id
	 * @return
	 */
	Result removeEntityById(Long id);

	/**
	 * 保存和更新方法
	 * 
	 * @param author
	 * @return
	 */
	Result saveOrUpdate(Enclosure enclosure);

}

'use strict'
const path = require('path')
const defaultSettings = require('@/settings.js')

import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: defaultSettings.api_url, // url = base url + request url
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 50000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.token) {
      // let each request carry token --['X-Token'] as a custom key.
      // please modify it according to the actual situation.
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code.
   */
  response => {
    const res = response.data

    console.log(res.code !== '20000')
    // if the custom code is not 20000, it is judged as an error.
    if (res.code != '20000') {
      console.log('验证失败 ，code：' + res.code)

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (res.code == '50008' || res.code == '50012' || res.code == '50014') {
        // to re-login

        /*
        MessageBox.message('您已经登出，请重新登录！', {

          confirmButtonText: '重新登录',
          cancelButtonText: 'CANCEL',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
			*/
				 Message({
					 message: '登录已超时，请重新登录！',
					 type: 'error',
					 duration: 2 * 1000,
					 onClose: function() {
						 /*
						 store.dispatch('user/resetToken').then(() => {
						  // location.reload()
						 })
						 */
					 }
				 })
      } else {
        Message({
				  message: res.msg || res.code,
				  type: 'error',
				  duration: 2 * 1000
        })
      }
      return Promise.reject(res.msg || 'error')
    } else {
      console.log('验证通过 code:' + res.code)
      return res
    }
  },
  error => {
    console.log('调试信息 err' + error) // for debug
    Message({
      message: error,
      type: 'error',
      duration: 2 * 1000
    })

    return Promise.reject(error)
  }
)

export default service

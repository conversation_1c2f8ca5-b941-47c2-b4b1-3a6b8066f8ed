<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.tradeId" :placeholder="$t('tradeAccount.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.userAccount" :placeholder="$t('tradeAccount.label5')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <!--el-input v-model="listQuery.groupName" placeholder="交易组别" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" /-->

      <el-select v-model="listQuery.type" style="width: 150px" :placeholder="$t('tradeAccount.label2')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label1')" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label2')" prop="backup3" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup3 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label3')" min-width="150px" align="center" prop="gmtCreate" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label14')" prop="fullname" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.fullname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label5')" prop="userAccount" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userAccount }}</span>
        </template>
      </el-table-column>
      <!--el-table-column label="交易组别" prop="groupName"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.groupName }}</span>
				</template>
			 </el-table-column-->
      <el-table-column :label="$t('tradeAccount.label6')" prop="leverShow" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>1:{{ scope.row.leverShow }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label7')" prop="balance1" min-width="150px" align="center" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.balance1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label8')" prop="balance2" min-width="150px" align="center" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.balance2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label9')" prop="balance3" min-width="150px" align="center" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.balance3 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label10')" prop="volume" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.volume }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label11')" prop="balance4" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.balance4 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccount.label12')" prop="balance5" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.balance5 }}</span>
        </template>
      </el-table-column>

    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog
      v-loading="loading"
      title="New"
      :visible.sync="dialogFormAddVisible"
      :close-on-click-modal="false"
      append-to-body=""
      element-loading-text="Creating a trading account"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="150px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('tradeAccount.label2')" prop="type">
          <el-select v-model="temp.type">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label16')" prop="groupName">
          <el-select v-model="temp.groupName">
            <el-option v-for="item in groupNameList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label15')" prop="leverShow">
          <el-select v-model="temp.leverShow">
            <el-option v-for="item in leverIdList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label13')" prop="backup6">
          <el-input v-model="temp.backup6" />
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label14')" prop="userAccount">
          <el-input v-model="temp.userAccount" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-loading="loading2"
      title="Update"
      :visible.sync="dialogFormEditVisible"

      :close-on-click-modal="false"
      element-loading-text="Updating trading accounts"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="150px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('tradeAccount.label1')" prop="tradeId">
          <el-input v-model="temp.tradeId" readonly />
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label4')" prop="userAccount">
          <el-input v-model="temp.userAccount" />
        </el-form-item>

        <el-form-item :label="$t('tradeAccount.label2')" prop="type">
          <el-select v-model="temp.type">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('tradeAccount.label16')" prop="groupName">
          <el-select v-model="temp.groupName">
            <el-option v-for="item in groupNameList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!--el-form-item label="交易组" prop="groupName">
                 <el-input v-model="temp.groupName" readonly></el-input>
        </el-form-item-->
        <el-form-item :label="$t('tradeAccount.label15')" prop="leverShow">
          <el-select v-model="temp.leverShow">
            <el-option v-for="item in leverIdList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchTradeAccount, createTradeAccount, updateTradeAccount, updateIsAvailable, removeTradeAccount, exportTradeAccountExcel } from '@/api/tradeAccount'
import { listAll } from '@/api/accountType'
import { listshow } from '@/api/userGroup'
import { fetchLeverList } from '@/api/lever'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'
export default {
  name: 'TradeAccountTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        tradeId: undefined,
        userId: undefined,
        userAccount: undefined,
        type: undefined,
        groupName: undefined,
        leverId: undefined,
        tradeStatus: undefined,
        gmtCreateSearchBegin: undefined,
        gmtCreateSearchEnd: undefined,
        gmtCreateSearchBegin2: undefined,
        gmtCreateSearchEnd2: undefined
      }, pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }

        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
					 }]				},
      temp: {
        id: undefined,
        tradeId: '',
        userId: '',
        userAccount: '',
        type: '',
        groupName: '',
        leverId: '',
        leverShow: '',
        tradeStatus: '',
        userName: '',
        fullname: '',
        email: '',
        tel: '',
        province: '',
        city: '',

        adress: '',
        password: '',
        backup6: ''
      }, dqryType: undefined, xbList: [

          					{
          						value: '1',
          						label: '男'
          					},
          					{
          						value: '2',
          						label: '女'
          					}
          					],
      gender: '',
      typeList: [],
      groupNameList: [],
      leverIdList: [],
      valueGmtCreate: undefined,
      valueGmtCreate2: undefined,
      loading: false,
      loading2: false,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        tradeId: [,

        ],
        userId: [
        ],
        userName: [
        ],
        userAccount: [,

        ],
        type: [
          { required: true, message: 'Account type cannot be empty', trigger: 'change' }
        ],
        groupName: [
        ],
        leverId: [
          { required: true, message: 'The leverage ratio cannot be empty', trigger: 'change' }
        ],
        leverShow: [
        ],
        tradeStatus: [
        ],
        balance1: [
        ],
        balance2: [
        ],
        balance3: [
        ],
        balance4: [
        ],
        balance5: [
        ],
        balance6: [
        ],
        balance7: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })
    this.getList()

    this.typeList = []
    listAll().then(response => {
      var datas1 = response.data
                	for (var j = 0; j < datas1.length; j++) {
                		var id = datas1[j].id
                		var name = datas1[j].typeName
                		var per = []
                		per.value = id
                		per.label = name
        this.typeList.push(per)
      }
    }).catch(err => {
      console.log(err)
    })
    this.groupNameList = []
    listshow().then(response => {
      var datas3 = response.data.items
                	for (var j = 0; j < datas3.length; j++) {
                		var id = datas3[j].groupName
                		var name = datas3[j].groupName
                		var per = []
                		per.value = id
                		per.label = name
        this.groupNameList.push(per)
      }
    }).catch(err => {
      console.log(err)
    })

    this.leverIdList = []
    fetchLeverList().then(response => {
      var datas1 = response.data.items
                	for (var j = 0; j < datas1.length; j++) {
                		var id = datas1[j].leverNum
                		var name = datas1[j].leverNum
                		var per = []
                		per.value = id
                		per.label = name
        this.leverIdList.push(per)
      }
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      if (this.valueGmtCreate) {
        this.listQuery.gmtCreateSearchBegin = parseTime(new Date(this.valueGmtCreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
        this.listQuery.gmtCreateSearchEnd = parseTime(new Date(this.valueGmtCreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        this.listQuery.gmtCreateSearchBegin = undefined
        this.listQuery.gmtCreateSearchEnd = undefined
      }
      if (this.valueGmtCreate2) {
        this.listQuery.gmtCreateSearchBegin2 = parseTime(new Date(this.valueGmtCreate2[0]), '{y}-{m}-{d} {h}:{i}:{s}')
        this.listQuery.gmtCreateSearchEnd2 = parseTime(new Date(this.valueGmtCreate2[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        this.listQuery.gmtCreateSearchBegin2 = undefined
        this.listQuery.gmtCreateSearchEnd2 = undefined
      }

      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
      if (prop === 'balance1') {
           						this.sortBybalance1(order)
      }
      if (prop === 'balance2') {
           						this.sortBybalance2(order)
      }
      if (prop === 'balance3') {
           						this.sortBybalance3(order)
      }
    }, handleExport() {
					 exportTradeAccountExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    sortBybalance1(order) {
        	if (order === 'ascending') {
        		this.listQuery.sort = '+balance1'
        	} else {
        		this.listQuery.sort = '-balance1'
        	}
        	this.handleFilter()
    },
    sortBybalance2(order) {
        	if (order === 'ascending') {
        		this.listQuery.sort = '+balance2'
        	} else {
        		this.listQuery.sort = '-balance2'
        	}
        	this.handleFilter()
    },
    sortBybalance3(order) {
        	if (order === 'ascending') {
        		this.listQuery.sort = '+balance3'
        	} else {
        		this.listQuery.sort = '-balance3'
        	}
        	this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        tradeId: '',
        userId: '',
        userAccount: '',
        type: '',
        groupName: '',
        leverId: '',
        leverShow: '',
        tradeStatus: '',
        backup6: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.backup6 = 'Aa123456!'
      this.dialogFormAddVisible = true

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loading = true
          createTradeAccount(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.loading = false
              this.dialogFormAddVisible = false
            } else {
              this.loading = false
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.loading2 = true
          updateTradeAccount(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.loading2 = false
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.loading2 = false
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.leverShow = row.leverShow
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this transaction account?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeTradeAccount(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.repository.FundInfoRepository;
import com.ews.crm.service.FundInfoService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class FundInfoServiceImpl implements FundInfoService 
{
	@Autowired
	private FundInfoRepository fundInfoRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<FundInfo> findAll(Integer page, Integer size,String sortName,String sortOrder, FundInfo fundInfo) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<FundInfo> pages = fundInfoRepository.findAll(new Specification<FundInfo>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<FundInfo> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(fundInfo.getUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("userId").as(Long.class), fundInfo.getUserId()));
             }
             if(!StringUtils.isEmpty(fundInfo.getUserName())) { 
                predicates.add(criteriaBuilder.like(root.get("userName").as(String.class),"%"+fundInfo.getUserName()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getCrmAccount())) { 
                predicates.add(criteriaBuilder.like(root.get("crmAccount").as(String.class),"%"+fundInfo.getCrmAccount()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getTradeId())) { 
                predicates.add(criteriaBuilder.equal(root.get("tradeId").as(String.class),fundInfo.getTradeId()));
             }
             if(!StringUtils.isEmpty(fundInfo.getDepositBankId())) { 
                predicates.add(criteriaBuilder.equal(root.get("depositBankId").as(Long.class), fundInfo.getDepositBankId()));
             }
             if(!StringUtils.isEmpty(fundInfo.getWithdrawBankId())) { 
                predicates.add(criteriaBuilder.equal(root.get("withdrawBankId").as(Long.class), fundInfo.getWithdrawBankId()));
             }
             if(!StringUtils.isEmpty(fundInfo.getAuditStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("auditStatus").as(Integer.class), fundInfo.getAuditStatus()));
             }
             
             if(!StringUtils.isEmpty(fundInfo.getBackup3())) { 
                 predicates.add(criteriaBuilder.equal(root.get("backup3").as(Integer.class), fundInfo.getBackup3()));
              }
             
             if(!StringUtils.isEmpty(fundInfo.getBackup4())) { 
                 predicates.add(criteriaBuilder.equal(root.get("backup4").as(Integer.class), fundInfo.getBackup4()));
              }
             
             if(!StringUtils.isEmpty(fundInfo.getCurrencyType())) { 
                 predicates.add(criteriaBuilder.equal(root.get("currencyType").as(Integer.class), fundInfo.getCurrencyType()));
              }
             if(!StringUtils.isEmpty(fundInfo.getType())) { 
                 predicates.add(criteriaBuilder.equal(root.get("type").as(Integer.class), fundInfo.getType()));
              }
             if(!StringUtils.isEmpty(fundInfo.getOperStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("operStatus").as(Integer.class), fundInfo.getOperStatus()));
             }
             if(!StringUtils.isEmpty(fundInfo.getAuditId())) { 
                predicates.add(criteriaBuilder.equal(root.get("auditId").as(Long.class), fundInfo.getAuditId()));
             }
             if(!StringUtils.isEmpty(fundInfo.getOrderId())) { 
                predicates.add(criteriaBuilder.like(root.get("orderId").as(String.class),"%"+fundInfo.getOrderId()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getBankName())) { 
                predicates.add(criteriaBuilder.like(root.get("bankName").as(String.class),"%"+fundInfo.getBankName()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getBankNum())) { 
                predicates.add(criteriaBuilder.like(root.get("bankNum").as(String.class),"%"+fundInfo.getBankNum()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getAccountName())) { 
                predicates.add(criteriaBuilder.like(root.get("accountName").as(String.class),"%"+fundInfo.getAccountName()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getMobile())) { 
                predicates.add(criteriaBuilder.like(root.get("mobile").as(String.class),"%"+fundInfo.getMobile()+"%"));
             }
             if(!StringUtils.isEmpty(fundInfo.getBankAddress())) { 
                predicates.add(criteriaBuilder.like(root.get("bankAddress").as(String.class),"%"+fundInfo.getBankAddress()+"%"));
             }
             
             if(!StringUtils.isEmpty(fundInfo.getIsAvailable())) { 
                 predicates.add(criteriaBuilder.equal(root.get("isAvailable").as(Integer.class),fundInfo.getIsAvailable()));
              }
             
             if(fundInfo.getUserInfoList()!=null&&fundInfo.getUserInfoList().size()>0)
             {
            	    Path<Object> path = root.get("userId");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					for(int i=0;i<fundInfo.getUserInfoList().size();i++) {
						  in.value(new Long(fundInfo.getUserInfoList().get(i).toString()));
					}
					predicates.add(criteriaBuilder.and(in));
             }
             
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public FundInfo findById(Long id) {
      Optional<FundInfo> op = fundInfoRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.fundInfoRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         FundInfo old = fundInfoRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 fundInfoRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 fundInfoRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.fundInfoRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(FundInfo fundInfo) {
      
        Result vr = new Result();
		try {
        	if (fundInfo.getId()== null) {
            	fundInfo.setGmtCreate(new Date());
            	fundInfo.setGmtModified(new Date());
            	fundInfo.setIsDeleted(0);
            	if(fundInfo.getIsAvailable() == null) {
            		fundInfo.setIsAvailable(1);
            	}
            
	    	} else {
            	fundInfo.setGmtModified(new Date());
        	}
            fundInfoRepository.save(fundInfo);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



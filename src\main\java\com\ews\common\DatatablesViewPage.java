package com.ews.common;

import java.util.List;

public class DatatablesViewPage<T> {
	private List<T> dataSrcData; // 与datatales 加载的“dataSrc"对应
	private long iTotalDisplayRecords;
	private long iTotalRecords;
	private String draw;

	public DatatablesViewPage() {

	}

	public List<T> getDataSrcData() {
		return dataSrcData;
	}

	public void setDataSrcData(List<T> dataSrcData) {
		this.dataSrcData = dataSrcData;
	}

	public long getiTotalDisplayRecords() {
		return iTotalDisplayRecords;
	}

	public void setiTotalDisplayRecords(long iTotalDisplayRecords) {
		this.iTotalDisplayRecords = iTotalDisplayRecords;
	}

	public long getiTotalRecords() {
		return iTotalRecords;
	}

	public void setiTotalRecords(long iTotalRecords) {
		this.iTotalRecords = iTotalRecords;
	}

	public String getDraw() {
		return draw;
	}

	public void setDraw(String draw) {
		this.draw = draw;
	}

	
	

}

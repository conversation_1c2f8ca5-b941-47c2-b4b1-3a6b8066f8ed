package com.ews.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "constant")
public class ConstantConfig {
	
	private String fileStoreUrl;

	private String fileStoreUrlPrefix;
	
	
	private String databaseStoreUrl;
	private String mysqldumpUrl;
	private String mysqlstoreUrl;
	private String mysqluser;
	private String mysqlpwd;
	private String mysqldbname;
	

	//上传服务器域名或ip
	private String uploadServiceDomainName;

	//上传请求action 路径
	private String uploadServiceAction;
	public String getFileStoreUrl() {
		return fileStoreUrl;
	}

	public void setFileStoreUrl(String fileStoreUrl) {
		this.fileStoreUrl = fileStoreUrl;
	}

	public String getFileStoreUrlPrefix() {
		return fileStoreUrlPrefix;
	}

	public void setFileStoreUrlPrefix(String fileStoreUrlPrefix) {
		this.fileStoreUrlPrefix = fileStoreUrlPrefix;
	}

	public String getDatabaseStoreUrl() {
		return databaseStoreUrl;
	}

	public void setDatabaseStoreUrl(String databaseStoreUrl) {
		this.databaseStoreUrl = databaseStoreUrl;
	}

	public String getMysqldumpUrl() {
		return mysqldumpUrl;
	}

	public void setMysqldumpUrl(String mysqldumpUrl) {
		this.mysqldumpUrl = mysqldumpUrl;
	}

	public String getMysqlstoreUrl() {
		return mysqlstoreUrl;
	}

	public void setMysqlstoreUrl(String mysqlstoreUrl) {
		this.mysqlstoreUrl = mysqlstoreUrl;
	}

	public String getMysqluser() {
		return mysqluser;
	}

	public void setMysqluser(String mysqluser) {
		this.mysqluser = mysqluser;
	}

	public String getMysqlpwd() {
		return mysqlpwd;
	}

	public void setMysqlpwd(String mysqlpwd) {
		this.mysqlpwd = mysqlpwd;
	}

	public String getMysqldbname() {
		return mysqldbname;
	}

	public void setMysqldbname(String mysqldbname) {
		this.mysqldbname = mysqldbname;
	}

	public String getUploadServiceDomainName() {
		return uploadServiceDomainName;
	}

	public void setUploadServiceDomainName(String uploadServiceDomainName) {
		this.uploadServiceDomainName = uploadServiceDomainName;
	}

	public String getUploadServiceAction() {
		return uploadServiceAction;
	}

	public void setUploadServiceAction(String uploadServiceAction) {
		this.uploadServiceAction = uploadServiceAction;
	}
	
	

}

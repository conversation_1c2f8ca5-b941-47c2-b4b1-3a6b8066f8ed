package com.ews;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

public class Test {
	
	
	public static void main(String[] args) {
	    try {
			   StringBuffer sb=new StringBuffer("first_name=TestFN&last_name=TestLN&email=<EMAIL>&postal_code=AFG&city=Z&address=Country23456&notification_url=https://seventy2u.seventybrokers.com/trade/payResult&login_id=8000581&currency=THB&amount=0.01&serial_number=WITHDRAW20230626_87_6686_1687786259878&bank_name=aa&bank_courty=BH&bank_swift=cc&bank_account=bb&sign=4220535a122e2b5dc8878feeb0f6a8a8");
	    } catch (Exception e) {
	    	e.printStackTrace();
	    }
	}
	public static String postByFormData(String url,StringBuffer sb){
	    String responseMessage = "";
	    StringBuffer response = new StringBuffer();
	    HttpURLConnection httpConnection = null;
	    OutputStreamWriter out = null;
	    BufferedReader reader = null;
	    try {
	        URL urlPost = new URL(url);
	        httpConnection = (HttpURLConnection) urlPost.openConnection();
	        httpConnection.setDoOutput(true);
	        httpConnection.setDoInput(true);
	        // 参数长度太大，不能用get方式
	        httpConnection.setRequestMethod("POST");
	        // 不使用缓存
	        httpConnection.setUseCaches(false);
	        // URLConnection.setInstanceFollowRedirects是成员函数，仅作用于当前函数
	        httpConnection.setInstanceFollowRedirects(true);
	        // 配置本次连接的Content-type，配置为application/x-www-form-urlencoded的
	        // 意思是正文是urlencoded编码过的form参数
	       // httpConnection.setRequestProperty("Connection", "Keep-Alive");
	        // 设置请求头信息
	        httpConnection.setRequestProperty("Charset", "UTF-8");
	        httpConnection.addRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)");  // 设置边界
	        String BOUNDARY = "----------" + System.currentTimeMillis();
	        httpConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
	        httpConnection.connect();
	        out = new OutputStreamWriter(httpConnection.getOutputStream(),"UTF-8");
	        //写入参数,DataOutputStream.writeBytes将字符串中的16位的unicode字符以8位的字符形式写道流里面
	        out.write(sb.toString());
	       // System.out.println("send_url:" + url);
	        //System.out.println("send_data:" + sb.toString());
	        // flush and close
	        out.flush();
	 
	    } catch (Exception e) {
	        e.printStackTrace();
	    } finally {
	        try {
	            if (null != out) {
	                out.close();
	            }
	            if (null != reader) {
	                reader.close();
	            }
	            if (null != httpConnection) {
	                httpConnection.disconnect();
	            }
	        } catch (Exception e2) {
	            e2.printStackTrace();
	        }
	    }
	 
	    try {
	        reader = new BufferedReader(new InputStreamReader(httpConnection.getInputStream(),"UTF-8"));
	        while ((responseMessage = reader.readLine()) != null) {
	            response.append(responseMessage);
	            response.append("\n");
	        }
	        if (!"failure".equals(response.toString())) {
	            System.out.println("success");
	        } else {
	            System.out.println("failue");
	        }
	        // 将该url的配置信息缓存起来
	        System.out.println(response.toString());
	        System.out.println(httpConnection.getResponseCode());
	    } catch (IOException e) {
	        e.printStackTrace();
	    }
	 
	    return response.toString();
	}


    
}

body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,th,td,input,select,textarea,option,button,p,abbr,address,artical,aside,b,bdo,blockquote,button,datalist,del,details,dialog,em,strong,dfn,code,samp,kbd,var,cite,footer,header,i,ins,legend,m,menu,nav,q,section,time{margin:0;padding:0;font-size:16px; font-family: "微软雅黑";}
table,td,th{border-collapse:collapse;border-spacing:0; font-family: "微软雅黑";}
fieldset,img,abbr,input{border:0} 

em,i{ font-style:normal;} 
ul,li{ list-style:none;} 
a,button,input{-webkit-tap-highlight-color:rgba(255,0,0,0);}

body a{outline:none;blr:expression(this.onFocus=this.blur());}
h1,h2,h3,h4,h5,h6,input,select,textarea,option,button{font-size:12px; font-weight:normal; outline: none;}
::-webkit-input-placeholder {color:#999999; font-size: 1.2em;}
:-moz-placeholder {color: #999999; font-size: 1.2em;}
::-moz-placeholder { color:#999999; font-size: 1.2em;}
:-ms-input-placeholder {color:#999999; font-size: 1.2em;}

.text1::-webkit-input-placeholder {color:#c2c2c2; font-size: 1em;}
.text1:-moz-placeholder {color: #c2c2c2; font-size: 1em;}
.text1::-moz-placeholder { color:#c2c2c2; font-size: 1em;}
.text1:-ms-input-placeholder {color:#c2c2c2; font-size: 1em;}

.t_l{ text-align:left;}
.t_c{ text-align:center;}
.t_r{ text-align:right;}


body{/*font-size: 16px;*/ background-color: #fff;}
.shoucang{background-color: #f3f3f3;}

img{width: 100%; cursor: pointer;}

a{text-decoration:none; color:#7e7d84; -webkit-tap-highlight-color:rgba(0,0,0,0);}
a:hover{ text-decoration:none;}
textarea{reszie:none;}/*禁止改变大小*/

.fl{float:left;display:inline;} 
.fr{float:right;display:inline;}
.clearfloat:after{display:block;clear:both;content:"";visibility:hidden;height:0}
.clearfloat{zoom:1}
.clear{clear:both;zoom:1;}
.dis_inBlock{display:inline-block;*display:inline;*zoom:1;}
.dis_block{ display:block;}

.mr1{ margin-right:1px;}
.mb1{ margin-bottom:1px;}

.m5{margin:5px}
.m10{margin:10px}
.m15{margin:15px}
.m20{margin:20px}
.m30{margin:30px}
.mt5{margin-top:5px}
.mt10{margin-top:10px}
.mt15{margin-top:15px}
.mt20{margin-top:20px}
.mt30{margin-top:30px}
.mt50{margin-top:50px}
.mt100{margin-top:100px}
.mb5{margin-bottom:5px}
.mb10{margin-bottom:10px}
.mb15{margin-bottom:15px}
.mb20{margin-bottom:20px}
.mb30{margin-bottom:30px}
.mb50{margin-bottom:50px}
.mb100{margin-bottom:100px}
.ml5{margin-left:5px}
.ml10{margin-left:10px}
.ml15{margin-left:15px}
.ml20{margin-left:20px}
.ml30{margin-left:30px}
.ml50{margin-left:50px}
.ml100{margin-left:100px}
.mr5{margin-right:5px}
.mr10{margin-right:10px}
.mr15{margin-right:15px}
.mr20{margin-right:20px}
.mr30{margin-right:30px}
.mr50{margin-right:50px}
.mr100{margin-right:100px}


body {
	-webkit-transition: -webkit-transform 0.3s ease;
	transition: transform 0.3s ease;
}
.box-s{
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
}

.opa2 {
  opacity: .2;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 20);
  filter: alpha(opacity = 20);
  -moz-opacity: .2;
  -khtml-opacity: .2;
}
.opa3 {
  opacity: .3;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 30);
  filter: alpha(opacity = 30);
  -moz-opacity: .3;
  -khtml-opacity: .3;
}
.opa4 {
  opacity: .4;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 40);
  filter: alpha(opacity = 40);
  -moz-opacity: .4;
  -khtml-opacity: .4;
}
.opa5 {
  opacity: .5;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 50);
  filter: alpha(opacity = 50);
  -moz-opacity: .5;
  -khtml-opacity: .5;
}
.opa6 {
  opacity: .6;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 60);
  filter: alpha(opacity = 60);
  -moz-opacity: .6;
  -khtml-opacity: .6;
}
.opa7 {
  opacity: .7;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 70);
  filter: alpha(opacity = 70);
  -moz-opacity: .7;
  -khtml-opacity: .7;
}
.opa8 {
  opacity: .8;
  -ms-filter: progid: DXImageTransform.Microsoft.Alpha(Opacity = 80);
  filter: alpha(opacity = 80);
  -moz-opacity: .8;
  -khtml-opacity: .8;
}

.warp{max-width: 640px; margin: 0 auto;}

/*清楚苹果按钮样式*/
input[type=”button”], input[type=”submit”], input[type=”reset”] {
	-webkit-appearance: none;
}

#main{position: relative;}

/*header*/
.header{width: 100%; height: 3.5em; background-color: #fff; border-bottom: 2px solid #ee2a7b; padding: .75em 5% 0 5%; position: fixed; top: 0; left: 0; z-index: 99;}
.header .search{width: 61.75%; height: 2em; background-color: #e5e4e4; border-radius: 5px; -webkit-border-radius: 5px;}
.header .search .btn{width: 20%; max-width: 40px; height: 2.7em; background: url(../img/search.png) center no-repeat; background-size: 50%; border-radius: 5px 0 0 5px; -webkit-border-radius: 5px 0 0 5px;}
.header .search .text{width: 80%; height: 2.7em; background-color: #e5e4e4; font-size: .7em; border-radius: 0 5px 5px 0; -webkit-border-radius: 0 5px 5px 0;}
.header .left{width: 16.6875%; text-align: center; margin-left: 2%; margin-top: -.3em;}
.header .left .icon1,.icon2{width: 35%; max-width: 24px; display: -webkit-inline-box; background: url(../img/wxiao.png) center no-repeat; background-size: 100%; height: 1.5em;}
.header .left p{color: #666666; font-size: .7em; line-height: .5em;}
.header .left .icon2{background: url(../img/shopcar.png) center no-repeat; background-size: 100%;}
.header1{border-bottom: 1px solid #c9c9c9;}
.header1 .search{width: 100%;}

.icon32{width: 55%; max-width: 28px; display: -webkit-inline-box; background: url(../img/menu.png) center no-repeat; background-size: 100%; height: 3em;}

.header2{width: 100%; height: 3.5em; background-color: #fff; border-bottom: 1px solid #e9e9e9; padding: 0 5%; position: fixed; top: 0; left: 0; z-index: 99;}
.header2 .left1,.header2 .right1{width: 4%; height: 3.5em; line-height: 4em;}
.header2 .left1 .back{display: block; width: 100%; height: 3.5em; background: url(../img/back.png) center left no-repeat; background-size: 38%; max-width: 40px;}
.header2 .middle{width: 90%; color: #333; text-align: center; height: 3.5em; line-height: 3.5em;}
.header2 .right1 .back{display: block; width: 100%; height: 3.5em; background: url(../img/home.png)right center no-repeat; background-size: 50%; max-width: 56px; float: right;}
.header2 .right1 .back1{display: block; width: 100%; height: 3.5em; background: url(../img/icon16.png)right center no-repeat; background-size: 40%; max-width: 56px; float: right;}

.header3 .left{margin-top:-.5em; text-align: left; width: 15%;}
.header3 .search{width: 83%;}

.header4 .left{margin-top:-.55em; text-align: left; width: 25%;}
.header4 .search{width: 73%;}
.icon45{width: 43px; height: 43px; border: 2px solid #eb9cbd; background: url(../upload/10.jpg) center no-repeat; border-radius: 50%; overflow: hidden; display: block;}
















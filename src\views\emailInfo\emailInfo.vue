<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="api_user" prop="apiUser" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.apiUser }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发件箱地址" prop="fromEmail" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.fromEmail }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发件箱名称" prop="emailName" min-width="160px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.emailName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" min-width="60px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.status==1?'Enable':'不Enable' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="api_user" prop="apiUser">
          <el-input v-model="temp.apiUser" />
        </el-form-item>
        <el-form-item label="api_key" prop="apiKey">
          <el-input v-model="temp.apiKey" />
        </el-form-item>
        <el-form-item label="发件箱地址" prop="fromEmail">
          <el-input v-model="temp.fromEmail" />
        </el-form-item>
        <el-form-item label="发件箱名称" prop="emailName">
          <el-input v-model="temp.emailName" />
        </el-form-item>
        <el-form-item label="验证码模板" prop="backup1">
          <el-input v-model="temp.backup1" />
        </el-form-item>
        <el-form-item label="开户模板" prop="backup2">
          <el-input v-model="temp.backup2" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="status in statuss"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="api_user" prop="apiUser">
          <el-input v-model="temp.apiUser" />
        </el-form-item>
        <el-form-item label="api_key" prop="apiKey">
          <el-input v-model="temp.apiKey" />
        </el-form-item>
        <el-form-item label="发件箱地址" prop="fromEmail">
          <el-input v-model="temp.fromEmail" />
        </el-form-item>
        <el-form-item label="发件箱名称" prop="emailName">
          <el-input v-model="temp.emailName" />
        </el-form-item>
        <el-form-item label="验证码模板" prop="backup1">
          <el-input v-model="temp.backup1" />
        </el-form-item>
        <el-form-item label="开户模板" prop="backup2">
          <el-input v-model="temp.backup2" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="status in statuss"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchEmailInfo, createEmailInfo, updateEmailInfo, updateIsAvailable, removeEmailInfo } from '@/api/emailInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'EmailInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      },
      temp: {
        id: undefined,
        apiUser: '',
        apiKey: '',
        fromEmail: '',
        emailName: '',
        status: 1,
        backup1: '',
        backup2: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 statuss: [
        {
          value: 1,
          label: 'Enable'
        },
        {
          value: 2,
          label: '不Enable'
        }
      ],
      rules: {
        apiUser: [
          { required: true, message: 'api_user不能为空', trigger: 'change' },,
        ],
        apiKey: [
          { required: true, message: 'api_key不能为空', trigger: 'change' }
        ],
        fromEmail: [
          { required: true, message: '发件箱地址不能为空', trigger: 'change' },,
        ],
        emailName: [
          { required: true, message: '发件箱名称不能为空', trigger: 'change' },,
        ],
        状态: [
          { required: true, message: '状态不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        apiUser: '',
        fromEmail: '',
        emailName: '',
        backup1: '',
        backup2: '',
        status: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.temp.status = 1
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createEmailInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateEmailInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeEmailInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

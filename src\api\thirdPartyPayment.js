import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/thirdPartyPayment/list',
    method: 'post',
    params: query
  })
}

export function fetchThirdPartyPayment(id) {
  return request({
    url: '/thirdPartyPayment/detail',
    method: 'get',
    params: { id }
  })
}

export function createThirdPartyPayment(data) {
  return request({
    url: '/thirdPartyPayment/add',
    method: 'post',
    data
  })
}

export function updateThirdPartyPayment(data) {
  return request({
    url: '/thirdPartyPayment/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/thirdPartyPayment/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeThirdPartyPayment(id) {
  return request({
    url: '/thirdPartyPayment/remove',
    method: 'get',
    params: { id }
  })
}


package com.ews.system.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ews.system.entity.Permission;
import com.ews.system.entity.Role;
import com.ews.system.entity.User;
import com.ews.system.entity.UserRole;
import com.ews.system.repository.UserRoleRepository;
import com.ews.system.service.PermissionService;
import com.ews.system.service.RoleService;
import com.ews.system.service.UserRoleService;
import com.ews.system.service.UserService;

@Service
public class UserRoleServiceImpl implements UserRoleService {
	@Autowired
	private UserRoleRepository userRoleRepository;

	@Autowired
	private UserRoleService userRoleService;

	@Autowired
	private PermissionService permissionService;

	@Autowired
	private RoleService roleService;
	@Autowired
	private UserService userService;

	@Override
	public List<UserRole> findByUserId(Long userId) {
		// TODO Auto-generated method stub
		return userRoleRepository.findByUserId(userId);
	}
	@Override
	public List<String> findRoleStrsByUserId(Long userId) {
		List<String> list = new ArrayList();
		list.add("home");
		List<UserRole> urs = this.userRoleService.findByUserId(userId);
		for (UserRole ur : urs) {
			Role role = this.roleService.findById(ur.getRoleId());
			if(role.getRoleSign()!=null&&role.getRoleSign().equals("-10000")) {
				List<Permission> permissions = permissionService.findAll();
				for (Permission permission : permissions) {
					list.add(permission.getPermissionSign());
				}
			}else {
				if (role.getIsAvailable() == 1 && role.getIsDeleted() == 0) {
					List<Permission> permissions = permissionService.getPermissions(ur.getRoleId());
					for (Permission permission : permissions) {
						list.add(permission.getPermissionSign());
					}
				}
			}
		
		}
		return list;
	}

	@Override
	public UserRole findByUserIdAndRoleId(Long userId, Long roleId) {
		// TODO Auto-generated method stub
		return this.userRoleRepository.findByUserIdAndRoleId(userId, roleId);
	}

	@Override
	public void deleteByRoleId(Long roleId) {
		this.userRoleRepository.removeByRoleId(roleId);
		
	}

}

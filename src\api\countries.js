import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/countries/list',
    method: 'post',
    params: query
  })
}

export function fetchCountriesList(query) {
  return request({
    url: '/countries/list',
    method: 'post',
    params: query
  })
}

export function fetchCountries(id) {
  return request({
    url: '/countries/detail',
    method: 'get',
    params: { id }
  })
}

export function createCountries(data) {
  return request({
    url: '/countries/add',
    method: 'post',
    data
  })
}

export function updateCountries(data) {
  return request({
    url: '/countries/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/countries/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeCountries(id) {
  return request({
    url: '/countries/remove',
    method: 'get',
    params: { id }
  })
}


import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/tradeAccount/list',
    method: 'post',
    params: query
  })
}
export function fetchList2(query) {
  return request({
    url: '/tradeAccount/list2',
    method: 'post',
    params: query
  })
}

export function fetchTradeAccount(id) {
  return request({
    url: '/tradeAccount/detail',
    method: 'get',
    params: { id }
  })
}

export function createTradeAccount(data) {
  return request({
    url: '/tradeAccount/add',
    method: 'post',
    data
  })
}

export function updateTradeAccount(data) {
  return request({
    url: '/tradeAccount/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/tradeAccount/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}

export function removeTradeAccount(id) {
  return request({
    url: '/tradeAccount/remove',
    method: 'get',
    params: { id }
  })
}
export function auditTradeAccount(id) {
  return request({
    url: '/tradeAccount/audit',
    method: 'get',
    params: { id }
  })
}

export function exportTradeAccountExcel(query) {
  return request({
    url: '/tradeAccount/exportTradeAccountExcel',
    method: 'post',
    params: query
  })
}

package com.ews.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import com.ews.config.filter.MobileFilter;

@Configuration
public class MyConfiguration {
	@Value("${cors.allowed.origin}")
	private String allowedOrigin;

	@Bean
	public CorsFilter corsFilter() {
		System.out.println("CorsFilter do filter ");
		
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        
		source.registerCorsConfiguration("/**", buildConfig()); // 对接口配置跨域设置
        
		return new CorsFilter(source);
	}

	private CorsConfiguration buildConfig() {
		System.out.println(" MyConfiguration buildConfig");
		CorsConfiguration corsConfiguration = new CorsConfiguration();
		corsConfiguration.setAllowCredentials(true);
		corsConfiguration.setMaxAge(24 * 3600L); // 预检请求的有效期
		System.out.println("allowedOrigin:"+allowedOrigin);
		if (null != allowedOrigin) {
			String[] origins = allowedOrigin.split(",");
			for (String origin : origins) {
				corsConfiguration.addAllowedOrigin(origin); // 允许跨域请求的源地址
			}
		}
		corsConfiguration.addAllowedHeader("*"); // 支持的所有头信息字段
		corsConfiguration.addAllowedMethod("*"); // 支持的所有跨域请求的方法
		return corsConfiguration;
	}
	
	
	
	/**
	 * 前端filter
	 * @return
	 */
	@Bean
    public FilterRegistrationBean someFilterRegistration() {
        //新建过滤器注册类
        FilterRegistrationBean registration = new FilterRegistrationBean();
        // 添加过滤器
        registration.setFilter( new MobileFilter());
        // 设置过滤器的URL模式
        registration.addUrlPatterns("/mobile/*");
        registration.setOrder(1);
        return registration;
 }
}

package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "company_info")
public class CompanyInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *公司名称
    **/
	@Column(name = "company_name")
	protected  String companyName;

    /**
    *公司logo
    **/
	@Column(name = "company_logo")
	protected  String companyLogo;

    /**
    *登陆页logo
    **/
	@Column(name = "login_logo")
	protected  String loginLogo;

    /**
    *系统名称
    **/
	@Column(name = "system_name")
	protected  String systemName;

    /**
    *官网地址
    **/
	@Column(name = "web_url")
	protected  String webUrl;

    /**
    *社区地址
    **/
	@Column(name = "community_url")
	protected  String communityUrl;

    /**
    *电子邮箱
    **/
	@Column(name = "email")
	protected  String email;

    /**
    *联系电话
    **/
	@Column(name = "telephone")
	protected  String telephone;

    /**
    *backup1   --欢迎语
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2   --系统标识（用于识别crm客户，例如三方支付时根据不同客户跳转不同页面）
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3  --配资协议
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4  --系统简称
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5
    **/
	@Column(name = "backup5")
	protected  String backup5;

    /**
    *backup6
    **/
	@Column(name = "backup6")
	protected  String backup6;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getCompanyName()
    {
        return companyName;
    }
    public void setCompanyName(String  companyName)
    {
        this.companyName = companyName;
    }
    public String  getCompanyLogo()
    {
        return companyLogo;
    }
    public void setCompanyLogo(String  companyLogo)
    {
        this.companyLogo = companyLogo;
    }
    public String  getLoginLogo()
    {
        return loginLogo;
    }
    public void setLoginLogo(String  loginLogo)
    {
        this.loginLogo = loginLogo;
    }
    public String  getSystemName()
    {
        return systemName;
    }
    public void setSystemName(String  systemName)
    {
        this.systemName = systemName;
    }
    public String  getWebUrl()
    {
        return webUrl;
    }
    public void setWebUrl(String  webUrl)
    {
        this.webUrl = webUrl;
    }
    public String  getCommunityUrl()
    {
        return communityUrl;
    }
    public void setCommunityUrl(String  communityUrl)
    {
        this.communityUrl = communityUrl;
    }
    public String  getEmail()
    {
        return email;
    }
    public void setEmail(String  email)
    {
        this.email = email;
    }
    public String  getTelephone()
    {
        return telephone;
    }
    public void setTelephone(String  telephone)
    {
        this.telephone = telephone;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public String  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(String  backup5)
    {
        this.backup5 = backup5;
    }
    public String  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(String  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

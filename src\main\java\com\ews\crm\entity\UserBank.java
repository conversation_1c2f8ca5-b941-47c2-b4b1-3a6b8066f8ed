package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "user_bank")
public class UserBank implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *用户ID
    **/
	@Column(name = "user_id")
	protected  Long userId;

    /**
    *账户名称
    **/
	@Column(name = "account_name")
	protected  String accountName;

    /**
    *开户行
    **/
	@Column(name = "bank_name")
	protected  String bankName;

    /**
    *银行账号
    **/
	@Column(name = "bank_account")
	protected  String bankAccount;

    /**
    *开户姓名
    **/
	@Column(name = "account_username")
	protected  String accountUsername;

    /**
    *银行地址
    **/
	@Column(name = "bank_address")
	protected  String bankAddress;

    /**
    *预留手机号
    **/
	@Column(name = "tel")
	protected  String tel;

    /**
    *swift
    **/
	@Column(name = "swift")
	protected  String swift;

    /**
    *状态
    **/
	@Column(name = "status")
	protected  Integer status;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public String  getAccountName()
    {
        return accountName;
    }
    public void setAccountName(String  accountName)
    {
        this.accountName = accountName;
    }
    public String  getBankName()
    {
        return bankName;
    }
    public void setBankName(String  bankName)
    {
        this.bankName = bankName;
    }
    public String  getBankAccount()
    {
        return bankAccount;
    }
    public void setBankAccount(String  bankAccount)
    {
        this.bankAccount = bankAccount;
    }
    public String  getAccountUsername()
    {
        return accountUsername;
    }
    public void setAccountUsername(String  accountUsername)
    {
        this.accountUsername = accountUsername;
    }
    public String  getBankAddress()
    {
        return bankAddress;
    }
    public void setBankAddress(String  bankAddress)
    {
        this.bankAddress = bankAddress;
    }
    public String  getTel()
    {
        return tel;
    }
    public void setTel(String  tel)
    {
        this.tel = tel;
    }
    public String  getSwift()
    {
        return swift;
    }
    public void setSwift(String  swift)
    {
        this.swift = swift;
    }
    public Integer  getStatus()
    {
        return status;
    }
    public void setStatus(Integer  status)
    {
        this.status = status;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.ExchangeRate;
import com.ews.crm.repository.ExchangeRateRepository;
import com.ews.crm.service.ExchangeRateService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class ExchangeRateServiceImpl implements ExchangeRateService 
{
	@Autowired
	private ExchangeRateRepository exchangeRateRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<ExchangeRate> findAll(Integer page, Integer size,String sortName,String sortOrder, ExchangeRate exchangeRate) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<ExchangeRate> pages = exchangeRateRepository.findAll(new Specification<ExchangeRate>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<ExchangeRate> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public ExchangeRate findById(Long id) {
      Optional<ExchangeRate> op = exchangeRateRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.exchangeRateRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         ExchangeRate old = exchangeRateRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 exchangeRateRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 exchangeRateRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.exchangeRateRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(ExchangeRate exchangeRate) {
       /* User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }*/
        Result vr = new Result();
		try {
        	if (exchangeRate.getId()== null) {
            	exchangeRate.setGmtCreate(new Date());
            	exchangeRate.setGmtModified(new Date());
            	exchangeRate.setIsDeleted(0);
            	if(exchangeRate.getIsAvailable() == null) {
            		exchangeRate.setIsAvailable(1);
            	}
            	//exchangeRate.setUserCreate(loginUser.getUserId());
	    	} else {
            	exchangeRate.setGmtModified(new Date());
        	}
            exchangeRateRepository.save(exchangeRate);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



<template>
  <div class="app-container">
    <div class="filter-container">

      <el-input v-model="listQuery.transferOut" :placeholder="$t('transferInfoAudit.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.transferIn" :placeholder="$t('transferInfoAudit.label3')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('transferInfoAudit.label1')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label2')" prop="transferOut" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferOut }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label3')" prop="transferIn" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferIn }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label4')" prop="fullname" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.fullname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label5')" prop="userName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label6')" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('transferInfoAudit.label7')" prop="transferAmount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.transferAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleDelete(scope.row,1)">{{ $t('transferInfoAudit.label8') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row,2)">{{ $t('transferInfoAudit.label9') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList2, fetchTransferInfo, createTransferInfo, updateTransferInfo, updateIsAvailable, removeTransferInfo, auditTransferInfo } from '@/api/transferInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'TransferInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        transferOut: undefined,
        transferIn: undefined,
        outStatus: undefined,
        inStatus: undefined,
        transferStatus: undefined,
        auditStatus: undefined,
        outOrderId: undefined,
        inOrderId: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        transferOut: '',
        transferIn: '',
        transferAmount: '',
        outStatus: '',
        inStatus: '',
        transferStatus: '',
        auditStatus: '',
        outOrderId: '',
        inOrderId: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        transferOut: [
        ],
        transferIn: [
        ],
        transferAmount: [
        ],
        outStatus: [
        ],
        inStatus: [
        ],
        transferStatus: [
        ],
        auditStatus: [
        ],
        remark: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        outOrderId: [
        ],
        inOrderId: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList2(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        transferOut: '',
        transferIn: '',
        transferAmount: '',
        outStatus: '',
        inStatus: '',
        transferStatus: '',
        auditStatus: '',
        outOrderId: '',
        inOrderId: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTransferInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateTransferInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row, type) {
      this.$confirm('Are you sure you want to review this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await auditTransferInfo(row.id, type).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'Audit successful',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

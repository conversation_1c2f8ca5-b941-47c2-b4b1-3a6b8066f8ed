package com.ews.system.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ews.common.DateUtil;
import com.ews.common.EncryptUtils;
import com.ews.common.RandomStrUtil;
import com.ews.system.entity.LoginUser;
import com.ews.system.entity.User;
import com.ews.system.model.IPermission;
import com.ews.system.model.IUserRole;
import com.ews.system.repository.UserRepository;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserRoleService;
import com.ews.system.service.UserService;

@Service
public class UserServiceImpl implements UserService {

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private LoginService loginService;
	
	@Autowired
	private UserRoleService userRoleService;

	@Override
	public User findByUserName(String userName) {
		return userRepository.findByUsername(userName);
	}

	@Override
	public List<IUserRole> findUserRoleByUserName(String userName) {
		return userRepository.findUserRoleByUsername(userName);

	}

	@Override
	public List<IUserRole> findAllUserRoleByUserId(Long userId) {
		return userRepository.findAllUserRoleByUserId(userId);
	}

	@Override
	public List<IPermission> findUserRolePermissionByUserName(String userName) {
		return userRepository.findUserRolePermissionByUsername(userName);
	}

	@Override
	public User findUserById(Long userId) {
		return userRepository.findNormalUserById(userId);
	}

	@Override
	public User save(User user) {
		return userRepository.save(user);
	}

	@Override
	public Page<User> findAllByUserNameContains(String userName, Pageable pageable) {
		return userRepository.findAllByUsernameContains(userName, pageable);
	}

	@Transactional
	@Override
	public void deleteAllUserByUserIdList(List<Long> userIdList) {
		userRepository.deleteAllUserRoleByUserIdList(userIdList);
		userRepository.deleteAllUserByUserIdList(userIdList);
	}

	@Transactional
	@Override
	public void deleteAllUserRoleByUserIdList(List<Long> userIdList) {
		userRepository.deleteAllUserRoleByUserIdList(userIdList);
	}

	@Transactional
	@Override
	public void deleteAllUserRoleByUserId(Long userId) {
		userRepository.deleteAllUserRoleByUserId(userId);
	}

	@Transactional
	@Override
	public void grantUserRole(Long userId, List<Long> roleIdList) {
		userRepository.deleteAllUserRoleByUserId(userId);
		for (Long roleId : roleIdList) {
			if(this.userRoleService.findByUserIdAndRoleId(userId, roleId) == null) {
				userRepository.insertUserRole(userId, roleId);
			}
		}
	}

	@Transactional
	@Override
	public boolean checkUserIsNormal(String username) {
		User user = userRepository.findByUsername(username);
		if (user != null && user.getIsAvailable().equals(1) && user.getIsDeleted().equals(0)) {
			return true;
		}
		return false;
	}

	@SuppressWarnings("serial")
	@Override
	public Page<User> findAll(Integer page, Integer size, String sortName, String sortOrder, User user) {
		// TODO Auto-generated method stub
		Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC,
				sortName);
		Pageable pageable = PageRequest.of(page, size, sortLocal);

		Page<User> pages = userRepository.findAll(new Specification<User>() {
			@Override
			public Predicate toPredicate(Root<User> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				List<Predicate> predicates = new ArrayList<>();
				predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));
				//过滤掉超级管理员
				
				predicates.add(
						criteriaBuilder.notEqual(root.get("userId").as(Integer.class), 1));
				
				
				if (!StringUtils.isEmpty(user.getNickName())) {
					predicates.add(criteriaBuilder.like(root.get("nickName").as(String.class),
							"%" + user.getNickName() + "%"));
				}
				if (!StringUtils.isEmpty(user.getUsername())) {
					predicates.add(criteriaBuilder.like(root.get("username").as(String.class),
							"%" + user.getUsername() + "%"));
				}
				
				if (!StringUtils.isEmpty(user.getSortStr())) {
					predicates.add(criteriaBuilder.like(root.get("orderStr").as(String.class),
							"%" + user.getSortStr() + "%"));
				}
				if (!StringUtils.isEmpty(user.getIsAvailable())) {
					predicates.add(
							criteriaBuilder.equal(root.get("isAvailable").as(Integer.class), user.getIsAvailable()));
				}

				if (!StringUtils.isEmpty(user.getGmtCreateBegin()) || !StringUtils.isEmpty(user.getGmtCreateEnd())) {
					try {
						if (!StringUtils.isEmpty(user.getGmtCreateBegin())
								&& StringUtils.isEmpty(user.getGmtCreateEnd())) {// 只有开始时间
							Date begin = DateUtil.parseDate(user.getGmtCreateBegin(), "yyyy-MM-dd HH:mm:ss");
							predicates.add(
									criteriaBuilder.greaterThanOrEqualTo(root.get("gmtCreate").as(Date.class), begin));// 小于开始时间
						} else if (StringUtils.isEmpty(user.getGmtCreateBegin())
								&& !StringUtils.isEmpty(user.getGmtCreateEnd())) {// 只有截至时间
							Date end = DateUtil.parseDate(user.getGmtCreateEnd(), "yyyy-MM-dd HH:mm:ss");
							predicates
									.add(criteriaBuilder.lessThanOrEqualTo(root.get("gmtCreate").as(Date.class), end));// 大于截至时间
						} else {
							Date begin = DateUtil.parseDate(user.getGmtCreateBegin(), "yyyy-MM-dd HH:mm:ss");
							Date end = DateUtil.parseDate(user.getGmtCreateEnd(), "yyyy-MM-dd HH:mm:ss");
							predicates.add(criteriaBuilder.between(root.get("gmtCreate"), begin, end));
						}
					} catch (ParseException e) {
						e.printStackTrace();
					}
				}
				
				 if(!StringUtils.isEmpty(user.getStoreId())) { 
	                 predicates.add(criteriaBuilder.equal(root.get("storeId").as(Long.class), user.getStoreId()));
	              }
				 if(!StringUtils.isEmpty(user.getDepartmentId())) { 
	                 predicates.add(criteriaBuilder.equal(root.get("departmentId").as(Long.class), user.getDepartmentId()));
	              }
				 
				 if(!StringUtils.isEmpty(user.getReId())) { 
	                 predicates.add(criteriaBuilder.equal(root.get("reId").as(Long.class), user.getReId()));
	              }
				 
				 if(!StringUtils.isEmpty(user.getRoleType())) { 
					 if(user.getRoleType().intValue()!=-99) {
	                 predicates.add(criteriaBuilder.equal(root.get("roleType").as(Long.class), user.getRoleType()));
					 }else {
						// predicates.add(criteriaBuilder.ge(root.get("roleType").as(Long.class), 0));
						 
						 
						     Path<Object> path = root.get("roleType");
							CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
								  in.value(new Long(1));
								  in.value(new Long(2));
								  in.value(new Long(3));
								  in.value(new Long(4));
							predicates.add(criteriaBuilder.and(in));
					 }
	              }
				 
				 if (!StringUtils.isEmpty(user.getSaleSerial())) {
						predicates.add(criteriaBuilder.equal(root.get("saleSerial").as(String.class),
								user.getSaleSerial()));
					}
				query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
				return query.getRestriction();
			}

		}, pageable);
		return pages;
	}

	@Transactional
	@Override
	public User saveOrUpdate(User user, String[] roleIdsStr) {
		boolean bl=true;
		
		User loginUser = this.findByUserName(this.loginService.getCurrentUserName());
		if(loginUser==null) {
			return null;
		}
		
		User newUser = null;
		if (user.getUserId() == null) {
			user.setSalt(RandomStrUtil.generate(32));// 32位随机密码盐
			user.setGmtCreate(new Date());
			String encryptPwd = new String();
			try {
				encryptPwd = EncryptUtils.encrypt(user.getPassword(), user.getCredentialsSalt());
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			user.setPassword(encryptPwd);
			user.setIsDeleted(0);
			if (user.getIsAvailable() == null) {
				user.setIsAvailable(1);
			}
			user.setUserType(1);
			user.setUserCreate(loginUser.getUserId());
			newUser = this.userRepository.save(user);
		} else {
			User old = this.userRepository.findById(user.getUserId()).get();
			old.setGmtModified(new Date());
			old.setNickName(user.getNickName());
			old.setEmail(user.getEmail());
			old.setMobile(user.getMobile());
			old.setBirthday(user.getBirthday());
			old.setDepartmentId(user.getDepartmentId());
			old.setStoreId(user.getStoreId());
			old.setRoleType(user.getRoleType());
			old.setReId(user.getReId());
			old.setUserIcon(user.getUserIcon());
			old.setSaleSerial(user.getSaleSerial());
			
			if(user.getIsAvailable()!=null) {
				old.setIsAvailable(user.getIsAvailable());
			}
			newUser = userRepository.save(old);
			
			if(old.getUserId()==loginUser.getUserId()) {
				bl=false;
			}
		}
		// 保存角色信息
		
		
		if(bl) {
		List<Long> roleIds = new ArrayList();
		System.out.println(roleIdsStr);
		if (roleIdsStr != null && roleIdsStr.length > 0) {
			for (int i = 0; i < roleIdsStr.length; i++) {
					roleIds.add(Long.parseLong(roleIdsStr[i]));
			}
		}
		this.grantUserRole(newUser.getUserId(), roleIds);
		}
		return newUser;
	}

	@Override
	@Transactional
	public boolean updateIsAvailableById(Long userId, Integer isAvailable) {
		// TODO Auto-generated method stub
		try {
			if (this.userRepository.updateIsAvailableById(userId, isAvailable) == 1) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return false;
	}

	@Override
	@Transactional
	public boolean logicalDeleteByUserId(Long userId) {
		// TODO Auto-generated method stub
		if (this.userRepository.existsById(userId)) {
			if (this.userRepository.logicalDeleteById(userId) == 1) {
				return true;
			}
			return false;
		}
		return false;
	}

	@Override
	public boolean updatePasswordByUserId(Long userId, String password) {
		// TODO Auto-generated method stub
		String encryptPwd = new String();
		User user = this.findUserById(userId);
		if (user != null) {
			try {
				encryptPwd = EncryptUtils.encrypt(password, user.getCredentialsSalt());
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			user.setPassword(encryptPwd);
			user.setGmtModified(new Date());
			userRepository.save(user);
			return true;
		}

		return false;
	}

	@Override
	public boolean checkExistedByIdAndUserName(Long userId, String username) {
		// TODO Auto-generated method stub
		User user = this.userRepository.findUserExistByUserName(userId, username);
		if (user != null) {
			return true;
		}
		return false;
	}

	@Override
	public boolean checkExistedByIdAndEmail(Long userId, String email) {
		// TODO Auto-generated method stub
		User user = this.userRepository.findUserExistByEmail(userId, email);
		if (user != null) {
			return true;
		}
		return false;
	}

	@Override
	public Map<String, String> checkExistedByIdAndUserNameAndEmail(Long userId, String username, String email) {
		// TODO Auto-generated method stub
		Map<String, String> map = new HashMap();
		if (userId == null) {
			userId = Long.MIN_VALUE;
		}
		map.put("existed", "false");

		if (this.checkExistedByIdAndUserName(userId, username)) {
			map.put("existed", "true");
			map.put("type", "1");
		} else {
			if (!StringUtils.isEmpty(email)) {
				if (this.checkExistedByIdAndEmail(userId, email)) {
					map.put("existed", "true");
					map.put("type", "2");
				}
			}
		}
		return map;
	}

	@Override
	public LoginUser findLoginUserByUserName(String username) {
		// TODO Auto-generated method stub
		User user = this.findByUserName(username);
		if(user!=null) {
			LoginUser loginUser = new LoginUser();
			loginUser.setUserId(user.getUserId());
			loginUser.setUsername(user.getUsername());
			loginUser.setIsAvailable(user.getIsAvailable());
			loginUser.setMobile(user.getMobile());
			loginUser.setNickName(user.getNickName());
			loginUser.setStoreId(user.getStoreId());
			loginUser.setDepartmentId(user.getDepartmentId());
			loginUser.setRoles(userRoleService.findRoleStrsByUserId(user.getUserId()));
			loginUser.setUserIcon(user.getUserIcon());
			loginUser.setSaleSerial(user.getSaleSerial());
			loginUser.setRoleType(user.getRoleType());
			return loginUser;
		}
		return null;
	}
	
	

}

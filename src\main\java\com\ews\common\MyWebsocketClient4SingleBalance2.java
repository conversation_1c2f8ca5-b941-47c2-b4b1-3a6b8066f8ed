package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.service.TradeAccountService;


public class MyWebsocketClient4SingleBalance2 extends WebSocketClient {
	private TradeAccountService tradeAccountService;
	
	
	
	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}

	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}

	public MyWebsocketClient4SingleBalance2(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
			if(jsStr.getString("UserLists")!=null&&!jsStr.getString("UserLists").equals("")&&!jsStr.getString("UserLists").equals("null")) {
				JSONArray ja=JSONArray.parseArray(jsStr.getString("UserLists"));
				for(int m=0;m<ja.size();m++) {
					JSONObject ob=(JSONObject)ja.get(m);
					try {
						TradeAccount ta_query=new TradeAccount();
						ta_query.setTradeId(ob.getString("login"));
					    Page<TradeAccount> page=this.tradeAccountService.findAll(0,1,"id","asc", ta_query);
						if(page.getContent().size()>0) {
							TradeAccount ta=(TradeAccount)page.getContent().get(0);
							ta.setBalance3(new Double(ob.get("equity").toString()));  //净值
							ta.setBalance4(new Double(ob.get("margin").toString()));   //已用预付款
							ta.setBalance5(new Double(ob.get("margin_free").toString())); //可用预付款
							
							this.tradeAccountService.saveOrUpdate(ta);
						}
					}catch(Exception e) {
						System.out.println(e);
					}
				}
			
			}
			
			
		}
		new Thread(){
    		public void run(){
    			try {
    				 Thread.sleep(1000);
    			     close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭"+arg0);
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

package com.ews.system.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "enclosure")
public class Enclosure implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *业务类型
    **/
	@Column(name = "bus_type")
	protected  Integer busType;

    /**
    *业务id
    **/
	@Column(name = "bus_id")
	protected  Long busId;

    /**
    *附件原始名称
    **/
	@Column(name = "endix_name")
	protected  String endixName;

    /**
    *附件存储路径
    **/
	@Column(name = "endix_url")
	protected  String endixUrl;

    /**
     * 附件类型
    **/
	@Column(name = "endix_type")
	protected  String endixType;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getBusType()
    {
        return busType;
    }
    public void setBusType(Integer  busType)
    {
        this.busType = busType;
    }
    public Long  getBusId()
    {
        return busId;
    }
    public void setBusId(Long  busId)
    {
        this.busId = busId;
    }
    public String  getEndixName()
    {
        return endixName;
    }
    public void setEndixName(String  endixName)
    {
        this.endixName = endixName;
    }
    public String  getEndixUrl()
    {
        return endixUrl;
    }
    public void setEndixUrl(String  endixUrl)
    {
        this.endixUrl = endixUrl;
    }
    public String  getEndixType()
    {
        return endixType;
    }
    public void setEndixType(String  endixType)
    {
        this.endixType = endixType;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

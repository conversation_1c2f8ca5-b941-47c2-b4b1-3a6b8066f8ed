package com.ews.common;

import javax.servlet.http.HttpServletRequest;

public class WebUtil {
	/**
	 * 判断请求是否为ajax
	 * @param request
	 * @return
	 */
    public static boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("x-requested-with");
        if (requestedWith != null && requestedWith.equalsIgnoreCase("XMLHttpRequest")) {
            return true;
        } else {
            return false;
        }
    }
}

<template>
  <div class="app-container">

    <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">

    
      <el-form-item :label="$t('companyInfo.label4')">
        <el-upload
          class="avatar-uploader"
          :action="uploadUrl"
          :auto-upload="true"
          :show-file-list="false"
          :data="uploadData"
          :headers="myHeaders"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
          accept="image/gif,image/jpeg,image/jpg,image/png"
        >
          <img v-if="temp.companyLogo" :src="temp.companyLogo" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </el-upload>
      </el-form-item>
     
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateData()">{{ $t('companyInfo.button1') }}</el-button>
    </div>

  </div>
</template>
<script>
import {
  	getUploadUrl
} from '@/api/upload'
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchCompanyInfo, createCompanyInfo, updateCompanyInfo, updateIsAvailable, removeCompanyInfo } from '@/api/companyInfo'
import { getToken } from '@/utils/auth.js'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import Tinymce from '@/components/Tinymce'
export default {
  name: 'CompanyInfoTable',
  components: { Pagination, Tinymce },
  directives: { waves },
  data() {
    return {
      uploadUrl: '',
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        companyName: undefined
      }, uploadData: {
        id: undefined,
        type: 1
      },
      myHeaders: {
				 'X-Token': getToken()
      },
      temp: {
        id: undefined,
        companyName: '',
        companyLogo: '',
        loginLogo: '',
        systemName: '',
        webUrl: '',
        backup1: '',
        backup3: '',
        communityUrl: '',
        email: '',
        telephone: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        companyName: [
          { required: true, message: 'Company name cannot be empty', trigger: 'change' },,
        ],
        companyLogo: [
          { required: true, message: 'Company logo cannot be empty', trigger: 'change' },,
        ],
        loginLogo: [
          { required: true, message: 'The landing page image cannot be empty', trigger: 'change' },,
        ],
        systemName: [
          { required: true, message: 'System name cannot be empty', trigger: 'change' },,
        ],
        webUrl: [
        ],
        communityUrl: [,

        ],
        email: [
        ],
        telephone: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getUploadUrl()
    fetchCompanyInfo(1).then(res => {
	        this.temp = Object.assign({}, res.data)
    })
  },
  methods: {
    getList() {
    },		getUploadUrl() {
      getUploadUrl().then(res => {
        if (res.code != 20000) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
        this.uploadUrl = res.msg
      })
    },
    handleAvatarSuccess(res, file) {
      this.temp.companyLogo = res.data.endixUrl
    },
    handleAvatarSuccess2(res, file) {
    	this.temp.loginLogo = res.data.endixUrl
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isLt2M) {
        this.$message.error('The size of the uploaded image cannot exceed 2MB!')
        return false
      }
      return true
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateCompanyInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'update success',
                type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 }
  }
}
</script>

<style>
	.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>


package com.ews.crm.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.RekebackRule;
import com.ews.crm.entity.RuleGroupMap;
import com.ews.crm.entity.RuleProdMap;
import com.ews.crm.entity.RuleUserMap;
import com.ews.crm.service.RekebackRuleService;
import com.ews.crm.service.RuleGroupMapService;
import com.ews.crm.service.RuleProdMapService;
import com.ews.crm.service.RuleUserMapService;



@RestController
@RequestMapping("/admin/rekebackRule")
public class RekebackRuleController {
	@Autowired
	private RekebackRuleService rekebackRuleService;
	@Autowired
	private RuleProdMapService ruleProdMapService;
	@Autowired
	private RuleGroupMapService ruleGroupMapService;
	
	@Autowired
	private RuleUserMapService ruleUserMapService;

   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	RekebackRule query  = new RekebackRule();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("ruleName"))) {
               	query.setRuleName(request.getParameter("ruleName").trim());
        	}
        	Page<RekebackRule> pages = rekebackRuleService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<RekebackRule> rekebackRules = pages.getContent();
        	for(int i=0;i<rekebackRules.size();i++) {
        		RekebackRule entity  = rekebackRules.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", rekebackRules);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			RekebackRule rekebackRule = new RekebackRule();
        	rekebackRule.setRuleName(data.getString("ruleName"));
        	rekebackRule.setRekebackA1(data.getDouble("rekebackA1"));
        	rekebackRule.setRekebackA2(data.getDouble("rekebackA2"));
        	rekebackRule.setRekebackA3(data.getDouble("rekebackA3"));
        	rekebackRule.setRekebackB1(data.getDouble("rekebackB1"));
        	rekebackRule.setRekebackB2(data.getDouble("rekebackB2"));
        	rekebackRule.setRekebackB3(data.getDouble("rekebackB3"));
        	rekebackRule.setRekebackB4(data.getDouble("rekebackB4"));
        	rekebackRule.setRekebackB5(data.getDouble("rekebackB5"));
        	rekebackRule.setRekebackB6(data.getDouble("rekebackB6"));
        	rekebackRule.setRekebackB7(data.getDouble("rekebackB7"));
        	rekebackRule.setRekebackB8(data.getDouble("rekebackB8"));
        	rekebackRule.setRekebackB9(data.getDouble("rekebackB9"));
        	rekebackRule.setRekebackB10(data.getDouble("rekebackB10"));
        	rekebackRule.setRekebackB11(data.getDouble("rekebackB11"));
        	rekebackRule.setRekebackB12(data.getDouble("rekebackB12"));
        	rekebackRule.setRekebackB13(data.getDouble("rekebackB13"));
        	rekebackRule.setRekebackB14(data.getDouble("rekebackB14"));
        	rekebackRule.setRekebackB15(data.getDouble("rekebackB15"));
        	rekebackRule.setRekebackB16(data.getDouble("rekebackB16"));
        	rekebackRule.setRekebackB17(data.getDouble("rekebackB17"));
        	rekebackRule.setRekebackB18(data.getDouble("rekebackB18"));
        	rekebackRule.setRekebackB19(data.getDouble("rekebackB19"));
        	rekebackRule.setRekebackB20(data.getDouble("rekebackB20"));
        	rekebackRule.setRekebackB21(data.getDouble("rekebackB21"));
        	rekebackRule.setRekebackB22(data.getDouble("rekebackB22"));
        	rekebackRule.setRekebackB23(data.getDouble("rekebackB23"));
        	rekebackRule.setRekebackB24(data.getDouble("rekebackB24"));
        	rekebackRule.setRekebackB25(data.getDouble("rekebackB25"));
        	rekebackRule.setRekebackB26(data.getDouble("rekebackB26"));
        	rekebackRule.setRekebackB27(data.getDouble("rekebackB27"));
        	rekebackRule.setRekebackB28(data.getDouble("rekebackB28"));
        	rekebackRule.setRekebackB29(data.getDouble("rekebackB29"));
        	rekebackRule.setRekebackB30(data.getDouble("rekebackB30"));
        	rekebackRule.setBackup5(data.getInteger("backup5"));
        	rekebackRule.setRemark(data.getString("remark"));
			Result re = rekebackRuleService.saveOrUpdate(rekebackRule);
			Object[] o1 = data.getJSONArray("tradeProds").toArray();
			for(int i=0;i<o1.length;i++) {
				RuleProdMap rpm=new RuleProdMap();
				rpm.setRuleId(rekebackRule.getId());
				rpm.setProdId(new Long(o1[i].toString()));
				this.ruleProdMapService.saveOrUpdate(rpm);
			}
			Object[] o2 = data.getJSONArray("userGroups").toArray();
			for(int i=0;i<o2.length;i++) {
				
				RuleGroupMap rgm=new RuleGroupMap();
				rgm.setRuleId(rekebackRule.getId());
				rgm.setGroupId(new Long(o2[i].toString()));
				this.ruleGroupMapService.saveOrUpdate(rgm);
			}
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(rekebackRule);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			RekebackRule rekebackRule = this.rekebackRuleService.findById(id);
				if (rekebackRule != null) {
					rekebackRule.setRuleName(data.getString("ruleName"));
					rekebackRule.setRekebackA1(data.getDouble("rekebackA1"));
					rekebackRule.setRekebackA2(data.getDouble("rekebackA2"));
					rekebackRule.setRekebackA3(data.getDouble("rekebackA3"));
					rekebackRule.setRekebackB1(data.getDouble("rekebackB1"));
					rekebackRule.setRekebackB2(data.getDouble("rekebackB2"));
					rekebackRule.setRekebackB3(data.getDouble("rekebackB3"));
		        	rekebackRule.setRekebackB4(data.getDouble("rekebackB4"));
		        	rekebackRule.setRekebackB5(data.getDouble("rekebackB5"));
		        	rekebackRule.setRekebackB6(data.getDouble("rekebackB6"));
		        	rekebackRule.setRekebackB7(data.getDouble("rekebackB7"));
		        	rekebackRule.setRekebackB8(data.getDouble("rekebackB8"));
		        	rekebackRule.setRekebackB9(data.getDouble("rekebackB9"));
		        	rekebackRule.setRekebackB10(data.getDouble("rekebackB10"));
		        	rekebackRule.setRekebackB11(data.getDouble("rekebackB11"));
		        	rekebackRule.setRekebackB12(data.getDouble("rekebackB12"));
		        	rekebackRule.setRekebackB13(data.getDouble("rekebackB13"));
		        	rekebackRule.setRekebackB14(data.getDouble("rekebackB14"));
		        	rekebackRule.setRekebackB15(data.getDouble("rekebackB15"));
		        	rekebackRule.setRekebackB16(data.getDouble("rekebackB16"));
		        	rekebackRule.setRekebackB17(data.getDouble("rekebackB17"));
		        	rekebackRule.setRekebackB18(data.getDouble("rekebackB18"));
		        	rekebackRule.setRekebackB19(data.getDouble("rekebackB19"));
		        	rekebackRule.setRekebackB20(data.getDouble("rekebackB20"));
		        	rekebackRule.setRekebackB21(data.getDouble("rekebackB21"));
		        	rekebackRule.setRekebackB22(data.getDouble("rekebackB22"));
		        	rekebackRule.setRekebackB23(data.getDouble("rekebackB23"));
		        	rekebackRule.setRekebackB24(data.getDouble("rekebackB24"));
		        	rekebackRule.setRekebackB25(data.getDouble("rekebackB25"));
		        	rekebackRule.setRekebackB26(data.getDouble("rekebackB26"));
		        	rekebackRule.setRekebackB27(data.getDouble("rekebackB27"));
		        	rekebackRule.setRekebackB28(data.getDouble("rekebackB28"));
		        	rekebackRule.setRekebackB29(data.getDouble("rekebackB29"));
		        	rekebackRule.setRekebackB30(data.getDouble("rekebackB30"));
		        	rekebackRule.setBackup5(data.getInteger("backup5"));
					rekebackRule.setRemark(data.getString("remark"));
					Result re = rekebackRuleService.saveOrUpdate(rekebackRule);
					
					
					RuleProdMap rpm_query=new RuleProdMap();
					rpm_query.setRuleId(rekebackRule.getId());
					Page<RuleProdMap> rpm_p=this.ruleProdMapService.findAll(0,10000,"id", "asc", rpm_query);
					for(int m=0;m<rpm_p.getContent().size();m++) {
						RuleProdMap alm=(RuleProdMap)rpm_p.getContent().get(m);
						this.ruleProdMapService.removeEntityById(alm.getId());
					}
					Object[] o1 = data.getJSONArray("tradeProds").toArray();
					for(int i=0;i<o1.length;i++) {
						RuleProdMap rpm=new RuleProdMap();
						rpm.setRuleId(rekebackRule.getId());
						rpm.setProdId(new Long(o1[i].toString()));
						this.ruleProdMapService.saveOrUpdate(rpm);
					}
					
					
					RuleGroupMap rgm_query=new RuleGroupMap();
					rgm_query.setRuleId(rekebackRule.getId());
					Page<RuleGroupMap> rgm_p=this.ruleGroupMapService.findAll(0,10000,"id", "asc", rgm_query);
					for(int m=0;m<rgm_p.getContent().size();m++) {
						RuleGroupMap alm=(RuleGroupMap)rgm_p.getContent().get(m);
						this.ruleGroupMapService.removeEntityById(alm.getId());
					}
					
					Object[] o2 = data.getJSONArray("userGroups").toArray();
					for(int i=0;i<o2.length;i++) {
						
						RuleGroupMap rgm=new RuleGroupMap();
						rgm.setRuleId(rekebackRule.getId());
						rgm.setGroupId(new Long(o2[i].toString()));
						this.ruleGroupMapService.saveOrUpdate(rgm);
					}
					
					
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				
				
                Long ruleID=-98765L;
				
				RekebackRule rekebackRule = this.rekebackRuleService.findById(id);
				if(rekebackRule!=null&&rekebackRule.getId()!=null) {
					RuleUserMap query  = new RuleUserMap();
					query.setRuleId(rekebackRule.getId()) ;
					Page<RuleUserMap> pages = ruleUserMapService.findAll(0, 10000, "id", "asc", query);
					
					for(int i=0;i<pages.getContent().size();i++) {
						this.ruleUserMapService.removeEntityById(pages.getContent().get(i).getId());
					}
					
				}
				
				
				Result result = rekebackRuleService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = rekebackRuleService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	@GetMapping( "/detail")
	public ResponseData detail(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				RekebackRule rekebackRule = this.rekebackRuleService.findById(id);
				
				RuleProdMap rpm_query=new RuleProdMap();
				rpm_query.setRuleId(rekebackRule.getId());
				Page<RuleProdMap> rpm_p=this.ruleProdMapService.findAll(0,10000,"id", "asc", rpm_query);
				
				
				RuleGroupMap rgm_query=new RuleGroupMap();
				rgm_query.setRuleId(rekebackRule.getId());
				Page<RuleGroupMap> rgm_p=this.ruleGroupMapService.findAll(0,10000,"id", "asc", rgm_query);
				
				rekebackRule.setProdList(rpm_p.getContent());
				rekebackRule.setGroupList(rgm_p.getContent());
				
				
		        	return ResponseDataUtil.buildSuccess(rekebackRule);
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}
	
	
	@PostMapping("/listshow")
    public ResponseData listshow(HttpServletRequest request) {
    	try {
        	RekebackRule query  = new RekebackRule();
        	Page<RekebackRule> pages = rekebackRuleService.findAll(0, 2000, "id", "asc", query);
        	List<RekebackRule> rekebackRules = pages.getContent();
        	JSONObject datas = new JSONObject();
        	datas.put("items", rekebackRules);
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


}


<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.crmAccount" :placeholder="$t('withdrawalAudit.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label12')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label1')" prop="crmAccount" min-width="190px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label2')" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label4')" prop="amount" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label3')" prop="backup5" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}  {{ scope.row.bankName }}   {{ scope.row.bankNum }}  {{ scope.row.bankAddress }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('withdrawalAudit.label5')" prop="backup6" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup6 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('withdrawalAudit.label6')" prop="rateRmb" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rateRmb }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">{{ $t('withdrawalAudit.label10') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('withdrawalAudit.label1')" prop="userName">
          {{ temp.userName }}    ({{ temp.crmAccount }})
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label2')" prop="tradeId">
          {{ temp.tradeId }}
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label4')" prop="amount">
          {{ temp.amount }}
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label9')" prop="backup5">
          {{ temp.accountAmount }}
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label5')" prop="backup6">
          {{ temp.backup6 }}
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label3')" prop="bankName">

          {{ temp.mobile }}  {{ temp.bankName }}   {{ temp.bankNum }}  {{ temp.bankAddress }}
        </el-form-item>

        <el-form-item :label="$t('withdrawalAudit.label13')" prop="remark">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item v-if="temp.backup3!=2" :label="$t('withdrawalAudit.label14')" prop="withdrawBankId">
          <el-select v-model="temp.withdrawBankId" style="width: 380px;" clearable>
            <el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('withdrawalAudit.label8')" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()">{{ $t('withdrawalAudit.label10') }}</el-button>
        <el-button type="danger" @click="updateData2()">{{ $t('withdrawalAudit.label11') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchWithdrawAuditList, auditWithdrawFundInfo, fetchFundInfo, createFundInfo, updateFundInfo, updateIsAvailable, removeFundInfo, auditWithdrawFundInfo2 } from '@/api/fundInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'FundInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        userName: undefined,
        crmAccount: undefined,
        tradeId: undefined,
        depositBankId: undefined,
        withdrawBankId: undefined,
        auditStatus: undefined,
        operStatus: undefined,
        auditId: undefined,
        orderId: undefined,
        bankName: undefined,
        bankNum: undefined,
        accountName: undefined,
        mobile: undefined,
        bankAddress: undefined,
        backup4: 2
      },
      temp: {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: '',
        backup5: 'Withdrawal'
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      bankList: [],
      rules: {
        userId: [
        ],
        userName: [
        ],
        crmAccount: [
        ],
        tradeId: [
        ],
        amount: [
        ],
        depositBankId: [
        ],
        withdrawBankId: [

        ],
        type: [
        ],
        allocationAmount: [
        ],
        auditStatus: [
        ],
        operStatus: [
        ],
        auditId: [
        ],
        orderId: [
        ],
        bankName: [
        ],
        bankNum: [
        ],
        accountName: [
        ],
        mobile: [
        ],
        bankAddress: [
        ],
        remark: [
        ],
        annex: [
        ],
        fee: [
        ],
        actualAmount: [
        ],
        originalAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ],
        rate: [
        ],
        rateRmb: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchWithdrawAuditList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          auditWithdrawFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData2() {
      auditWithdrawFundInfo2(this.temp).then(result => {
        if (result.code == 20000) {
          this.$notify({
            title: 'success',
            message: 'oper success',	type: 'success',
            duration: 2000
          })
          this.getList()
          this.dialogFormEditVisible = false
        } else {
          this.$message.error(response.msg)
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.backup5 = 'Withdrawal'
      this.bankList = []
      var datas3 = row.bankList
                	for (var j = 0; j < datas3.length; j++) {
                		var id = datas3[j].id
                		var name = datas3[j].bankName + ' ' + datas3[j].bankAccount + ' ' + datas3[j].accountName
                		var per = []
                		per.value = id
                		per.label = name
        this.bankList.push(per)
      }

      var per_ = []
      per_.value = -999
      per_.label = 'Offline payment'
      this.bankList.push(per_)

      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeFundInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

package com.ews.system.service;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Component;

import com.ews.common.LoginResult;

@Component
public interface LoginService {
	 LoginResult login(String userName, String password);
	 /**
	  * 获取当前用户的登录名
	  * @return
	  */
	    String getCurrentUserName();
	    void logout();
	   /**
	     * 前台登录方法
	     * @param userName
	     * @param password
	     * @return
	     */
	LoginResult frontLogin(String userName,String password,HttpServletRequest request);
}

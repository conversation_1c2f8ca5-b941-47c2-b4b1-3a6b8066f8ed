import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/tradeProd/list',
    method: 'post',
    params: query
  })
}

export function fetchTradeProd(id) {
  return request({
    url: '/tradeProd/detail',
    method: 'get',
    params: { id }
  })
}

export function createTradeProd(data) {
  return request({
    url: '/tradeProd/add',
    method: 'post',
    data
  })
}

export function updateTradeProd(data) {
  return request({
    url: '/tradeProd/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/tradeProd/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeTradeProd(id) {
  return request({
    url: '/tradeProd/remove',
    method: 'get',
    params: { id }
  })
}

export function synchroProd() {
  return request({
    url: '/tradeProd/synchroProd',
    method: 'get'
  })
}

export function listshowtradeProd() {
  return request({
    url: '/tradeProd/listshow',
    method: 'post'
  })
}

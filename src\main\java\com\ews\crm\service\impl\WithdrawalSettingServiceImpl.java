package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.WithdrawalSetting;
import com.ews.crm.repository.WithdrawalSettingRepository;
import com.ews.crm.service.WithdrawalSettingService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class WithdrawalSettingServiceImpl implements WithdrawalSettingService 
{
	@Autowired
	private WithdrawalSettingRepository withdrawalSettingRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<WithdrawalSetting> findAll(Integer page, Integer size,String sortName,String sortOrder, WithdrawalSetting withdrawalSetting) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<WithdrawalSetting> pages = withdrawalSettingRepository.findAll(new Specification<WithdrawalSetting>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<WithdrawalSetting> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public WithdrawalSetting findById(Long id) {
      Optional<WithdrawalSetting> op = withdrawalSettingRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.withdrawalSettingRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         WithdrawalSetting old = withdrawalSettingRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 withdrawalSettingRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 withdrawalSettingRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.withdrawalSettingRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(WithdrawalSetting withdrawalSetting) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (withdrawalSetting.getId()== null) {
            	withdrawalSetting.setGmtCreate(new Date());
            	withdrawalSetting.setGmtModified(new Date());
            	withdrawalSetting.setIsDeleted(0);
            	if(withdrawalSetting.getIsAvailable() == null) {
            		withdrawalSetting.setIsAvailable(1);
            	}
            	withdrawalSetting.setUserCreate(loginUser.getUserId());
	    	} else {
            	withdrawalSetting.setGmtModified(new Date());
        	}
            withdrawalSettingRepository.save(withdrawalSetting);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



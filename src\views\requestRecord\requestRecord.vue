<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.orderId" placeholder="订单ID" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.signStr" placeholder="sign" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.account" placeholder="账号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.status" placeholder="状态" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.thirdNo" placeholder="第三方流水号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.despoitId" placeholder="入金ID" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="订单ID" prop="orderId">
          <el-input v-model="temp.orderId" />
        </el-form-item>
        <el-form-item label="sign" prop="signStr">
          <el-input v-model="temp.signStr" />
        </el-form-item>
        <el-form-item label="账号" prop="account">
          <el-input v-model="temp.account" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-input v-model="temp.status" />
        </el-form-item>
        <el-form-item label="第三方流水号" prop="thirdNo">
          <el-input v-model="temp.thirdNo" />
        </el-form-item>
        <el-form-item label="入金ID" prop="despoitId">
          <el-input v-model="temp.despoitId" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="订单ID" prop="orderId">
          <el-input v-model="temp.orderId" />
        </el-form-item>
        <el-form-item label="sign" prop="signStr">
          <el-input v-model="temp.signStr" />
        </el-form-item>
        <el-form-item label="账号" prop="account">
          <el-input v-model="temp.account" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-input v-model="temp.status" />
        </el-form-item>
        <el-form-item label="第三方流水号" prop="thirdNo">
          <el-input v-model="temp.thirdNo" />
        </el-form-item>
        <el-form-item label="入金ID" prop="despoitId">
          <el-input v-model="temp.despoitId" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchRequestRecord, createRequestRecord, updateRequestRecord, updateIsAvailable, removeRequestRecord } from '@/api/requestRecord'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'RequestRecordTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        orderId: undefined,
        signStr: undefined,
        account: undefined,
        status: undefined,
        thirdNo: undefined,
        despoitId: undefined
      },
      temp: {
        id: undefined,
        orderId: '',
        signStr: '',
        account: '',
        status: '',
        thirdNo: '',
        despoitId: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        orderId: [
        ],
        signStr: [
        ],
        amount: [
        ],
        account: [
        ],
        status: [
        ],
        thirdNo: [
        ],
        recallStr: [
        ],
        despoitId: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        orderId: '',
        signStr: '',
        account: '',
        status: '',
        thirdNo: '',
        despoitId: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createRequestRecord(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateRequestRecord(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeRequestRecord(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/exchangeRate/list',
    method: 'post',
    params: query
  })
}

export function fetchExchangeRate(id) {
  return request({
    url: '/exchangeRate/detail',
    method: 'get',
    params: { id }
  })
}

export function createExchangeRate(data) {
  return request({
    url: '/exchangeRate/add',
    method: 'post',
    data
  })
}

export function updateExchangeRate(data) {
  return request({
    url: '/exchangeRate/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/exchangeRate/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeExchangeRate(id) {
  return request({
    url: '/exchangeRate/remove',
    method: 'get',
    params: { id }
  })
}


import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/reckbackInfo/list',
    method: 'post',
    params: query
  })
}

export function fetchReckbackInfo(id) {
  return request({
    url: '/reckbackInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createReckbackInfo(data) {
  return request({
    url: '/reckbackInfo/add',
    method: 'post',
    data
  })
}

export function updateReckbackInfo(data) {
  return request({
    url: '/reckbackInfo/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/reckbackInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeReckbackInfo(id) {
  return request({
    url: '/reckbackInfo/remove',
    method: 'get',
    params: { id }
  })
}

export function exportReckbackInfoExcel(query) {
  return request({
    url: '/reckbackInfo/exportReckbackInfoExcel',
    method: 'post',
    params: query
  })
}

package com.ews.common;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.HttpClient;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserGroup;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserGroupService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.service.UserService;
import com.wx.core.util.HttpClientUtil;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@EnableAsync 
public class SaticScheduleTask {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private UserService userService;
	@Autowired
	private ReckbackInfoService reckbackInfoService;
	
	
	@Autowired
	private UserGroupService userGroupService;

	
	
	//是否开启自动轮询功能 true 开启   false  关闭
	/**/
	private boolean bl1=true;   //用户，
	private boolean bl8=true;   //余额                
	private boolean bl4=false;   //查询持仓                    
	private boolean bl5=true;   //查询历史订单
	private boolean mainSwitch=true;    //总开关
	
   
	//@Async
    @Scheduled(fixedRate=300000L)//每5分钟查询一次
	public void configureTasks() {
    	if(bl1&&mainSwitch) {
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
		
			UserGroup query  = new UserGroup();
			Page<UserGroup> pages = userGroupService.findAll(0,10000, "id", "asc", query);
			List<UserGroup> userGroups = pages.getContent();
		    HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
		    
		    boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
			for(int i=0;i<userGroups.size();i++) {
        	     UserGroup entity  = userGroups.get(i);
        	     
        	     
        	     if(blll) {
        	    	  org.json.JSONObject json=httpClient.getUserInfoByGroup(entity.getGroupName());
        	    	  
        	    	   if(json.getString("retcode").equals("0 Done")) {
        	    		 //  System.out.println("同步组用户："+json);
					    	  org.json.JSONArray ja=json.getJSONArray("answer");
					    	  
					    	  for(int m=0;m<ja.length();m++) {
					    		    TradeAccount ta_query=new TradeAccount();
					    		    org.json.JSONObject joo_d = ( org.json.JSONObject)ja.get(m);
									ta_query.setTradeId(joo_d.getString("Login"));
								    Page<TradeAccount> page=this.tradeAccountService.findAll(0,1,"id","asc", ta_query);
									if(page.getContent().size()<=0) {
										TradeAccount ta=new TradeAccount();
										ta.setTradeId(joo_d.getString("Login"));
										ta.setGroupName(joo_d.getString("Group"));
										ta.setLeverShow(joo_d.getString("Leverage"));
										ta.setTradeStatus(1);
										ta.setGmtCreate(new Date());  
										this.tradeAccountService.saveOrUpdate(ta);
									}else {
										TradeAccount ta=(TradeAccount)page.getContent().get(0);
										ta.setGroupName(joo_d.getString("Group"));
										ta.setLeverShow(joo_d.getString("Leverage"));
										this.tradeAccountService.saveOrUpdate(ta);
										
		        	                }
					    	  }
        	    	   }
        	     }
			}
			
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
   
	
	
	//查询用户余额   每3分钟查询一次
   // @Async
    @Scheduled(fixedRate=60000*3L)  
    public void configureTasks2() {
    	if(bl1&&mainSwitch) {
    
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    	try {
    		
			UserGroup query  = new UserGroup();
			Page<UserGroup> pages = userGroupService.findAll(0,10000, "id", "asc", query);
			List<UserGroup> userGroups = pages.getContent();
		    HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
		    
		    boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
			for(int i=0;i<userGroups.size();i++) {
        	     UserGroup entity  = userGroups.get(i);
        	     
        	     
        	     if(blll) {
        	    	  org.json.JSONObject json=httpClient.getTradeInfoByGroup(entity.getGroupName());
        	    	  
        	    	   if(json.getString("retcode").equals("0 Done")) {
					    	  org.json.JSONArray ja=json.getJSONArray("answer");
					    	  
					    	  for(int m=0;m<ja.length();m++) {
					    		    TradeAccount ta_query=new TradeAccount();
					    		    org.json.JSONObject joo_d = ( org.json.JSONObject)ja.get(m);
									ta_query.setTradeId(joo_d.getString("Login"));
								    Page<TradeAccount> page=this.tradeAccountService.findAll(0,1,"id","asc", ta_query);
									if(page.getContent().size()>0) {
										TradeAccount ta=(TradeAccount)page.getContent().get(0);
										ta.setBalance1(joo_d.getDouble("Balance"));
										ta.setBalance2(joo_d.getDouble("Credit"));
										ta.setBalance3(joo_d.getDouble("Equity"));
										ta.setBalance4(joo_d.getDouble("MarginFree"));
										this.tradeAccountService.saveOrUpdate(ta);
									}else {
		        	                }
					    	  }
        	    	   }
        	     }
			}
			
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
   
    //查询历史   每15分钟查询一次，延迟4分钟
   // @Async
    @Scheduled(fixedRate=60000*15L,initialDelay=240000)  
    public void configureTasks3() {
    	if(bl5&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,8000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    	try {
    		
    		   HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
   		    
   		      boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
   		    
   		    
    	
			for(int i=0;i<pages2.getContent().size();i++) {
	    		 TradeAccount entity  = pages2.getContent().get(i);

				  // 判断当前时间和修改时间比较，如果超过24小时则执行解绑-BEGIN
				  Date currentTime = new Date();
				  Date modifiedTime = entity.getGmtModified();
				  
				  if(modifiedTime != null) {
					  long timeDifferenceInMillis = currentTime.getTime() - modifiedTime.getTime();
					  long twentyFourHoursInMillis = 24 * 60 * 60 * 1000L; // 24小时的毫秒数
					  
					  if(timeDifferenceInMillis > twentyFourHoursInMillis) {
						  entity.setUserId(null);
						  entity.setUserAccount("");
						  entity.setIsAvailable(-2);
						  entity.setBalance1(0d);
						  entity.setBalance2(0d);
						  entity.setBalance3(0d);
						  entity.setBalance4(0d);
						  entity.setBalance5(0d);
						  entity.setBalance6(0d);
						  entity.setBalance7(0d);
						  this.tradeAccountService.saveOrUpdate(entity);
						 
					  }
				  }
				   // 判断当前时间和修改时间比较，如果超过24小时则执行解绑-END
	    		
	    		OrderInfo order_query=new OrderInfo();
	 			order_query.setLoginId(String.valueOf(entity.getTradeId()));
	 			order_query.setStatus(2);
	 			List ll=new ArrayList();
	 	    	ll.add(1);
	 	    	ll.add(0);
	 	    	ll.add(2);
	 	    	ll.add(3);
	 	    	ll.add(4);
	 	    	ll.add(5);
	 	    	ll.add(6);
	 	    	order_query.setTypeList(ll);
	 			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,1, "closeTime","desc", order_query);
	 			
	 			Long from=new Date().getTime();
	 			if(oi_page.getContent().size()>0) {//有记录
	 				from= new Long((oi_page.getContent().get(0).getCloseTime()));
	 			}else {//没有交易信息
	 				from=(new Date().getTime()/1000)-86400L;
	 			}
	 		
	 			if(blll) {
	 			   org.json.JSONObject json=httpClient.getHistoryOrder(entity.getTradeId(),String.valueOf(from),String.valueOf(((new Date().getTime()/1000)+86400L)));
	 			  if(json.getString("retcode").equals("0 Done"))  {
	 				  org.json.JSONArray ja=json.getJSONArray("answer");
			    	  for(int m=0;m<ja.length();m++) {
			    		  
			    		    org.json.JSONObject joo_d = (org.json.JSONObject)ja.get(m);
			    		    OrderInfo order_query2=new OrderInfo();
			    		    order_query2.setOrderNo(joo_d.getString("Order"));
							Page<OrderInfo> oi_page2=this.orderInfoService.findAll(0,1, "id","asc", order_query2);
							if(oi_page2.getContent().size()>0) {//已存在
								
							}else {//未存在
								
								if(joo_d.getString("Entry").equals("1")) {
									OrderInfo order_info=new OrderInfo();
									order_info.setOrderNo(joo_d.getString("Order"));
									order_info.setOpenTime(joo_d.getInt("Time"));
									order_info.setOpenPrice(joo_d.getDouble("Price"));
			                        order_info.setType(joo_d.getInt("Action"));
			                        order_info.setVolume(joo_d.getDouble("Volume")/10000);
			                        order_info.setSymbol(joo_d.getString("Symbol"));
			                        order_info.setStorage(joo_d.getDouble("Storage"));
			                        order_info.setProfit(joo_d.getDouble("Profit"));
			                        order_info.setStatus(2);
			                        order_info.setIsRakeback(0);
			                        order_info.setLoginId(joo_d.getString("Login"));
			                        
			                        order_info.setCloseTime(joo_d.getInt("Time"));
			                        order_info.setClosePrice(joo_d.getDouble("Price"));
			                        
			                        
			                        TradeAccount query3=new TradeAccount();
			                        query3.setTradeId(entity.getTradeId());
			    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query3);
			    					if(ta_page.getContent().size()>0) {
			    						order_info.setTradeId(ta_page.getContent().get(0).getId());
			    					}
			                        
			                        this.orderInfoService.saveOrUpdate(order_info);
								}
								
								
		                        
							}
			    		  
			    	  }
                 }
	 			}
	 			
	 			
	 			
	    	
		   }
  		}catch(Exception e) {
  			System.out.println(e);
  		}
    	}
    }
    
    
}
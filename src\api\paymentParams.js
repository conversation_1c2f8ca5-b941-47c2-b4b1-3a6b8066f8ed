import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/paymentParams/list',
    method: 'post',
    params: query
  })
}

export function fetchPaymentParams(id) {
  return request({
    url: '/paymentParams/detail',
    method: 'get',
    params: { id }
  })
}

export function createPaymentParams(data) {
  return request({
    url: '/paymentParams/add',
    method: 'post',
    data
  })
}

export function updatePaymentParams(data) {
  return request({
    url: '/paymentParams/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/paymentParams/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removePaymentParams(id) {
  return request({
    url: '/paymentParams/remove',
    method: 'get',
    params: { id }
  })
}


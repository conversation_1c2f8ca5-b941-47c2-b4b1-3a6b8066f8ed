package com.ews.system.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import com.ews.system.entity.RolePermission;

public interface RolePermissionRepository extends  JpaRepository<RolePermission, Long> {
	/**
	 * 通过roleId查询权限配置信息
	 * @param roleId
	 * @return
	 */
	public List<RolePermission> findByRoleId(Long roleId);
	
	/**
	 * 通过roleid和permissionid查询权限配置信息
	 * @param roleId
	 * @param permissionId
	 * @return
	 */
	public List<RolePermission> findByRoleIdAndPermissionId(Long roleId,Long permissionId);
	
	/**
	 * 通过权限id删除数据
	 * @param roleId
	 */
	@Modifying
	@Transactional
	public void removeByRoleId(Long roleId);
}

<template>
  <div class="app-container">

    <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="160px" style="max-width: 600px; margin-left:10px;">

      <el-form-item :label="$t('withdrawalSetting.label1')" prop="noticeEmail">
        <el-select v-model="listBackup1" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
          <el-option
            v-for="item in option1s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('withdrawalSetting.label2')" prop="noticeMobile">
        <el-select v-model="listBackup2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
          <el-option
            v-for="item in option2s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('withdrawalSetting.label3')" prop="backup1">
        <el-select v-model="listBackup3" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
          <el-option
            v-for="item in option3s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('withdrawalSetting.label4')" prop="backup2">
        <el-input v-model="temp.backup2" placeholder="Withdrawal amount <?" />
      </el-form-item>

      <el-form-item :label="$t('withdrawalSetting.label5')" prop="backup3">
        <el-input v-model="temp.backup3" placeholder="equity -credit >?" />
      </el-form-item>

      <el-form-item :label="$t('withdrawalSetting.label6')" prop="backup4">
        <el-input v-model="temp.backup4" placeholder="Withdrawal amount <(free margin *?%-credit)" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">

      <el-button type="primary" @click="updateData()">{{ $t('table.save') }}</el-button>
    </div>
  </div>
</template>
<script>
import { fetchCountriesList } from '@/api/countries'
import { fetchALLList } from '@/api/userTag'
import { fetchAccountTypeList } from '@/api/accountType'
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchWithdrawalSetting, createWithdrawalSetting, updateWithdrawalSetting, updateIsAvailable, removeWithdrawalSetting } from '@/api/withdrawalSetting'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'WithdrawalSettingTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      }, listQuery2: {
			 		page: 1,
        limit: 1000

      },
      option3s: [],
      listBackup3: [],
      option2s: [],
      listBackup2: [],
      option1s: [],
      listBackup1: [],
      temp: {
        id: undefined,
        withdrawalMin: '',
        withdrawalMax: '',
        feeType: '',
        feeProportion: '',
        feeMin: '',
        feeMax: '',
        feeAmount: '',
        noticeType: '',
        noticeEmail: '',
        noticeMobile: '',
        backup1: '',
        backup2: '',
        backup3: '',
        backup4: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 feeTypes: [
        {
          value: 1,
          label: 'a'
        },
        {
          value: 2,
          label: 'b'
        }
      ],
				 noticeTypes: [
        {
          value: 1,
          label: 'c'
        },
        {
          value: 2,
          label: 'd'
        }
      ],
      rules: {
        withdrawalMin: [,
        ],
        withdrawalMax: [,
        ],
        feeType: [,
        ],
        feeProportion: [
        ],
        feeMin: [
        ],
        feeMax: [
        ],
        feeAmount: [
        ],
        noticeType: [
        ],
        noticeEmail: [
        ],
        noticeMobile: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.option3s = []
    this.option2s = []
    this.option1s = []
    fetchALLList(this.listQuery2).then(response => {
      var datas4 = response.data.items
           	for (var j = 0; j < datas4.length; j++) {
           		var id = '"' + datas4[j].id + '"'
           		var name = datas4[j].tagName
           		var per = []
           		per.value = id + ''
           		per.label = name
        this.option3s.push(per)
      }
    })

    fetchCountriesList(this.listQuery2).then(response => {
      var datas4 = response.data.items
           	for (var j = 0; j < datas4.length; j++) {
           		var id = '"' + datas4[j].countryCodeThree + '"'
           		var name = datas4[j].countryName
           		var per = []
           		per.value = id + ''
           		per.label = name
        this.option1s.push(per)
      }
    })

    fetchAccountTypeList(this.listQuery2).then(response => {
      var datas4 = response.data.items
           	for (var j = 0; j < datas4.length; j++) {
           		var id = '"' + datas4[j].id + '"'
           		var name = datas4[j].typeName
           		var per = []
           		per.value = id + ''
           		per.label = name
        this.option2s.push(per)
      }
    })

	   fetchWithdrawalSetting(1).then(res => {
			  this.temp = Object.assign({}, res.data)

      this.listBackup3 = []
      var datas3 = eval(this.temp.backup1)

      for (var m = 0; m < datas3.length; m++) {
        this.listBackup3.push('"' + datas3[m] + '"')
      }

      this.listBackup2 = []
      var datas2 = eval(this.temp.noticeMobile)

      for (var m = 0; m < datas2.length; m++) {
        this.listBackup2.push('"' + datas2[m] + '"')
      }

      this.listBackup1 = []
      var datas1 = eval(this.temp.noticeEmail)

      for (var m = 0; m < datas1.length; m++) {
        this.listBackup1.push('"' + datas1[m] + '"')
      }
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        withdrawalMin: '',
        withdrawalMax: '',
        feeType: '',
        feeProportion: '',
        feeMin: '',
        feeMax: '',
        feeAmount: '',
        noticeType: '',
        noticeEmail: '',
        noticeMobile: '',
        backup1: '',
        backup2: '',
        backup3: '',
        backup4: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createWithdrawalSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.noticeEmail = this.listBackup1
          this.temp.noticeMobile = this.listBackup2
          this.temp.backup1 = this.listBackup3
          updateWithdrawalSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeWithdrawalSetting(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

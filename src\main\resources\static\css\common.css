@charset "utf-8";
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    font-size: 625%;
}
article,
aside,
blockquote,
body,
button,
code,
dd,
details,
div,
dl,
dt,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
hr,
input,
legend,
li,
menu,
nav,
ol,
p,
pre,
section,
td,
textarea,
th,
ul {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    vertical-align: baseline;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block;
}
li,
ol,
ul {
    list-style: none;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
}
address,
b,
cite,
code,
em,
i,
small,
strong {
    font-style: normal;
}
button,
input[type=button],
input[type=reset],
input[type=submit] {
    -webkit-appearance: button;
    -webkit-user-select: none;
    cursor: pointer;
}
button[disabled],
input[disabled] {
    cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0 none;
    padding: 0;
}
input[type=checkbox],
input[type=radio] {
    -webkik-box-sizing: border-box;
    box-sizing: border-box;
}
input[type=search] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}
input:-webkit-autofill,
select:-webkit-autofill,
textarea:-webkit-autofill {
    box-shadow: 0 0 0 999px #fff inset;
}
input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none
}
textarea {
    overflow: auto;
    vertical-align: top;
    resize: vertical;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
caption,
td,
th {
    vertical-align: middle;
}
blockquote,
q {
    quotes: none;
}
blockquote:after,
blockquote:before,
q:after,
q:before {
    content: "";
    content: none;
}
a img {
    border: none;
}
img {
    display: block;
}
a {
    color: #000;
    text-decoration: none;
    font-size: 100%;
    vertical-align: baseline;
}
a:active,
button:focus,
input:focus,
textarea:focus {
    outline: 0 none;
}
abbr[title],
dfn[title] {
    border-bottom: 1px dotted;
    cursor: help;
}
body {
    min-width: 320px;
    max-width: 960px;
    background-color: #fff;
    color: #000;
    font: .14rem/1.5 microsoft yahei, helvetica neue, tahoma, arial, hiragino sans gb, sans-serif;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased;
	padding-bottom:.50rem;
}
button,
textarea,
input,
select{
    -webkit-appearance:none;
    appearance:none;
}
input,
select {
    vertical-align: middle;
}
button {
    outline: 0;
    border: none;
    background-color: transparent;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    color: #bfbfbf;
}
del {
    text-decoration: line-through
}
input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none!important;
    margin: 0
}
.clearfix:after {
    content: "";
    display: table;
    font: 0/0 "";
    clear: both;
}
.header{
	
	width:100%;
	height:.44rem;
	background:#fd6e3b;
	position:fixed;
	margin-bottom:.44rem;
	z-index:2;
}
.header-back{
	
	padding-left:5%;
	padding-top:.15rem;
	display:block;
	float:left;
}
.header-back a{
	
	display:block;
}
.header-back img{
	
	width:.14rem;
	height:.14rem;
}
.header-title{
	
	float:left;
	margin-left:.30rem;
	height:.33rem;
	margin-top:.05rem;
	border-left:1px solid #b3c4ca;
	line-height:.33rem;
	color:#fff;
	font-size:.21rem;
	padding-left:.10rem;
	letter-spacing:.03rem;
}
.header-list{
	
	padding-right:5%;
	padding-top:.12rem;
	display:block;
	float:right;
}
.header-list a{
	
	display:block;
}
.header-list img{
	
	width:.06rem;
	height:.20rem;
}
.notice{
	
	width:90%;
	height:.40rem;
	margin:0 auto;
	line-height:.40rem;
	font-size:.16rem;
	color:#000;
	padding-top:.44rem;
}

.menu{

    position: fixed;
    bottom: 0;
    width: 100%;
    height: .50rem;
	background:#fff;
}
.menu-nav{

	width:100%;
}
.menu-nav_item{

    text-align: center;
	float:left;
	width:25%;
}
.menu-nav_item a{

    display: block;
    padding-top: .20rem;
    font-size: .12rem;
    color: #3b3b3b;
	letter-spacing:.03rem;
}
.menu-nav_item .active{

    color: #f93526;
}
.menu_1{

    background: url(../images/menu_1.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
	border-top:.09rem solid #fd331e;  
}
.menu_2{

    background: url(../images/menu_2.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
	border-top:.09rem solid #fdb904;   
}
.menu_3{

    background: url(../images/menu_3.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
	border-top:.09rem solid #2e86f4; 
}
.menu_4{

    background: url(../images/menu_4.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
	border-top:.09rem solid #29b14b; 
}
.select1{

    background: url(../images/menu_01.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
    
}
.select2{

    background: url(../images/menu_02.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
}
.select3{

    background: url(../images/menu_03.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
}
.select4{

    background: url(../images/menu_04.png) no-repeat center .02rem;
    background-size: .20rem .20rem; 
}

.recharge-head{

    overflow: hidden;
    height: .43rem;
    background: #fff;
    border-bottom: .05rem solid #f7f7f7;
    position: fixed;
    display: block;
    width: 100%;
    top: 0;
    text-align: center;
}
.back{

    position: absolute;
    left: .20rem;
	padding-top:.15rem;
}
.back img{

    width:.08rem;
	height:.13rem;
}
.index-banner{
	
	width:100%;
	height:auto;
}
.index-banner img{
	
	width:100%;
	height:auto;
}
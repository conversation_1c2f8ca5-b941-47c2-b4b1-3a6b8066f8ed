import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/webContent/list',
    method: 'post',
    params: query
  })
}

export function fetchWebContent(id) {
  return request({
    url: '/webContent/detail',
    method: 'get',
    params: { id }
  })
}

export function createWebContent(data) {
  return request({
    url: '/webContent/add',
    method: 'post',
    data
  })
}

export function updateWebContent(data) {
  return request({
    url: '/webContent/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/webContent/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeWebContent(id) {
  return request({
    url: '/webContent/remove',
    method: 'get',
    params: { id }
  })
}


package com.ews.crm.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.ews.common.Result;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.repository.OrderInfoRepository;
import com.ews.crm.service.OrderInfoService;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class OrderInfoServiceImpl implements OrderInfoService 
{
	@Autowired
	private OrderInfoRepository orderInfoRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<OrderInfo> findAll(Integer page, Integer size,String sortName,String sortOrder, OrderInfo orderInfo) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<OrderInfo> pages = orderInfoRepository.findAll(new Specification<OrderInfo>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<OrderInfo> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(orderInfo.getOrderNo())) { 
                predicates.add(criteriaBuilder.equal(root.get("orderNo").as(String.class),orderInfo.getOrderNo()));
             }
             if(!StringUtils.isEmpty(orderInfo.getType())) { 
                predicates.add(criteriaBuilder.equal(root.get("type").as(Integer.class), orderInfo.getType()));
             }
             if(!StringUtils.isEmpty(orderInfo.getSymbol())) { 
                predicates.add(criteriaBuilder.like(root.get("symbol").as(String.class),"%"+orderInfo.getSymbol()+"%"));
             }
             if(!StringUtils.isEmpty(orderInfo.getStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("status").as(Integer.class), orderInfo.getStatus()));
             }
             if(!StringUtils.isEmpty(orderInfo.getQueryBeginTime())) { 
                 predicates.add(criteriaBuilder.ge(root.get("closeTime").as(Integer.class), orderInfo.getQueryBeginTime()));
              }
             if(!StringUtils.isEmpty(orderInfo.getQueryEndTime())) { 
                 predicates.add(criteriaBuilder.le(root.get("closeTime").as(Integer.class), orderInfo.getQueryEndTime()));
              }
             if(!StringUtils.isEmpty(orderInfo.getIsRakeback())) { 
                 predicates.add(criteriaBuilder.equal(root.get("isRakeback").as(Integer.class), orderInfo.getIsRakeback()));
              }
             if(!StringUtils.isEmpty(orderInfo.getTradeId())) { 
                predicates.add(criteriaBuilder.equal(root.get("tradeId").as(Long.class), orderInfo.getTradeId()));
             }
             if(!StringUtils.isEmpty(orderInfo.getLoginId())) { 
                predicates.add(criteriaBuilder.equal(root.get("loginId").as(String.class),orderInfo.getLoginId()));
             }

             if(orderInfo.getTradeLoginIdList()!=null&&orderInfo.getTradeLoginIdList().size()>0) {
                Path<Object> path = root.get("loginId");
                CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
                for(int i=0;i<orderInfo.getTradeLoginIdList().size();i++) {
                    in.value(orderInfo.getTradeLoginIdList().get(i));
                }
                predicates.add(criteriaBuilder.and(in));
             }
            
             
             if(orderInfo.getTradeList()!=null&&orderInfo.getTradeList().size()>0)
             {
            	    Path<Object> path = root.get("tradeId");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					
					for(int i=0;i<orderInfo.getTradeList().size();i++) {
						TradeAccount ta=(TradeAccount)orderInfo.getTradeList().get(i);
						  in.value(ta.getId());
					}
					predicates.add(criteriaBuilder.and(in));
             }
             
             if(orderInfo.getTypeList()!=null&&orderInfo.getTypeList().size()>0)
             {
            	    Path<Object> path = root.get("type");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					for(int i=0;i<orderInfo.getTypeList().size();i++) {
						  in.value(new Integer(orderInfo.getTypeList().get(i).toString()));
					}
					predicates.add(criteriaBuilder.and(in));
             }
             
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public OrderInfo findById(Long id) {
      Optional<OrderInfo> op = orderInfoRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.orderInfoRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         OrderInfo old = orderInfoRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 orderInfoRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 orderInfoRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.orderInfoRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(OrderInfo orderInfo) {
      
        Result vr = new Result();
		try {
        	if (orderInfo.getId()== null) {
            	orderInfo.setGmtCreate(new Date());
            	orderInfo.setGmtModified(new Date());
            	orderInfo.setIsDeleted(0);
            	if(orderInfo.getIsAvailable() == null) {
            		orderInfo.setIsAvailable(1);
            	}
            	//orderInfo.setUserCreate(loginUser.getUserId());
	    	} else {
            	orderInfo.setGmtModified(new Date());
        	}
            orderInfoRepository.save(orderInfo);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



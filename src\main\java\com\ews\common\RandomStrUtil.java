package com.ews.common;

import java.util.Random;

public class RandomStrUtil {
	 /**
	  * 生成随机字符串
	  * @param length 字符串长度
	  * @return
	  */
    public static String generate(int length) {  
    	 String val = "";  
         Random random = new Random();        
         //length为几位密码 
         for(int i = 0; i < length; i++) {          
             String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";  
             //输出字母还是数字  
             if( "char".equalsIgnoreCase(charOrNum) ) {  
                 //输出是大写字母还是小写字母  
                 int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;  
                 val += (char)(random.nextInt(26) + temp);  
             } else if( "num".equalsIgnoreCase(charOrNum) ) {  
                 val += String.valueOf(random.nextInt(10));  
             }  
         }  
         return val;  
    }  
    
    public static void main(String args[]) {
    	System.out.println(RandomStrUtil.generate(32));
    }

}

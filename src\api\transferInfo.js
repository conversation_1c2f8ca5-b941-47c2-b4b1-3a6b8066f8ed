import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/transferInfo/list',
    method: 'post',
    params: query
  })
}

export function fetchList2(query) {
  return request({
    url: '/transferInfo/list2',
    method: 'post',
    params: query
  })
}

export function fetchTransferInfo(id) {
  return request({
    url: '/transferInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createTransferInfo(data) {
  return request({
    url: '/transferInfo/add',
    method: 'post',
    data
  })
}

export function updateTransferInfo(data) {
  return request({
    url: '/transferInfo/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/transferInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}

export function auditTransferInfo(id, type) {
  return request({
    url: '/transferInfo/auditTransferInfo',
    method: 'get',
    params: { id, type }
  })
}

export function removeTransferInfo(id) {
  return request({
    url: '/transferInfo/remove',
    method: 'get',
    params: { id }
  })
}

export function exportExcel(query) {
  return request({
    url: '/transferInfo/exportExcel',
    method: 'post',
    params: query
  })
}


<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.crmAccount" placeholder="CRM账号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tradeId" placeholder="交易账号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.bankName" placeholder="开户行" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.bankNum" placeholder="银行卡号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.accountName" placeholder="开户姓名" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.mobile" placeholder="预留手机号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="CRM用户名称" prop="userName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="CRM账号" prop="crmAccount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易账号" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出金金额" prop="amount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>${{ scope.row.amount }}(￥{{ scope.row.rateRmb }})</span>
        </template>
      </el-table-column>
      <el-table-column label="开户行" prop="bankName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="银行卡号" prop="bankNum" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开户姓名" prop="accountName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.accountName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预留手机号" prop="mobile" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="CRM用户" prop="userName">
          {{ temp.userName }}    ({{ temp.crmAccount }})
        </el-form-item>
        <el-form-item label="交易账号" prop="tradeId">
          {{ temp.tradeId }}
        </el-form-item>
        <el-form-item label="出金金额" prop="amount">
          ${{ temp.amount }}(￥{{ temp.rateRmb }})
        </el-form-item>
        <el-form-item label="当前余额" prop="backup5">
          ${{ temp.accountAmount }}
        </el-form-item>
        <el-form-item label="收款开户行" prop="bankName">
          {{ temp.bankName }}
        </el-form-item>
        <el-form-item label="收款银行卡号" prop="bankNum">
          {{ temp.bankNum }}
        </el-form-item>
        <el-form-item label="收款开户姓名" prop="accountName">
          {{ temp.accountName }}
        </el-form-item>
        <el-form-item label="收款手机号" prop="mobile">
          {{ temp.mobile }}
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item label="付款款银行" prop="withdrawBankId">
          <el-select v-model="temp.withdrawBankId" style="width: 380px;" placeholder="付款款银行" clearable>
            <el-option v-for="item in bankList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核备注" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()">审核</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchWithdrawAuditList, auditWithdrawFundInfo, fetchFundInfo, createFundInfo, updateFundInfo, updateIsAvailable, removeFundInfo } from '@/api/fundInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'FundInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        userName: undefined,
        crmAccount: undefined,
        tradeId: undefined,
        depositBankId: undefined,
        withdrawBankId: undefined,
        auditStatus: undefined,
        operStatus: undefined,
        auditId: undefined,
        orderId: undefined,
        bankName: undefined,
        bankNum: undefined,
        accountName: undefined,
        mobile: undefined,
        bankAddress: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: '',
        backup5: '出金'
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      bankList: [],
      rules: {
        userId: [
        ],
        userName: [
        ],
        crmAccount: [
        ],
        tradeId: [
        ],
        amount: [
        ],
        depositBankId: [
        ],
        withdrawBankId: [
          { required: true, message: '付款银行不能为空', trigger: 'change' }
        ],
        type: [
        ],
        allocationAmount: [
        ],
        auditStatus: [
        ],
        operStatus: [
        ],
        auditId: [
        ],
        orderId: [
        ],
        bankName: [
        ],
        bankNum: [
        ],
        accountName: [
        ],
        mobile: [
        ],
        bankAddress: [
        ],
        remark: [
        ],
        annex: [
        ],
        fee: [
        ],
        actualAmount: [
        ],
        originalAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ],
        rate: [
        ],
        rateRmb: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchWithdrawAuditList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          auditWithdrawFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.backup5 = '出金'
      this.bankList = []
      var datas3 = row.bankList
                	for (var j = 0; j < datas3.length; j++) {
                		var id = datas3[j].id
                		var name = datas3[j].bankName + ' ' + datas3[j].bankAccount + ' ' + datas3[j].accountName
                		var per = []
                		per.value = id
                		per.label = name
        this.bankList.push(per)
      }

      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeFundInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

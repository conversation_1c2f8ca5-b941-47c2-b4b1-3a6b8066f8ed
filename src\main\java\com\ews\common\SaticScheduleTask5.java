package com.ews.common;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.controller.WebIndexController;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.entity.EmailInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.EmailInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.model.HttpMethod;
import com.fasterxml.jackson.databind.ObjectMapper;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@EnableAsync    //多线程
public class SaticScheduleTask5 {
	

    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
	private UserInfoService userInfoService;
    
    @Autowired
	private EmailInfoService emailInfoService;
	
	
	//@Async   //多线程
	@Scheduled(fixedRate=300000L)
	public void configureTasks777() {
		
		JSONObject json_userInfo=new JSONObject();
		
		UserInfo ui_query=new UserInfo();
		ui_query.setIsAvailable(2);
		Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1000,"id","asc", ui_query);
		
		for(int i=0;i<userInfo_page.getContent().size();i++) {
			
			UserInfo ui=(UserInfo)userInfo_page.getContent().get(i);
			long diff = 0;
			long nd = 1000 * 24 * 60 * 60 * 3;// 三天的毫秒数
			
			SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date now = new Date();
			Date start =ui.getGmtModified();
			
			diff = now.getTime() - start.getTime();
		      if (diff > nd) {
		    	  
		    	     ui.setIsAvailable(3);
	    			 this.userInfoService.saveOrUpdate(ui);
		      } else {
		    	  if(ui.getCountry().equals(ui.getProvince())) {
						
						try {
						    String applicantStatusStr = getApplicantStatus(ui.getBackup4());
						  
						     JSONObject json1=JSONObject.parseObject(applicantStatusStr);
						   
						     
						     if(json1.getString("IDENTITY")!=null&&json1.getString("SELFIE")!=null) {
						    	 
						    	 String reviewAnswer_IDENTITY="";
						    	 String reviewAnswer_SELFIE="";
						    	 
						    	 JSONObject json2=JSONObject.parseObject(json1.getString("IDENTITY"));
						    	 
						    	 if(json2.getString("reviewResult")!=null) {
						    		 JSONObject json3=JSONObject.parseObject(json2.getString("reviewResult"));
						    		 reviewAnswer_IDENTITY=json3.getString("reviewAnswer");
						    		 
						    		 json_userInfo.put("IDENTITY_reviewAnswer", reviewAnswer_IDENTITY);
						    	 }
						    	 
					            JSONObject json22=JSONObject.parseObject(json1.getString("SELFIE"));
						    	 
						    	 if(json22.getString("reviewResult")!=null) {
						    		 JSONObject json33=JSONObject.parseObject(json22.getString("reviewResult"));
						    		 reviewAnswer_SELFIE=json33.getString("reviewAnswer");
						    		 json_userInfo.put("SELFIE_reviewAnswer", reviewAnswer_SELFIE);
						    	 }
						    	 
						    	 if(reviewAnswer_IDENTITY.equals("GREEN")&&reviewAnswer_SELFIE.equals("GREEN")) {
						    		 
						    		 String applicantDatasStr = getApplicantData(ui.getBackup4());
						    		 
						    		 
						    		 JSONObject json111=JSONObject.parseObject(applicantDatasStr);
						    		 
						    		 json_userInfo.put("createdAt", json111.getString("createdAt"));
						    		 json_userInfo.put("externalUserId", json111.getString("externalUserId"));
						    		 if(json111.getString("info")!=null) {
						    			 json_userInfo.put("info", json111.getString("info"));
						    			 JSONObject json222=JSONObject.parseObject(json111.getString("info"));
						    			 
						    			 String firstName=json222.getString("firstName");
						    			 String firstNameEn=json222.getString("firstNameEn");
						    			 String lastName=json222.getString("lastName");
						    			 String lastNameEn=json222.getString("lastNameEn");
						    			 String dob=json222.getString("dob");
						    			 String country=json222.getString("country");
						    			 
						    			 
						    			 String idDocs=json222.getString("idDocs");
						    			 
						    			 JSONArray ja=JSONArray.parseArray(json222.getString("idDocs"));
						    			 
						    			 JSONObject json333=JSONObject.parseObject(ja.get(0).toString());
						    			 String idDoc=json333.getString("number");
						    			 
						    			 
						    			 SimpleDateFormat sFormat = new SimpleDateFormat("yyyy-MM-dd");
						    			 
						    			 
						    			 
						    			
						    			 
						    			 if(firstName.equals(ui.getName())&&lastName.equals(ui.getSurname())&&dob.equals(sFormat.format(ui.getBirthday()))&&country.equals(ui.getProvince())) {
						    				 ui.setIdentityNum(idDoc);
						    				 ui.setIsAvailable(1);
						    				 ui.setBackup6(json_userInfo.toJSONString());
							    			 this.userInfoService.saveOrUpdate(ui);
						    			 }else {
						    				 
						    				 if(dob.equals(sFormat.format(ui.getBirthday()))&&country.equals(ui.getProvince())) {
						    					 
						    					 if(lastName==null) {
						    						 lastName="";
						    					 }
						    					 
						    					 if(lastNameEn==null) {
						    						 lastNameEn=""; 
						    					 }
						    					 
						    					 
						    					 String crmName=(ui.getName()+ui.getSurname()).trim().replaceAll(" ","").toUpperCase();
						    					 String crmName2=(ui.getSurname()+ui.getName()).trim().replaceAll(" ","").toUpperCase();
						    					 String kycName1=(firstName+lastName).trim().replaceAll(" ","").toUpperCase();
						    					 String kycName2=(lastName+firstName).trim().replaceAll(" ","").toUpperCase();
						    					 String kycName3=(firstNameEn+lastNameEn).trim().replaceAll(" ","").toUpperCase();
						    					 String kycName4=(lastNameEn+firstNameEn).trim().replaceAll(" ","").toUpperCase();
						    					
						    					 
						    					 if(crmName.equals(kycName1)||crmName2.equals(kycName1)) {
						    						 ui.setIdentityNum(idDoc);
						    						 ui.setIsAvailable(1);
						    						 ui.setBackup6(json_userInfo.toJSONString());
									    			 this.userInfoService.saveOrUpdate(ui);
									    			   EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
						    			                try {
						    			                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Successfully Verified","","","",emailInfo.getBackup5(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,ui.getUserName(),ui.getFullname(),"");
						    			                }catch(Exception e) {
						    			                	System.out.println(e);
						    			                }
						    					 }else  if(crmName.equals(kycName2)||crmName2.equals(kycName2)) {
						    						 ui.setIdentityNum(idDoc);
						    						 ui.setIsAvailable(1);
						    						 ui.setBackup6(json_userInfo.toJSONString());
									    			 this.userInfoService.saveOrUpdate(ui);
									    			 EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
						    			                try {
						    			                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Successfully Verified","","","",emailInfo.getBackup5(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,ui.getUserName(),ui.getFullname(),"");
						    			                }catch(Exception e) {
						    			                	System.out.println(e);
						    			                }
						    					 }else  if(crmName.equals(kycName3)||crmName2.equals(kycName3)) {
						    						 ui.setIdentityNum(idDoc);
						    						 ui.setIsAvailable(1);
						    						 ui.setBackup6(json_userInfo.toJSONString());
									    			 this.userInfoService.saveOrUpdate(ui);
									    			   EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
						    			                try {
						    			                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Successfully Verified","","","",emailInfo.getBackup5(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,ui.getUserName(),ui.getFullname(),"");
						    			                }catch(Exception e) {
						    			                	System.out.println(e);
						    			                }
						    					 }else  if(crmName.equals(kycName4)||crmName2.equals(kycName4)) {
						    						 ui.setIdentityNum(idDoc);
						    						 ui.setIsAvailable(1);
						    						 ui.setBackup6(json_userInfo.toJSONString());
									    			 this.userInfoService.saveOrUpdate(ui);
									    			   EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
						    			                try {
						    			                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Successfully Verified","","","",emailInfo.getBackup5(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,ui.getUserName(),ui.getFullname(),"");
						    			                }catch(Exception e) {
						    			                	System.out.println(e);
						    			                }
						    					 }else {
						    						 ui.setIdentityNum(idDoc);
						    						 ui.setIsAvailable(3);
						    						 ui.setBackup6(json_userInfo.toJSONString());
									    			 this.userInfoService.saveOrUpdate(ui);
						    					 }
						    					 
						    				 }else {
						    					 ui.setIdentityNum(idDoc);
						    					 ui.setIsAvailable(3);
						    					 ui.setBackup6(json_userInfo.toJSONString());
								    			 this.userInfoService.saveOrUpdate(ui);
						    				 }
						    				 
						    				 
						    			 }
						    	
						    			 
						    			 
						    		 }
						    	 }else {
						    		 
						    		 if(reviewAnswer_IDENTITY.equals("RED")||reviewAnswer_SELFIE.equals("RED")) {
						    			 
						    			 String applicantDatasStr = getApplicantData(ui.getBackup4());
							    		 JSONObject json111=JSONObject.parseObject(applicantDatasStr);
							    		 json_userInfo.put("createdAt", json111.getString("createdAt"));
							    		 json_userInfo.put("externalUserId", json111.getString("externalUserId"));
							    		 json_userInfo.put("info", json111.getString("info"));
							    		 ui.setBackup6(json_userInfo.toJSONString());
						    			 ui.setIsAvailable(3);
						    			 ui.setBackup6(json_userInfo.toJSONString());
						    			 this.userInfoService.saveOrUpdate(ui);
						    		 }
						    		 
						    	 }
						    	 
						     }else {
						    	 
						     }
						}catch(Exception e) {
							
						}
					}else {
						 ui.setIsAvailable(3);
		    			 this.userInfoService.saveOrUpdate(ui);
					}

		      }
		}
    }
	
	
	  public  String getApplicantStatus(String applicantId) throws NoSuchAlgorithmException, InvalidKeyException, IOException {

	        Response response = sendGet("/resources/applicants/" + applicantId + "/requiredIdDocsStatus");

	        ResponseBody responseBody = response.body();
	        return responseBody != null ? responseBody.string() : null;
	    }
	    
	
	  public  String getApplicantData(String applicantId) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
	        // https://developers.sumsub.com/api-reference/#getting-applicant-status-api

	        Response response = sendGet("/resources/applicants/" + applicantId + "/one");

	        ResponseBody responseBody = response.body();
	        return responseBody != null ? responseBody.string() : null;
	    }
	  
	  private  Response sendPost(String url, RequestBody requestBody) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
	        long ts = Instant.now().getEpochSecond();

	        Request request = new Request.Builder()
	                .url(WebIndexController.SUMSUB_TEST_BASE_URL + url)
	                .header("X-App-Token", WebIndexController.SUMSUB_APP_TOKEN)
	                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.POST, url, requestBodyToBytes(requestBody)))
	                .header("X-App-Access-Ts", String.valueOf(ts))
	                .post(requestBody)
	                .build();

	        Response response = new OkHttpClient().newCall(request).execute();

	        if (response.code() != 200 && response.code() != 201) {
	            // https://developers.sumsub.com/api-reference/#errors
	            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
	            // Then perhaps you should throw the exception. (depends on the logic of your code)
	        }
	        return response;
	    }

	    private  Response sendGet(String url) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
	        long ts = Instant.now().getEpochSecond();

	        Request request = new Request.Builder()
	                .url(WebIndexController.SUMSUB_TEST_BASE_URL + url)
	                .header("X-App-Token", WebIndexController.SUMSUB_APP_TOKEN)
	                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.GET, url, null))
	                .header("X-App-Access-Ts", String.valueOf(ts))
	                .get()
	                .build();

	        Response response = new OkHttpClient().newCall(request).execute();

	        if (response.code() != 200 && response.code() != 201) {
	            // https://developers.sumsub.com/api-reference/#errors
	            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
	            // Then perhaps you should throw the exception. (depends on the logic of your code)
	        }
	        return response;
	    }

	    private  String createSignature(long ts, HttpMethod httpMethod, String path, byte[] body) throws NoSuchAlgorithmException, InvalidKeyException {
	        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
	        hmacSha256.init(new SecretKeySpec(WebIndexController.SUMSUB_SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
	        hmacSha256.update((ts + httpMethod.name() + path).getBytes(StandardCharsets.UTF_8));
	        byte[] bytes = body == null ? hmacSha256.doFinal() : hmacSha256.doFinal(body);
	        return Hex.encodeHexString(bytes);
	    }

	    public  byte[] requestBodyToBytes(RequestBody requestBody) throws IOException {
	        Buffer buffer = new Buffer();
	        requestBody.writeTo(buffer);
	        return buffer.readByteArray();
	    }
	
	
	
    
    
}
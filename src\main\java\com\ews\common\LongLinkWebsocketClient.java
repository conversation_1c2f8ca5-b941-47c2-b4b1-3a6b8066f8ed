package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserInfoService;


public class LongLinkWebsocketClient extends WebSocketClient {
	
	private TradeAccountService tradeAccountService;

	private ServerSettingService serverSettingService;
	
	private OrderInfoService orderInfoService;
	
	private UserInfoService userInfoService;
	
	

	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}

	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}

	public ServerSettingService getServerSettingService() {
		return serverSettingService;
	}

	public void setServerSettingService(ServerSettingService serverSettingService) {
		this.serverSettingService = serverSettingService;
	}

	public OrderInfoService getOrderInfoService() {
		return orderInfoService;
	}

	public void setOrderInfoService(OrderInfoService orderInfoService) {
		this.orderInfoService = orderInfoService;
	}

	public UserInfoService getUserInfoService() {
		return userInfoService;
	}

	public void setUserInfoService(UserInfoService userInfoService) {
		this.userInfoService = userInfoService;
	}

	public LongLinkWebsocketClient(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		System.out.println("自动推送打开链接");
	}
	@Override
	public void onMessage(String arg0) {
		
		
	}
 
	@Override
	public void onError(Exception arg0) {
		System.out.println("自动推送链接错误"+arg0);
	}
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("自动推送链接关闭");
	}
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

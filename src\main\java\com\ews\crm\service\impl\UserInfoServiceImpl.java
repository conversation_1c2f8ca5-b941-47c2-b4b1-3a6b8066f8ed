package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.repository.UserInfoRepository;
import com.ews.crm.service.UserInfoService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class UserInfoServiceImpl implements UserInfoService 
{
	@Autowired
	private UserInfoRepository userInfoRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<UserInfo> findAll(Integer page, Integer size,String sortName,String sortOrder, UserInfo userInfo) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<UserInfo> pages = userInfoRepository.findAll(new Specification<UserInfo>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<UserInfo> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             
             if(!StringUtils.isEmpty(userInfo.getGmtCreateSearchBegin()) || !StringUtils.isEmpty(userInfo.getGmtCreateSearchEnd())) {  
                 try {
                	 if(!StringUtils.isEmpty(userInfo.getGmtCreateSearchBegin()) && StringUtils.isEmpty(userInfo.getGmtCreateSearchEnd())) {//只有开始时间
                	 	Date begin= DateUtil.parseDate(userInfo.getGmtCreateSearchBegin(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("gmtCreate").as(Date.class), begin));//小于开始时间
                	 }else if(StringUtils.isEmpty(userInfo.getGmtCreateSearchBegin()) && !StringUtils.isEmpty(userInfo.getGmtCreateSearchEnd())) {//只有截至时间
                	 	Date end = DateUtil.parseDate(userInfo.getGmtCreateSearchEnd(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("gmtCreate").as(Date.class), end));//大于截至时间
                	 }else {
                	 	Date begin= DateUtil.parseDate(userInfo.getGmtCreateSearchBegin(), "yyyy-MM-dd HH:mm:ss");
                	 	Date end = DateUtil.parseDate(userInfo.getGmtCreateSearchEnd(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.between(root.get("gmtCreate"), begin, end));
                	 }
                 } catch (ParseException e) {
                	 e.printStackTrace();
                 }
                 
               }
             if(!StringUtils.isEmpty(userInfo.getFullname())) { 
                predicates.add(criteriaBuilder.like(root.get("fullname").as(String.class),"%"+userInfo.getFullname()+"%"));
             }
             if(!StringUtils.isEmpty(userInfo.getUserName())) { 
                predicates.add(criteriaBuilder.equal(root.get("userName").as(String.class),userInfo.getUserName()));
             }
             if(!StringUtils.isEmpty(userInfo.getEmail())) { 
                predicates.add(criteriaBuilder.like(root.get("email").as(String.class),"%"+userInfo.getEmail()+"%"));
             }
             if(!StringUtils.isEmpty(userInfo.getTel())) { 
                predicates.add(criteriaBuilder.like(root.get("tel").as(String.class),"%"+userInfo.getTel()+"%"));
             }
             if(!StringUtils.isEmpty(userInfo.getBackup5())) { 
                 predicates.add(criteriaBuilder.like(root.get("backup5").as(String.class),"%"+userInfo.getBackup5()+"%"));
              }
             if(!StringUtils.isEmpty(userInfo.getPassword())) { 
                 predicates.add(criteriaBuilder.equal(root.get("password").as(String.class),userInfo.getPassword()));
              }
             if(!StringUtils.isEmpty(userInfo.getInvitationCode())) { 
                predicates.add(criteriaBuilder.equal(root.get("invitationCode").as(String.class),userInfo.getInvitationCode()));
             }
             if(!StringUtils.isEmpty(userInfo.getUserType())) { 
                predicates.add(criteriaBuilder.equal(root.get("userType").as(Long.class), userInfo.getUserType()));
             }
             if(!StringUtils.isEmpty(userInfo.getParentId())) { 
                predicates.add(criteriaBuilder.equal(root.get("parentId").as(Long.class), userInfo.getParentId()));
             }
             if(!StringUtils.isEmpty(userInfo.getPinyin())) { 
                predicates.add(criteriaBuilder.like(root.get("pinyin").as(String.class),"%"+userInfo.getPinyin()+"%"));
             }
             if(!StringUtils.isEmpty(userInfo.getIsAgent())) { 
                predicates.add(criteriaBuilder.equal(root.get("isAgent").as(Integer.class), userInfo.getIsAgent()));
             }
             if(!StringUtils.isEmpty(userInfo.getIsAvailable())) { 
                 predicates.add(criteriaBuilder.equal(root.get("isAvailable").as(Integer.class), userInfo.getIsAvailable()));
              }
             
             
             if(userInfo.getUserInfoList()!=null&&userInfo.getUserInfoList().size()>0)
             {
            	    Path<Object> path = root.get("parentId");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					for(int i=0;i<userInfo.getUserInfoList().size();i++) {
						  in.value(new Long(userInfo.getUserInfoList().get(i).toString()));
					}
					predicates.add(criteriaBuilder.and(in));
             }
             
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public UserInfo findById(Long id) {
      Optional<UserInfo> op = userInfoRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.userInfoRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         UserInfo old = userInfoRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 userInfoRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 userInfoRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.userInfoRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(UserInfo userInfo) {
    	
       
        Result vr = new Result();
		try {
        	if (userInfo.getId()== null) {
            	userInfo.setGmtCreate(new Date());
            	userInfo.setGmtModified(new Date());
            	userInfo.setIsDeleted(0);
            	if(userInfo.getIsAvailable() == null) {
            		userInfo.setIsAvailable(1);
            	}
            	//userInfo.setUserCreate(loginUser.getUserId());
	    	} else {
            	userInfo.setGmtModified(new Date());
        	}
            userInfoRepository.save(userInfo);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}




package com.ews.crm.controller;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.ews.common.EncryptUtils;
import com.ews.common.HttpUtils;
import com.ews.common.DateUtil;
import com.ews.common.LoginResult;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.common.Result;

import com.alibaba.fastjson.JSONObject; 
import com.alibaba.fastjson.JSONArray;


import com.ews.crm.service.ExchangeRateService;
import com.ews.crm.entity.ExchangeRate;



@RestController
@RequestMapping("/admin/exchangeRate")
public class ExchangeRateController {
	@Autowired
	private ExchangeRateService exchangeRateService;




   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	ExchangeRate query  = new ExchangeRate();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "asc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "desc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	Page<ExchangeRate> pages = exchangeRateService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<ExchangeRate> exchangeRates = pages.getContent();
        	for(int i=0;i<exchangeRates.size();i++) {
        		ExchangeRate entity  = exchangeRates.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", exchangeRates);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			ExchangeRate exchangeRate = new ExchangeRate();
        	exchangeRate.setCurrencyPairs(data.getString("currencyPairs"));
        	exchangeRate.setPurchasePrice(data.getDouble("purchasePrice"));
        	exchangeRate.setSellPrice(data.getDouble("sellPrice"));
        	if (data.containsKey("backup1")) {
        		exchangeRate.setBackup1(data.getInteger("backup1"));
        	}
			if (data.containsKey("backup5")) {
				exchangeRate.setBackup5(data.getDouble("backup5"));
        	}
			if (data.containsKey("backup6")) {
				exchangeRate.setBackup6(data.getDouble("backup6"));
			}
			if (data.containsKey("fee")) {
				exchangeRate.setFee(data.getDouble("fee"));
			}
        	
			Result re = exchangeRateService.saveOrUpdate(exchangeRate);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(exchangeRate);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			ExchangeRate exchangeRate = this.exchangeRateService.findById(id);
				if (exchangeRate != null) {
					exchangeRate.setCurrencyPairs(data.getString("currencyPairs"));
					exchangeRate.setPurchasePrice(data.getDouble("purchasePrice"));
					exchangeRate.setSellPrice(data.getDouble("sellPrice"));
					
					if (data.containsKey("backup1")) {
		        		exchangeRate.setBackup1(data.getInteger("backup1"));
		        	}
					if (data.containsKey("backup5")) {
						exchangeRate.setBackup5(data.getDouble("backup5"));
		        	}
					if (data.containsKey("backup6")) {
						exchangeRate.setBackup6(data.getDouble("backup6"));
					}
					if (data.containsKey("fee")) {
						exchangeRate.setFee(data.getDouble("fee"));
					}
					
					
					
					 if(exchangeRate.getBackup1()!=null&&exchangeRate.getBackup1().intValue()==1) {//按汇率定时更新
						 String host = "https://ali-waihui.showapi.com";
						    String path = "/waihui-list";
						    String method = "GET";
						    String appcode = "cfe5c51e7637470e9b0043bf07de8093";
						    Map<String, String> headers = new HashMap<String, String>();
						    headers.put("Authorization", "APPCODE " + appcode);
						    Map<String, String> querys = new HashMap<String, String>();
						    querys.put("code", "USD");
						    try {
						    	HttpResponse response = HttpUtils.doGet(host, path, method, headers, querys);
						    	JSONObject jsStr = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
						    	JSONObject jsStr_result=JSONObject.parseObject(jsStr.getString("showapi_res_body"));
						    	JSONArray ja_list=JSONArray.parseArray(jsStr_result.getString("list"));
						    	JSONObject ob=(JSONObject)ja_list.get(0);
						    	exchangeRate.setFee(new BigDecimal((ob.getDoubleValue("hui_in")/100)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
						    	exchangeRate.setPurchasePrice(exchangeRate.getFee()+new BigDecimal((exchangeRate.getBackup5()/1000)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
						    	exchangeRate.setSellPrice(exchangeRate.getFee()+new BigDecimal((exchangeRate.getBackup6()/1000)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
						    	//this.exchangeRateService.saveOrUpdate(exchangeRate);
						    } catch (Exception e) {
						    	e.printStackTrace();
						    }
						 
						 
					 }
					Result re = exchangeRateService.saveOrUpdate(exchangeRate);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = exchangeRateService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = exchangeRateService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}


}


package com.ews.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.util.ByteSource;

public class EncryptUtils {
	public static final String ALGORITHMNAME = "md5";// 加密方式

	public static final Integer HASHITERATIONS = 2;// hash次数

	/**
	 * 加密
	 * @param str
	 * @param salt
	 * @return
	 */
	public static String encrypt(String str, String salt) {
		
		return new SimpleHash(ALGORITHMNAME, str, ByteSource.Util.bytes(salt), HASHITERATIONS).toString();
	}

	public static void main(String args[]) throws ParseException {
		String dateString = "2018-12-16 12:45:14"; 
		Date date= new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(dateString);
		
		String salt="adminxxassdasdadasd112132"+date.getTime();
		
		System.out.println(EncryptUtils.encrypt("111111", salt));
	}
}

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.operName" :placeholder="$t('operLog.placeholder1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.busType" style="width: 320px" :placeholder="$t('operLog.placeholder2')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in busTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.operType" style="width: 120px" :placeholder="$t('operLog.placeholder3')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in operTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('operLog.placeholder5')" min-width="150px" align="center" prop="gmtCreate" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('operLog.placeholder1')" prop="operName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.operName }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('operLog.placeholder2')" prop="busType" min-width="150px" align="center" :formatter="fmtBusType" />
      <el-table-column :label="$t('operLog.placeholder3')" prop="operType" min-width="150px" align="center" :formatter="fmtOperType" />

      <el-table-column :label="$t('operLog.placeholder4')" prop="backup1" min-width="350px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup1 }}</span>
        </template>
      </el-table-column>

    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchOperLog, createOperLog, updateOperLog, updateIsAvailable, removeOperLog } from '@/api/operLog'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'OperLogTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        operName: undefined,
        busId: undefined,
        operType: undefined,
        busType: undefined
      },
      temp: {
        id: undefined,
        operName: '',
        busId: '',
        busType: '',
        operType: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 busTypes: [
        {
          value: 1,
          label: this.$t('operLog.busType1')
        },
        {
          value: 2,
          label: this.$t('operLog.busType2')
        },
        {
          value: 3,
          label: this.$t('operLog.busType3')
        },
        {
          value: 4,
          label: this.$t('operLog.busType4')
        },
        {
          value: 5,
          label: this.$t('operLog.busType5')
        },
        {
          value: 6,
          label: this.$t('operLog.busType6')
        },
        {
          value: 7,
          label: this.$t('operLog.busType7')
        }
      ],
				 operTypes: [
        {
          value: 1,
          label: this.$t('operLog.operType1')
        },
        {
          value: 2,
          label: this.$t('operLog.operType2')
        },
        {
          value: 3,
          label: this.$t('operLog.operType3')
        },
        {
          value: 4,
          label: this.$t('operLog.operType4')
        }
      ],
      rules: {
        operId: [
        ],
        operName: [
          { required: true, message: 'The operator name cannot be empty', trigger: 'change' },,
        ],
        busId: [
          { required: true, message: 'Business ID cannot be empty', trigger: 'change' },,
        ],
        busType: [
        ],
        operType: [
          { required: true, message: 'Operation type cannot be empty', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        operName: '',
        busId: '',
        busType: '',
        operType: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createOperLog(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateOperLog(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 }, fmtBusType(row, column) {
					   if (row.busType == 1) {
        return this.$t('operLog.busType1')
      } else if (row.busType == 2) {
        return this.$t('operLog.busType2')
      } else if (row.busType == 3) {
        return this.$t('operLog.busType3')
      } else if (row.busType == 4) {
        return this.$t('operLog.busType4')
      } else if (row.busType == 5) {
        return this.$t('operLog.busType5')
      } else if (row.busType == 6) {
        return this.$t('operLog.busType6')
      } else if (row.busType == 7) {
        return this.$t('operLog.busType7')
      }
    }, fmtOperType(row, column) {
					   if (row.operType == 1) {
        return this.$t('operLog.operType1')
      } else if (row.operType == 2) {
        return this.$t('operLog.operType2')
      } else if (row.operType == 3) {
        return this.$t('operLog.operType3')
      } else if (row.operType == 4) {
        return this.$t('operLog.operType4')
      }
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeOperLog(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

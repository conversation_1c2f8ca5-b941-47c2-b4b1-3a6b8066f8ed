package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "deposit_bank")
public class DepositBank implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *通道名称
    **/
	@Column(name = "bank_name")
	protected  String bankName;

    /**
    *通道货币
    **/
	@Column(name = "bank_account")
	protected  String bankAccount;

    /**
    *汇率加点
    **/
	@Column(name = "bank_address")
	protected  String bankAddress;

    /**
    *请求地址
    **/
	@Column(name = "account_name")
	protected  String accountName;

    /**
    *最小入金数量
    **/
	@Column(name = "tel")
	protected  String tel;

    /**
    *最大入金数量
    **/
	@Column(name = "swift")
	protected  String swift;

    /**
    *通道类型   1 网关   2银行转账  3钱包
    **/
	@Column(name = "currency_type")
	protected  Integer currencyType;

    /**
    *KYC通过才能看到
    **/
	@Column(name = "is_show")
	protected  Integer isShow;

    /**
    *在哪个居住地
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *这些类型看不到
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *这些标签看不到
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *Display Notes (English)
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *Display Notes (Chinese-Simplified)
    **/
	@Column(name = "backup5")
	protected  String backup5;

    /**
    *Display Notes (Chinese-Traditional)
    **/
	@Column(name = "backup6")
	protected  String backup6;
	
	  /**
	    *Display Notes (Thai)
	    **/
		@Column(name = "backup7")
		protected  String backup7;
		
		  /**
		    *Display Notes (Malay)
		    **/
			@Column(name = "backup8")
			protected  String backup8;
			
			  /**
			    *Display Notes (Vietnamese)
			    **/
				@Column(name = "backup9")
				protected  String backup9;
				
				  /**
				    *Display Notes (Indonesia)
				    **/
					@Column(name = "backup10")
					protected  String backup10;
					
					/**
					    *Callback IP
					    **/
						@Column(name = "backup11")
						protected  String backup11;

						
						
						/**
					        *银行名字
					    **/
						@Column(name = "backup12")
						protected  String backup12;
						/**
					        *银行地址
					    **/
						@Column(name = "backup13")
						protected  String backup13;
						/**
					    *SWIFT CODE
					    **/
						@Column(name = "backup14")
						protected  String backup14;
						
						/**
					        *银行账号
					    **/
						@Column(name = "backup15")
						protected  String backup15;
						/**
					        *收款公司
					    **/
						@Column(name = "backup16")
						protected  String backup16;
						/**
					        *数字货币地址
					    **/
						@Column(name = "backup17")
						protected  String backup17;
						
						@Column(name = "backup18")
						protected  String backup18;
						
						@Column(name = "backup19")
						protected  String backup19;
						
						@Column(name = "backup20")
						protected  String backup20;
					

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getBankName()
    {
        return bankName;
    }
    public void setBankName(String  bankName)
    {
        this.bankName = bankName;
    }
    public String  getBankAccount()
    {
        return bankAccount;
    }
    public void setBankAccount(String  bankAccount)
    {
        this.bankAccount = bankAccount;
    }
    public String  getBankAddress()
    {
        return bankAddress;
    }
    public void setBankAddress(String  bankAddress)
    {
        this.bankAddress = bankAddress;
    }
    public String  getAccountName()
    {
        return accountName;
    }
    public void setAccountName(String  accountName)
    {
        this.accountName = accountName;
    }
    public String  getTel()
    {
        return tel;
    }
    public void setTel(String  tel)
    {
        this.tel = tel;
    }
    public String  getSwift()
    {
        return swift;
    }
    public void setSwift(String  swift)
    {
        this.swift = swift;
    }
    public Integer  getCurrencyType()
    {
        return currencyType;
    }
    public void setCurrencyType(Integer  currencyType)
    {
        this.currencyType = currencyType;
    }
    public Integer  getIsShow()
    {
        return isShow;
    }
    public void setIsShow(Integer  isShow)
    {
        this.isShow = isShow;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public String  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(String  backup5)
    {
        this.backup5 = backup5;
    }
    public String  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(String  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public String getBackup7() {
		return backup7;
	}
	public void setBackup7(String backup7) {
		this.backup7 = backup7;
	}
	public String getBackup8() {
		return backup8;
	}
	public void setBackup8(String backup8) {
		this.backup8 = backup8;
	}
	public String getBackup9() {
		return backup9;
	}
	public void setBackup9(String backup9) {
		this.backup9 = backup9;
	}
	public String getBackup10() {
		return backup10;
	}
	public void setBackup10(String backup10) {
		this.backup10 = backup10;
	}
	public String getBackup11() {
		return backup11;
	}
	public void setBackup11(String backup11) {
		this.backup11 = backup11;
	}
	public String getBackup12() {
		return backup12;
	}
	public void setBackup12(String backup12) {
		this.backup12 = backup12;
	}
	public String getBackup13() {
		return backup13;
	}
	public void setBackup13(String backup13) {
		this.backup13 = backup13;
	}
	public String getBackup14() {
		return backup14;
	}
	public void setBackup14(String backup14) {
		this.backup14 = backup14;
	}
	public String getBackup15() {
		return backup15;
	}
	public void setBackup15(String backup15) {
		this.backup15 = backup15;
	}
	public String getBackup16() {
		return backup16;
	}
	public void setBackup16(String backup16) {
		this.backup16 = backup16;
	}
	public String getBackup17() {
		return backup17;
	}
	public void setBackup17(String backup17) {
		this.backup17 = backup17;
	}
	public String getBackup18() {
		return backup18;
	}
	public void setBackup18(String backup18) {
		this.backup18 = backup18;
	}
	public String getBackup19() {
		return backup19;
	}
	public void setBackup19(String backup19) {
		this.backup19 = backup19;
	}
	public String getBackup20() {
		return backup20;
	}
	public void setBackup20(String backup20) {
		this.backup20 = backup20;
	}
    
	
    

}

package com.ews.config.i18n;

import java.util.Locale;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

public class MyLocaleResolver implements LocaleResolver {

	private static final String I18N_LANGUAGE = "i18n_language";
	private static final String I18N_LANGUAGE_SESSION = "i18n_language_session";

	@Override
	public Locale resolveLocale(HttpServletRequest req) {
		String i18n_language = req.getParameter(I18N_LANGUAGE);
		HttpSession session = req.getSession();
		Locale locale = Locale.getDefault();
		
		
		if (!StringUtils.isEmpty(i18n_language)) {
			String[] language = i18n_language.split("_");
			locale = new Locale(language[0], language[1]);

			// 将国际化语言保存到session
			session.setAttribute(I18N_LANGUAGE_SESSION, locale);
		} else {
			// 如果没有带国际化参数，则判断session有没有保存，有保存，则使用保存的，也就是之前设置的，避免之后的请求不带国际化参数造成语言显示不对
			String i18n_language_session="";
			if(session==null||session.getAttribute(I18N_LANGUAGE_SESSION)==null) {
				 i18n_language_session="zh_CN";
			}else {
				 i18n_language_session = session.getAttribute(I18N_LANGUAGE_SESSION).toString();
			}
			
			
			String[] language = i18n_language_session.split("_");
			Locale localeInSession = null;
			try {
				 localeInSession = new Locale(language[0], language[1]);
			}catch(Exception e) {
				
			}

			if (localeInSession != null) {
				locale = localeInSession;
			} else {
				locale = req.getLocale();
			}
		}
		return locale;
	}

	@Override
	public void setLocale(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			Locale locale) {
	}

}


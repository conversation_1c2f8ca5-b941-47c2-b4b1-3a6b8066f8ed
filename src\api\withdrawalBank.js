import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/withdrawalBank/list',
    method: 'post',
    params: query
  })
}

export function fetchWithdrawalBank(id) {
  return request({
    url: '/withdrawalBank/detail',
    method: 'get',
    params: { id }
  })
}

export function createWithdrawalBank(data) {
  return request({
    url: '/withdrawalBank/add',
    method: 'post',
    data
  })
}

export function updateWithdrawalBank(data) {
  return request({
    url: '/withdrawalBank/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/withdrawalBank/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeWithdrawalBank(id) {
  return request({
    url: '/withdrawalBank/remove',
    method: 'get',
    params: { id }
  })
}


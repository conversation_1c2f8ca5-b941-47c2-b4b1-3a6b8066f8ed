package com.ews.config.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

public class MobileFilter implements Filter {


    String NO_LOGIN = "您还未登录";
 
    //不需要登录就可以访问的路径(比如:注册登录等) admin后台在下方过滤的时候全部默认放开
 
    String[] includeUrls = new String[]{"/mobile/login","/toLogin","/","/css","/js","/images","img","/uploads"};
 
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
    	
    	//处理前台用户登录操作及验证
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        HttpSession session = request.getSession(false);
        String uri = request.getRequestURI();
        //是否需要过滤
        boolean needFilter = isNeedFilter(uri);
        if (!needFilter) { //不需要过滤直接传给下一个过滤器
            filterChain.doFilter(servletRequest, servletResponse);
        } else { //需要过滤器
            // session中包含user对象,则是登录状态
            if(session!=null&&session.getAttribute("user") != null){
                System.out.println("user:"+session.getAttribute("user"));
                filterChain.doFilter(request, response);
            }else{
            	filterChain.doFilter(request, response);
            	/*
                String requestType = request.getHeader("X-Requested-With");
                //判断是否是ajax请求（因为ajax页面不跳转，所以这块需要判断一下）
                if(requestType!=null && "XMLHttpRequest".equals(requestType)){
                    response.getWriter().write(this.NO_LOGIN);
                }else{
                	System.out.println(uri+"   验证失败   go to defalut");
                    //重定向到指定路径
                    response.sendRedirect(request.getContextPath()+"/mobile/login");
                }
                return;
                
                */
            }
        }
    }
    /**
     * @Description: 是否需要过滤
     */
    public boolean isNeedFilter(String uri) {
      /*
    	for (String includeUrl : includeUrls) {
            if(includeUrl.equals(uri) || uri.indexOf("/admin/")>-1||uri.indexOf("/admin")>-1 || uri.indexOf("/mobile/css/") >-1 || uri.indexOf("/plugins/")>-1 || uri.indexOf("/js/") >-1 || uri.indexOf("/css/")>-1)  {
                return false;
            }
        }
        */
        return false;
 
    }
 
    @Override
    public void init(FilterConfig FilterConfig) throws ServletException {
    }
 
    @Override
    public void destroy() {
    }


}

<template>
	<div class="app-container">
		<div class="filter-container">
				<el-date-picker 
					v-model="closeDateRange" 
					type="daterange" 
					align="right" 
					unlink-panels 
					range-separator="至" 
					start-placeholder="平仓开始日期" 
					end-placeholder="平仓结束日期" 
					
					style="width: 380px" 
					class="filter-item"
				/>

&nbsp;&nbsp;


				<el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
				&nbsp;&nbsp;
				<el-tag v-if="(this.yjss!=undefined)?true:false">交易手数{{ this.yjss }}</el-tag>&nbsp;&nbsp;
				<el-tag v-if="(this.hlje!=undefined)?true:false">盈亏统计{{ this.hlje }}</el-tag>
		</div>
		<el-table :key="tableKey"  v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;"  @sort-change="sortChange" >
			 <el-table-column label="序号" width="50px"  align="center">
				<template slot-scope="scope">
					 <span>{{ scope.row.sortNum }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="订单号" prop="orderNo"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.orderNo }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="交易账号" prop="loginId"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.loginId }}</span>
				</template>
			 </el-table-column>
			 
			 <el-table-column label="交易方向" prop="type"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span v-if="scope.row.type === 0">BUY</span>
					<span v-else-if="scope.row.type === 1">SELL</span>
					<span v-else>{{ scope.row.type }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="交易手数" prop="volume"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.volume }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="交易品种" prop="symbol"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.symbol }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="平仓时间" prop="closeTime"  min-width="150px"  align="center">	
				<template slot-scope="scope">
					<span>{{ scope.row.closeTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="平仓价格" prop="closePrice"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.closePrice }}</span>
				</template>
			 </el-table-column>
			 <el-table-column label="盈亏" prop="profit"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.profit }}</span>
				</template>
			 </el-table-column>
			
			 	
						
		</el-table>
		<pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
	
	</div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import {fetchMyList,fetchOrderInfo,createOrderInfo,updateOrderInfo,updateIsAvailable,removeOrderInfo} from '@/api/orderInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
	name: 'orderInfoTable',
	components: { Pagination },			
	directives: { waves },			
	data() {
			return {
				tableKey: 0,
				list: null,
				total: 0,
			    listLoading: true,
				listQuery: {
			 		page: 1,
					limit: 10,
					orderNo:undefined,
					type:undefined,
					symbol:undefined,
					status:undefined,
					tradeId:undefined,
					loginId:undefined,
					closeTime1:undefined,
					closeTime2:undefined,
					agentAcount:undefined,
			},
				temp: {
					id:undefined,
					orderNo:'',
					type:'',
					symbol:'',
					status:'',
					tradeId:'',
					loginId:'',
				},
				yjss:undefined,
				hlje:undefined,
				closeDateRange:undefined,
				dialogFormAddVisible:false,
				dialogFormEditVisible:false,
				rules: {
					orderNo: [
					],
					openTime: [
					],
					openPrice: [
					],
					type: [
					],
					volume: [
					],
					symbol: [
					],
					closeTime: [
					],
					closePrice: [
					],
					storage: [
					],
					profit: [
					],
					status: [
					],
					isRakeback: [
					],
					rakebackAmount: [
					],
					tradeId: [
					],
					loginId: [
					],
					backup1: [
					],
					backup2: [
					],
					backup3: [
					],
					backup4: [
					],
					backup5: [
					],
					backup6: [
					],
					backup7: [
					],
					backup8: [
					]
				}
				}
			},
			created() {
				this.getList()
			},
			methods: {
				getList() {
					this.listLoading = true//显示加载动画
					fetchMyList(this.listQuery).then(response => {
						this.list = response.data.items
					 	this.total = response.data.total
						this.listLoading = false
						this.yjss = response.data.yjss
						this.hlje = response.data.hlje
					})
				},
				handleFilter() {
					if (this.closeDateRange) {
				 this.listQuery.closeTime1 = Math.floor(new Date(this.closeDateRange[0]).getTime() / 1000)+(5*60*60)//雅典时间
				 this.listQuery.closeTime2 = Math.floor(new Date(this.closeDateRange[1]).getTime() / 1000)+(24*60*60-1)+(5*60*60)//雅典时间
			 } else {
				 	 this.listQuery.closeTime1 = undefined
					 this.listQuery.closeTime2 = undefined
			 }
					this.listQuery.page = 1
					this.getList()
				},
				sortChange(data) {
					const { prop, order } = data
					 if (prop === 'gmtCreate') {
						this.sortByGmtCreate(order)
					 }
				},
				sortByGmtCreate(order) {
					if (order === 'ascending') {
						this.listQuery.sort = '+gmtCreate'
					} else {
						this.listQuery.sort = '-gmtCreate'
					}
					this.handleFilter()
				},
				resetTemp() {
					this.temp = {
							id:undefined,
							orderNo:'',
							type:'',
							symbol:'',
							status:'',
							tradeId:'',
							loginId:'',
					}
				},
				handleCreate() {
					this.resetTemp()
					this.dialogFormAddVisible = true
					this.$nextTick(() => {
						this.$refs['dataForm'].clearValidate()
					})
				},
				createData() {
					this.$refs['dataForm'].validate((valid) => {
						if (valid) {
							createOrderInfo(this.temp).then(result => {
								if(result.code==20000){
									this.$notify({
										title: '成功',
										message: '操作成功',
										type: 'success',
										duration: 2000
									})
									this.getList()
									this.dialogFormAddVisible = false
								}else{
									this.$message.error(response.msg);
								}
							})
						}
					})
				 },
				updateData() {
					this.$refs['dataEditForm'].validate((valid) => {
						if (valid) {
							updateOrderInfo(this.temp).then(result => {
								if(result.code==20000){
									this.$notify({
										title: '成功',
										message: '操作成功',
										type: 'success',
										duration: 2000
									})
									this.getList()
									this.dialogFormEditVisible = false
								}else{
									this.$message.error(response.msg);
								}
							})
						}
					})
				 },
				handleUpdate(row) {
					this.temp = Object.assign({}, row)
					this.dialogFormEditVisible=true
					this.$nextTick(() => {
						this.$refs['dataEditForm'].clearValidate()
					})
				},
				 handleDelete(row) {
					this.$confirm('您确定要删除该数据吗?', '提示信息', {
				 		confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					.then(async() => {
				 		await removeOrderInfo(row.id).then(result =>{
							if(result.code == 20000){
								this.$notify({
									title: '成功',
									message: '删除成功',
									type: 'success',
									duration: 2000
								})
								const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
							}else{
								this.$message.error(result.msg);
							}
					})
				})
				.catch(err => { console.error(err) })
				},
			}
}
</script>

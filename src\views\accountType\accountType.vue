<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.typeName" :placeholder="$t('accountType.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.groupName" :placeholder="$t('accountType.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.isShow" style="width: 120px" :placeholder="$t('accountType.label3')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in isShows" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.openType" style="width: 120px" :placeholder="$t('accountType.label4')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in openTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('accountType.label1')" prop="typeName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.typeName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('accountType.label2')" prop="groupName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.groupName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('accountType.label3')" prop="isShow" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.isShow==1?$t('accountType.show'):$t('accountType.hidden') }}</span>
        </template>
      </el-table-column>
      <!--el-table-column :label="$t('accountType.label4')" prop="openType"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.openType==1?$t('accountType.scale'):$t('accountType.regular') }}</span>
				</template>
			 </el-table-column>
			 <el-table-column :label="$t('accountType.label5')" prop="creditValue"  min-width="150px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.creditValue }}</span>
				</template>
			 </el-table-column-->
      <el-table-column :label="$t('accountType.label6')" prop="backup1" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('accountType.label7')" prop="backup2" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog :title="$t('accountType.dialogTitleAdd')" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="150px" style="width: 100%; margin-left:10px;">
        <el-form-item :label="$t('accountType.label1')" prop="typeName">
          <el-input v-model="temp.typeName" />
        </el-form-item>
        <el-form-item :label="$t('accountType.label2')" prop="groupName">
          <el-select v-model="temp.groupName" style="width: 100%; max-width: 380px;" :placeholder="$t('accountType.label2')" clearable>
            <el-option v-for="item in groupNames" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('accountType.label3')" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
        <!--el-form-item :label="$t('accountType.label4')" prop="openType">
				 <el-select v-model="temp.openType" :placeholder="$t('userTable.placeholder3')">
				 	<el-option
				 	v-for="openType in openTypes"
				 	:key="openType.value"
				 	:label="openType.label"
				 	:value="openType.value">
				 </el-option>
				 </el-select>
				</el-form-item>
				<el-form-item :label="$t('accountType.label5')" prop="creditValue">
				 <el-input v-model="temp.creditValue"></el-input>
				</el-form-item-->
        <el-form-item :label="$t('accountType.label6')" prop="backup1">
          <el-input v-model="temp.backup1" />
        </el-form-item>
        <el-form-item :label="$t('accountType.label7')" prop="backup2">
          <el-input v-model="temp.backup2" />
        </el-form-item>

        <el-form-item :label="$t('accountType.label10')" prop="isAutoAudit1">
          <el-select v-model="temp.isAutoAudit1" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="iaa in isAutoAudits"
              :key="iaa.value"
              :label="iaa.label"
              :value="iaa.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('accountType.label9')" prop="isAutoAudit2">
          <el-select v-model="temp.isAutoAudit2" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="iaa in isAutoAudits"
              :key="iaa.value"
              :label="iaa.label"
              :value="iaa.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('supplement.label7')" prop="max_Accounts">
          <el-input v-model="temp.max_Accounts" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label14')" prop="countryShow">
          <el-select v-model="listBackup1" multiple :placeholder="$t('userTable.placeholder3')" style="width: 100%; max-width: 500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('accountType.label8')">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('accountType.selectAll') }}</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-model="checkedCities">
            <el-checkbox v-for="p in levers" :key="p.id" :label="p.id">1:{{ p.leverNum }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="$t('accountType.dialogTitleEdit')" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="150px" style="width: 100%; margin-left:10px;">
        <el-form-item :label="$t('accountType.label1')" prop="typeName">
          <el-input v-model="temp.typeName" />
        </el-form-item>
        <el-form-item :label="$t('accountType.label2')" prop="groupName">
          <el-select v-model="temp.groupName" style="width: 100%; max-width: 380px;" :placeholder="$t('accountType.label2')" clearable>
            <el-option v-for="item in groupNames" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('accountType.label3')" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
        <!--el-form-item :label="$t('accountType.label4')" prop="openType">
				 <el-select v-model="temp.openType" :placeholder="$t('userTable.placeholder3')">
				 	<el-option
				 	v-for="openType in openTypes"
				 	:key="openType.value"
				 	:label="openType.label"
				 	:value="openType.value">
				 </el-option>
				 </el-select>
				</el-form-item>
				<el-form-item :label="$t('accountType.label5')" prop="creditValue">
				 <el-input v-model="temp.creditValue"></el-input>
				</el-form-item-->
        <el-form-item :label="$t('accountType.label6')" prop="backup1">
          <el-input v-model="temp.backup1" />
        </el-form-item>
        <el-form-item :label="$t('accountType.label7')" prop="backup2">
          <el-input v-model="temp.backup2" />
        </el-form-item>

        <el-form-item :label="$t('accountType.label10')" prop="isAutoAudit1">
          <el-select v-model="temp.isAutoAudit1" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="iaa in isAutoAudits"
              :key="iaa.value"
              :label="iaa.label"
              :value="iaa.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('accountType.label9')" prop="isAutoAudit2">
          <el-select v-model="temp.isAutoAudit2" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="iaa in isAutoAudits"
              :key="iaa.value"
              :label="iaa.label"
              :value="iaa.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('supplement.label7')" prop="max_Accounts">
          <el-input v-model="temp.max_Accounts" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label14')" prop="countryShow">
          <el-select v-model="listBackup1" multiple :placeholder="$t('userTable.placeholder3')" style="width: 100%; max-width: 500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('accountType.label8')">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('role.allSelect') }}</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-model="checkedCities">
            <el-checkbox v-for="p in levers" :key="p.id" :label="p.id">1:{{ p.leverNum }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchCountriesList } from '@/api/countries'
import { fetchList, fetchAccountType, createAccountType, updateAccountType, updateIsAvailable, removeAccountType } from '@/api/accountType'
import { listshow } from '@/api/userGroup'
import { fetchLeverList, fetchLeverList2 } from '@/api/lever'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

var count = 0
export default {
  name: 'AccountTypeTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      checkAll: false,
      isIndeterminate: false,
      checkedCities: [],
      listQuery: {
			 		page: 1,
        limit: 10,
        typeName: undefined,
        groupName: undefined,
        isShow: undefined,
        openType: undefined
      }, listQuery2: {
			 		page: 1,
        limit: 1000

      },
      listBackup1: [],
      temp: {
        id: undefined,
        typeName: '',
        groupName: '',
        isShow: '',
        openType: 1,
        backup1: '',
        backup2: '',
        isAutoAudit1: undefined,
        isAutoAudit2: undefined,
        max_Accounts: 5,
        countryShow: '',
        creditValue: 0,
          	levers: [],
        levers2: []
      },
      option1s: [],
      groupNames: [],
      levers: [

        				],
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      isAutoAudits: [
        {
          	value: 1,
          	label: this.$t('accountType.autoAudit')
        },
        {
          	value: 0,
          	label: this.$t('accountType.manualReview')
        }

      ],
				 isShows: [
        {
          value: 1,
          label: this.$t('accountType.show')
        },
        {
          value: 2,
          label: this.$t('accountType.hidden')
        }
      ],
				 openTypes: [
        {
          value: 1,
          label: this.$t('accountType.scale')
        },
        {
          value: 2,
          label: this.$t('accountType.regular')
        }
      ],
      rules: {
        typeName: [
          { required: true, message: this.$t('accountType.typeNameEmpty'), trigger: 'change' },,
        ],
        groupName: [
          { required: true, message: this.$t('accountType.groupNameEmpty'), trigger: 'change' },,
        ],
        isShow: [
          { required: true, message: this.$t('accountType.isShowEmpty'), trigger: 'change' },,
        ],
        openType: [
          { required: true, message: this.$t('accountType.openTypeEmpty'), trigger: 'change' },,
        ],
        creditValue: [
          { required: true, message: this.$t('accountType.creditValueEmpty'), trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.getList()
    this.option1s = []
    fetchLeverList(this.listQuery).then(response => {
      // console.log(response.data.item.length)
      this.levers = response.data.items
    })
    fetchCountriesList(this.listQuery2).then(response => {
      var datas4 = response.data.items
             	for (var j = 0; j < datas4.length; j++) {
             		var id = '"' + datas4[j].countryCodeThree + '"'
             		var name = datas4[j].countryName
             		var per = []
             		per.value = id + ''
             		per.label = name
        this.option1s.push(per)
      }
    })
    this.groupNames = []
    listshow().then(response => {
      var datas3 = response.data.items
             	for (var j = 0; j < datas3.length; j++) {
             		var id = datas3[j].groupName
             		var name = datas3[j].groupName
             		var per = []
             		per.value = id
             		per.label = name
        this.groupNames.push(per)
      }
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    }, findLeverList(query) {
      fetchLeverList2(query).then(res => {
          	 var data = res.data.items
          	for (var j = 0; j < data.length; j++) {
          		this.checkedCities.push(data[j].leverId)
          	}
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    handleCheckAllChange(val) {
      this.checkedCities = []
      if (val) {
        for (var j = 0; j < this.levers.length; j++) {
          this.checkedCities.push(this.levers[j].id)
        }
      }
      this.isIndeterminate = false
				  },
					  handleCheckedCitiesChange(value) {
      const checkedCount = value.length
					  },
    resetTemp() {
      this.temp = {
        id: undefined,
        typeName: '',
        groupName: '',
        isShow: '',
        backup1: '',
        openType: 1,
        creditValue: 0,
        isAutoAudit1: 0,
        isAutoAudit2: 0,
        max_Accounts: 5,
        countryShow: '',
        levers: [],
        levers2: []
      }
    },
    handleCreate() {
      this.resetTemp()
      this.checkedCities = []
      this.listBackup1 = []
      this.checkAll = false
      this.dialogFormAddVisible = true
      this.temp.isShow = 1
      this.temp.openType = 2

      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
          	this.temp.levers2 = this.checkedCities
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.countryShow = this.listBackup1
          createAccountType(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.temp.levers2 = this.checkedCities
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.countryShow = this.listBackup1
          updateAccountType(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.checkedCities = []
      this.checkAll = false

      this.listBackup1 = []
      if (row.countryShow != null && row.countryShow != '' && row.countryShow != undefined) {
        var datas1 = eval(row.countryShow)

        for (var m = 0; m < datas1.length; m++) {
          this.listBackup1.push('"' + datas1[m] + '"')
        }
      }

      this.findLeverList(row.id)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeAccountType(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

<style scoped>
@media screen and (max-width: 992px) {
  .el-form-item__label {
    width: auto !important;
    text-align: left !important;
  }
  .el-form {
    padding-right: 10px;
  }
  .el-form-item {
    display: block !important;
    margin-bottom: 22px;
  }
  .el-checkbox-group {
    max-width: 100%;
    flex-wrap: wrap;
  }
}
</style>

package com.ews.config.result;

public enum ResultEnums {
	SUCCESS("20000", "请求成功"),
    ERROR("00000", "请求失败"),
    SYSTEM_ERROR("1000", "系统异常"),
    BUSSINESS_ERROR("2001", "业务逻辑错误"),
    VERIFY_CODE_ERROR("2002", "业务参数错误"),
    PARAM_ERROR("2002", "业务参数错误"),
	LOGIN_ERROR("60204","用户名或密码错误"),
	LOGIN_TIMEOUT_ERROR("50008","登录超时");
	
	
    private String code;
    private String msg;

    ResultEnums(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

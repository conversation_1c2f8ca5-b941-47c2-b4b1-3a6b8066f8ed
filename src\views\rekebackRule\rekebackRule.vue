<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.ruleName" :placeholder="$t('rekebackRule.label1')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label1')" prop="ruleName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ruleName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label2')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label3')" prop="rekebackA1" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackA1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label4')" prop="rekebackA2" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackA2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label5')" prop="rekebackA3" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackA3 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label6')" prop="rekebackB1" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackB1 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label7')" prop="rekebackB2" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackB2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label8')" prop="rekebackB3" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackB3 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('rekebackRule.label9')"prop="rekebackBALL" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rekebackA1+scope.row.rekebackA2+scope.row.rekebackA3+scope.row.rekebackB1+scope.row.rekebackB2+scope.row.rekebackB3+scope.row.rekebackB4+scope.row.rekebackB5+scope.row.rekebackB6+scope.row.rekebackB7+scope.row.rekebackB8+scope.row.rekebackB9+scope.row.rekebackB10+scope.row.rekebackB11+scope.row.rekebackB12+scope.row.rekebackB13+scope.row.rekebackB14+scope.row.rekebackB15+scope.row.rekebackB16+scope.row.rekebackB17+scope.row.rekebackB18+scope.row.rekebackB19+scope.row.rekebackB20+scope.row.rekebackB21+scope.row.rekebackB22+scope.row.rekebackB23+scope.row.rekebackB24+scope.row.rekebackB25+scope.row.rekebackB26+scope.row.rekebackB27+scope.row.rekebackB28+scope.row.rekebackB29+scope.row.rekebackB30 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button type="success" size="mini" @click="handleCopy(scope.row)">{{ $t('rekebackRule.label10') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchRekebackRule, createRekebackRule, updateRekebackRule, updateIsAvailable, removeRekebackRule } from '@/api/rekebackRule'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'RekebackRuleTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        ruleName: undefined
      },
      temp: {
        id: undefined,
        ruleName: '',
        rekebackA1: '',
        rekebackA2: '',
        rekebackA3: '',
        rekebackB1: '',
        rekebackB2: '',
        rekebackB3: '',
        remark: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        ruleName: [,
        ],
        rekebackA1: [,
        ],
        rekebackA2: [,
        ],
        rekebackA3: [,
        ],
        rekebackA4: [
        ],
        rekebackA5: [
        ],
        rekebackB1: [,
        ],
        rekebackB2: [,
        ],
        rekebackB3: [,
        ],
        rekebackB4: [
        ],
        remark: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.listQuery.page = this.$route.params.page
    this.listQuery.ruleName = this.$route.params.ruleName
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        ruleName: '',
        rekebackA1: '',
        rekebackA2: '',
        rekebackA3: '',
        rekebackB1: '',
        rekebackB2: '',
        rekebackB3: '',
        remark: ''
      }
    },
    handleCreate() {
      this.$router.push({ path: '/tradeSetting/addRekebackRule/' })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createRekebackRule(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateRekebackRule(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.$router.push({
        name: 'EditRekebackRule',
        params: {
          id: row.id,
          page: this.listQuery.page,
          ruleName: this.listQuery.ruleName
        }
      })
      //	this.$router.push({path: '/tradeSetting/editRekebackRule/'+row.id});
    }, handleCopy(row) {
      this.$router.push({
        name: 'CopyRekebackRule',
        params: {
          id: row.id,
          page: this.listQuery.page,
          ruleName: this.listQuery.ruleName
        }
      })
      // this.$router.push({path: '/tradeSetting/copyRekebackRule/'+row.id});
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeRekebackRule(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

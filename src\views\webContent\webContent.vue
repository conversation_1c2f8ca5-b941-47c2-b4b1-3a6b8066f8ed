<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.contentTitle" :placeholder="$t('webContent.placeholder1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter"> {{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate"> {{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('webContent.label1')" prop="contentTitle" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.contentTitle }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('webContent.label3')" prop="gmtCreate" sortable="custom" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button v-if="scope.row.contentType!=1" size="mini" type="danger" @click="handleDelete(scope.row)">  {{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('webContent.label1')" prop="contentTitle">
          <el-input v-model="temp.contentTitle" />
        </el-form-item>

        <el-form-item :label="$t('webContent.label4')" prop="contentInfo" style="width:700px;">
          <tinymce v-model="temp.contentInfo" :height="300" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">  {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false" @close="closeDialog">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('webContent.label1')" prop="contentTitle">
          <el-input v-model="temp.contentTitle" />
        </el-form-item>

        <el-form-item :label="$t('webContent.label4')" prop="contentInfo" style="width:700px;">
          <tinymce v-model="temp.contentInfo" :height="300" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false">  {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import Tinymce from '@/components/Tinymce'
import { fetchList, fetchWebContent, createWebContent, updateWebContent, updateIsAvailable, removeWebContent } from '@/api/webContent'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'WebContentTable',
  components: { Pagination, Tinymce },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        contentTitle: undefined,
        contentType: undefined
      },
      temp: {
        id: undefined,
        contentTitle: '',
        contentType: '',
        contentInfo: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        contentTitle: [
          { required: true, message: 'The content title cannot be empty', trigger: 'change' },,
        ],
        contentType: [
          { required: true, message: 'Content type cannot be empty', trigger: 'change' },,
        ],
        contentInfo: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        contentTitle: '',
        contentType: '',
        contentInfo: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.temp.contentInfo = ''
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      if (this.temp.contentType === '' || this.temp.contentType === null) {
        this.temp.contentType = 2
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createWebContent(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.temp.contentInfo = ''
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 }, closeDialog(row) {
      this.$router.replace({
        path: '/permission/webContent/',
        query: {
							  t: Date.now()
        }
						  })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateWebContent(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.temp.contentInfo = ''
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.dialogFormEditVisible = true
      this.temp = Object.assign({}, row)
      this.$nextTick(() => {
        tinymce.init(row)
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeWebContent(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

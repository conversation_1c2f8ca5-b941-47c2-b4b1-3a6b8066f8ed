import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */
import componentsRouter from './modules/components'
import chartsRouter from './modules/charts'
import tableRouter from './modules/table'
import nestedRouter from './modules/nested'

/* Settings */
import settings from '@/settings'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 *
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 * 所有权限都可以访问的路由
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/logout',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'dashboard',
        meta: { title: 'dashboard', icon: 'dashboard', noCache: true, affix: true }
      }
    ]
  }
]

/**
 * 订单管理路由配置
 */
const orderManagerRoute = {
  path: '/orderManager',
  component: Layout,
  redirect: '/orderManager',
  alwaysShow: true,
  name: 'orderManager',
  meta: {
    title: 'orderManager',
    icon: 'documentation',
  },
  children: [{
    path: 'orderList',
    component: () => import('@/views/orderInfo/orderInfo'),
    name: 'OrderList',
    meta: {
      title: 'orderList'
    }
  },{
    path: 'teamist',
    component: () => import('@/views/orderInfo/teamOrderInfo'),
    name: 'TeamList',
    meta: {
      title: 'teamist'
    }
  }]
}

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 * 需要根据用户角色动态加载的路由
 */
export const asyncRoutes = [

  // 系统管理菜单
  {
    path: '/permission',
    component: Layout,
    redirect: '/permission',
    alwaysShow: false, // will always show the root menu
    name: 'Permission',
    meta: {
      title: 'permission',
      icon: 'documentation',
      roles: ['role:list', 'user:list', 'wangzhanxinxi', 'fuwuqishezhi', 'youxiangshezhi', 'gonggaoguanli', 'caozuorizhi', 'yonghubiaoqian'] // you can set roles in root nav
    },
    children: [

      {
					  path: 'role',
					  component: () => import('@/views/permission/role'), // 角色管理
					  name: 'RolePermission',
					  meta: {
					    title: 'rolePermission',
					    roles: ['role:list']
					  }
      },
      {
				  path: 'user',
				  component: () => import('@/views/permission/user'), // 管理员管理
				  name: 'UserPermission',
				  meta: {
				    title: 'userPermission',
				    roles: ['user:list']
				  }
      },
      {
				  path: 'companyInfo',
				  component: () => import('@/views/companyInfo/companyInfo'), // 网站信息
				  name: 'CompanyInfo',
				  meta: {
				    title: 'companyInfo',
				    roles: ['wangzhanxinxi']
				  }
      },

      {
      	  path: 'webContent',
      	  component: () => import('@/views/webContent/webContent'), // 公告管理
      	  name: 'WebContent',
      	  meta: {
      	    title: 'webContent',
				    roles: ['gonggaoguanli']
      	  }
      },

      {
      	  path: 'userTag',
      	  component: () => import('@/views/userTag/userTag'), // 用户标签
      	  name: 'UserTag',
      	  meta: {
      	    title: 'userTag',
				    roles: ['yonghubiaoqian']
      	  }
      }, {
      	  path: 'operLog',
      	  component: () => import('@/views/operLog/operLog'), // 操作日志
      	  name: 'OperLog',
      	  meta: {
      	    title: 'operLog',
				    roles: ['caozuorizhi']
      	  }
      }

    ]
  }, {
    path: '/tradeSetting',
    component: Layout,
    redirect: '/tradeSetting',
    alwaysShow: true,
    name: 'tradeSetting',
    meta: {
      title: 'tradeSetting',
      icon: 'documentation',
      roles: ['huilvshezhi', 'gangganshezhi', 'jiaoyipinzhong', 'yonghujiaoyhizu', 'zhanghaoleixing', 'fanyongguizezu']
    },
    children: [{
      	  path: 'lever',
      	  component: () => import('@/views/lever/lever'), // 杠杆设置
      	  name: 'Lever',
      	  meta: {
      	    title: 'lever',
				    roles: ['gangganshezhi']
      	  }
    }, {
      	  path: 'tradeProd',
      	  component: () => import('@/views/tradeProd/tradeProd'), // 交易品种
      	  name: 'TradeProd',
      	  meta: {
      	    title: 'tradeProd',
				    roles: ['jiaoyipinzhong']
      	  }
    }, {
      	  path: 'userGroup',
      	  component: () => import('@/views/userGroup/userGroup'), // 用户组
      	  name: 'UserGroup',
      	  meta: {
      	    title: 'userGroup',
				    roles: ['yonghujiaoyhizu']
      	  }
    },
    {
      	  path: 'accountType',
      	  component: () => import('@/views/accountType/accountType'), // 账号类型
      	  name: 'AccountType',
      	  meta: {
      	    title: 'accountType',
				    roles: ['zhanghaoleixing']
      	  }
    }, {
      	  path: 'rekebackRule',
      	  component: () => import('@/views/rekebackRule/rekebackRule'), // 返佣规则组
      	  name: 'RekebackRule',
      	  meta: {
      	    title: 'rekebackRule',
				    roles: ['fanyongguizezu']
      	  }
    }, {

  		    path: 'addRekebackRule',
  		    component: () => import('@/views/rekebackRule/rekebackRule_add'),
  		    name: 'AddRekebackRule',
  		    meta: {
  		      title: 'addRekebackRule'
  		    }, hidden: true

  		}, {

  		    path: 'editRekebackRule/:id(\\d+)',
  		    component: () => import('@/views/rekebackRule/rekebackRule_edit'),
  		    name: 'EditRekebackRule',
  		    meta: {
  		      title: 'editRekebackRule'
  		    }, hidden: true

  		}, {

  		    path: 'copyRekebackRule/:id(\\d+)',
  		    component: () => import('@/views/rekebackRule/rekebackRule_copy'),
  		    name: 'CopyRekebackRule',
  		    meta: {
  		      title: 'copyRekebackRule'
  		    }, hidden: true

  		}
    ]
  },
  {
    path: '/deposit',
    component: Layout,
    redirect: '/deposit',
    alwaysShow: true,
    name: 'deposit',
    meta: {
      title: 'deposit',
      icon: 'documentation',
      roles: ['rujinpeizhi', 'rujinzhanghhu', 'sanfangzhifu']
    },
    children: [
      {
        path: 'depositBank',
        component: () => import('@/views/depositBank/depositBank'),
        name: 'DepositBank',
        meta: {
          title: 'deposit',
				    roles: ['rujinzhanghhu']
        }
      }] },
  {
    path: '/withdrawal',
    component: Layout,
    redirect: '/withdrawal',
    alwaysShow: true,
    name: 'withdrawal',
    meta: {
      title: 'withdrawal',
      icon: 'documentation',
      roles: ['chujinpeizhi', 'chujinzhanghu']
    },
    children: [
    {
      path: 'withdrawalBank',
      component: () => import('@/views/withdrawalBank/withdrawalBank'),
      name: 'WithdrawalBank',
      meta: {
        title: 'withdrawal',
				    roles: ['chujinzhanghu']
      }
    }]
  },
  {
    path: '/auditManager',
    component: Layout,
    redirect: '/auditManager',
    alwaysShow: true,
    name: 'auditManager',
    meta: {
      title: 'auditManager',
      icon: 'documentation',
      roles: ['yonghushenpi', 'rujinshenpi', 'chujinshennpi', 'tongmingzhanghao', 'tongmingzhuanzhang', 'zhifushenpi', 'zhifushibai']
    },
    children: [{
      path: 'auditUser',
      component: () => import('@/views/userInfo/userInfo_audit'),
      name: 'AuditUser',
      meta: {
        title: 'auditUser',
				    roles: ['yonghushenpi']
      }
    }, {
      path: 'auditAccount',
      component: () => import('@/views/tradeAccount/tradeAccount_audit'),
      name: 'AuditUser',
      meta: {
        title: 'auditAccount',
				    roles: ['tongmingzhanghao']
      }
    }, {
      path: 'depositAudit',
      component: () => import('@/views/fundInfo/fundInfo_deposit'),
      name: 'DepositAudit',
      meta: {
        title: 'depositAudit',
				    roles: ['rujinshenpi']
      }
    }, {
      path: 'withdrawalAudit',
      component: () => import('@/views/fundInfo/fundInfo_withdraw'),
      name: 'WithdrawalAudit',
      meta: {
        title: 'withdrawalAudit',
				    roles: ['chujinshennpi']
      }
    }, /*    ,{
      path: 'withdrawalAudit2',
      component: () => import('@/views/fundInfo/fundInfo_withdraw2'),
      name: 'WithdrawalAudit2',
      meta: {
        title: 'withdrawalAudit2',
				    roles: ['zhifushenpi']
      }
    },{
      path: 'withdrawalAudit3',
      component: () => import('@/views/fundInfo/fundInfo_withdraw3'),
      name: 'WithdrawalAudit3',
      meta: {
        title: 'withdrawalAudit3',
				    roles: ['zhifushibai']
      }
    }*/

    {
      path: 'auditTransfer',
      component: () => import('@/views/transferInfo/transferInfo_audit'),
      name: 'AuditUser',
      meta: {
        title: 'auditTransfer',
				    roles: ['tongmingzhuanzhang']
      }
    }

    ]
  },
  {
    path: '/userManager',
    component: Layout,
    redirect: '/userManager',
    alwaysShow: true,
    name: 'userManager',
    meta: {
      title: 'userManager',
      icon: 'documentation',
      roles: ['dailiyonghu', 'crmyonghu', 'jiaoyiyonghu', 'dailiyeji', 'crmyonghuchakan', 'jiaoyiyonghuchakan']
    },
    children: [{
      path: 'userList',
      component: () => import('@/views/permission/user2'),
      name: 'userList',
      meta: {
        title: 'userList',
				    roles: ['dailiyonghu']
      }
    }, {
      path: 'agentAchievement',
      component: () => import('@/views/permission/user3'),
      name: 'userList',
      meta: {
        title: 'agentAchievement',
				    roles: ['dailiyeji']
      }
    }, {
      path: 'crmUserList',
      component: () => import('@/views/userInfo/userInfo'),
      name: 'CrmUserList',
      meta: {
        title: 'crmUserList',
				    roles: ['crmyonghu']
      }
    },

    /* {
      path: 'crmUserListquery',
      component: () => import('@/views/userInfo/userInfo2'),
      name: 'CrmUserList',
      meta: {
        title: 'crmUserListquery',
				    roles: ['crmyonghuchakan']
      }
    },
    */
    {
      path: 'tradeUserList',
      component: () => import('@/views/tradeAccount/tradeAccount'),
      name: 'TradeUserList',
      meta: {
        title: 'tradeUserList',
				    roles: ['jiaoyiyonghu']
      }
    }
    /* {
      path: 'tradeUserListquery',
      component: () => import('@/views/tradeAccount/tradeAccount2'),
      name: 'TradeUserList',
      meta: {
        title: 'tradeUserListquery',
				    roles: ['jiaoyiyonghuchakan']
      }
    }
      */
    ]
  }, {
    path: '/financeManager',
    component: Layout,
    redirect: '/financeManager',
    alwaysShow: true,
    name: 'financeManager',
    meta: {
      title: 'financeManager',
      icon: 'documentation',
      roles: ['rujinjilu', 'chujinjilu', 'zhuanzhangjilu', 'yongjinjilu', 'rujindaiqueren', 'chujindaiqueren']
    },
    children: [{
      path: 'depositList',
      component: () => import('@/views/fundInfo/depositList'),
      name: 'DepositList',
      meta: {
        title: 'depositList',
				    roles: ['rujinjilu']
      }
    }, {
      path: 'withdrawalList',
      component: () => import('@/views/fundInfo/withdrawList'),
      name: 'WithdrawalList',
      meta: {
        title: 'withdrawalList',
				    roles: ['chujinjilu']
      }
    },
    /*
    {
      path: 'depositList_undo',
      component: () => import('@/views/fundInfo/depositList_undo'),
      name: 'DepositList',
      meta: {
        title: 'label3',
				    roles: ['rujindaiqueren']
      }
    },

    {
      path: 'withdrawalList_undo',
      component: () => import('@/views/fundInfo/withdrawList_undo'),
      name: 'WithdrawalList',
      meta: {
        title: 'label4',
				    roles: ['chujindaiqueren']
      }
    },  */{
      path: 'transferList',
      component: () => import('@/views/transferInfo/transferInfo'),
      name: 'AuditUser',
      meta: {
        title: 'transferList',
				    roles: ['zhuanzhangjilu']
      }
    },

    {
      path: 'rekebackList',
      component: () => import('@/views/reckbackInfo/reckbackInfo'),
      name: 'AuditUser',
      meta: {
        title: 'rekebackList',
				    roles: ['yongjinjilu']
      }
    }]
  },

  //start of orderManager only for HG1
  // 订单管理路由根据settings.js中的isShowOrderManagement设置动态添加
  
 //end of orderManager
  
  // 根据设置条件性地添加订单管理路由
  ...(settings.isShowOrderManagement ? [orderManagerRoute] : []),

  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router

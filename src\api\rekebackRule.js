import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/rekebackRule/list',
    method: 'post',
    params: query
  })
}

export function fetchRekebackRule(id) {
  return request({
    url: '/rekebackRule/detail',
    method: 'get',
    params: { id }
  })
}

export function createRekebackRule(data) {
  return request({
    url: '/rekebackRule/add',
    method: 'post',
    data
  })
}

export function updateRekebackRule(data) {
  return request({
    url: '/rekebackRule/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/rekebackRule/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeRekebackRule(id) {
  return request({
    url: '/rekebackRule/remove',
    method: 'get',
    params: { id }
  })
}
export function listshowRule() {
  return request({
    url: '/rekebackRule/listshow',
    method: 'post'
  })
}

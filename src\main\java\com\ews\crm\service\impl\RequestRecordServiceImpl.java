package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.RequestRecord;
import com.ews.crm.repository.RequestRecordRepository;
import com.ews.crm.service.RequestRecordService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class RequestRecordServiceImpl implements RequestRecordService 
{
	@Autowired
	private RequestRecordRepository requestRecordRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<RequestRecord> findAll(Integer page, Integer size,String sortName,String sortOrder, RequestRecord requestRecord) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<RequestRecord> pages = requestRecordRepository.findAll(new Specification<RequestRecord>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<RequestRecord> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(requestRecord.getOrderId())) { 
                predicates.add(criteriaBuilder.equal(root.get("orderId").as(String.class),requestRecord.getOrderId()));
             }
             if(!StringUtils.isEmpty(requestRecord.getSignStr())) { 
                predicates.add(criteriaBuilder.equal(root.get("signStr").as(String.class),requestRecord.getSignStr()));
             }
             if(!StringUtils.isEmpty(requestRecord.getAccount())) { 
                predicates.add(criteriaBuilder.equal(root.get("account").as(String.class),requestRecord.getAccount()));
             }
             if(!StringUtils.isEmpty(requestRecord.getStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("status").as(Integer.class), requestRecord.getStatus()));
             }
             if(!StringUtils.isEmpty(requestRecord.getThirdNo())) { 
                predicates.add(criteriaBuilder.like(root.get("thirdNo").as(String.class),"%"+requestRecord.getThirdNo()+"%"));
             }
             if(!StringUtils.isEmpty(requestRecord.getDespoitId())) { 
                predicates.add(criteriaBuilder.equal(root.get("despoitId").as(Long.class), requestRecord.getDespoitId()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public RequestRecord findById(Long id) {
      if(id == null) {
      	return null;
      }
      Optional<RequestRecord> op = requestRecordRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.requestRecordRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         RequestRecord old = requestRecordRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 requestRecordRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 requestRecordRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.requestRecordRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(RequestRecord requestRecord) {
       
        Result vr = new Result();
		try {
        	if (requestRecord.getId()== null) {
            	requestRecord.setGmtCreate(new Date());
            	requestRecord.setGmtModified(new Date());
            	requestRecord.setIsDeleted(0);
            	if(requestRecord.getIsAvailable() == null) {
            		requestRecord.setIsAvailable(1);
            	}
	    	} else {
            	requestRecord.setGmtModified(new Date());
        	}
            requestRecordRepository.save(requestRecord);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



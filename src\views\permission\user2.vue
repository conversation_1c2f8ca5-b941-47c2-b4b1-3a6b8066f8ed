<template>
  <div class="app-container">
    <div>
      <el-tree
        :props="props1"
        :load="loadNode1"
        lazy
        @node-click="handleNodeClick"
      />
    </div>
    <div class="filter-container">
      <el-input v-model="listQuery.username" :placeholder="$t('userTable.userName')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.nickName" :placeholder="$t('userTable.nickName')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.parent_ID" :placeholder="$t('user2.label1')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <!--
      <el-select v-model="listQuery.type" :placeholder="$t('table.type')" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in calendarTypeOptions" :key="item.key" :label="item.display_name+'('+item.key+')'" :value="item.key" />
      </el-select>
      <el-select v-model="listQuery.sort" style="width: 140px" class="filter-item" @change="handleFilter">
        <el-option v-for="item in sortOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
			-->

      <el-select v-model="listQuery.isAvailable" style="width: 100px" :placeholder="$t('userTable.status')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        {{ $t('userTable.search') }}
      </el-button>
      <el-button v-waves class="filter-item" type="primary" @click="handleExport">导出报表</el-button>

    </div>

    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <!--id序号-->
      <el-table-column :label="$t('userTable.number')" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.sort }}</span>
        </template>
      </el-table-column>
      <!--用户名-->
      <el-table-column :label="$t('userTable.userName')" width="200px" align="center" sortable="custom" prop="username">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <!--姓名-->
      <el-table-column :label="$t('userTable.nickName')" width="200px" align="center" sortable="custom" prop="nickName">
        <template slot-scope="scope">
          <span>{{ scope.row.nickName }}</span>
        </template>
      </el-table-column>

      <!--联系电话-->
      <el-table-column :label="$t('userTable.mobile')" width="200px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>

      <!--创建时间-->
      <el-table-column :label="$t('userTable.createDate')" prop="gmtCreate" sortable="custom" width="200px" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreateDateLong | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('user2.label2')" width="200px" align="center" sortable="custom" prop="storeId">
        <template slot-scope="scope">
          <span>{{ scope.row.storeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('user2.label3')" width="200px" align="center" prop="storeName">
        <template slot-scope="scope">
          <span>{{ scope.row.storeName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('user2.label4')" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.saleSerial }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!--分页显示-->
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>

<script>

import { fetchList2, fetchUser, createUser, updateUser, updateIsAvailable, resetPassword, removeUser, fetchRoleList, fetchUserRoleList, fetchSaleMgn, bindCrmUser, fetchUserTreeList, exportUserExcel } from '@/api/user'
import { listshowRule } from '@/api/rekebackRule'
// import {fetchDepartmentList} from '@/api/department'
// import {fetchStoreList} from '@//api/store'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth.js'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'

import Pagination from '@/components/Pagination' // secondary package based on el-pagination

var roles = []
var count = 0
var checkedRoles = []
export default {
  name: 'UserTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      uploadUrl: 'http://localhost:8099/user/uploadUserIcon',
      tableKey: 0,
      list: null,

      total: 0,
      listLoading: true,

      listQuery: {
        page: 1,
        limit: 10,
        reId: undefined,
        parent_ID: undefined,
        username: undefined,
        nickName: undefined,
        sort: '-gmtCreate',
        isAvailable: undefined,
        gmtCreateBegin: undefined,
        gmtCreateEnd: undefined
      },
      uploadData: {
        id: undefined,
        type: 1
      },
      myHeaders: {
				 'X-Token': getToken()
      },
      props1: {
        label: 'name',
        children: 'zones',
        isLeaf: 'leaf',
        value: 0
      },
      dqryType: undefined,
      valueGmtGreate: undefined,
      isIndeterminate: false,
      checkAll: false,
      checkedRoles: checkedRoles,
      roles: roles,
      departments: [],
      stores: [],
      saleMgns: [],
		  options: [{
        value: 1,
        label: this.$t('permission.enable')
      }, {
        value: 0,
        label: this.$t('permission.disable')
      }],
      roleTypes: [{
				     value: 0,
				     label: '后台管理员'
				  }, {
				    value: 1,
				    label: '代理用户'
				  }, {
				    value: 2,
				    label: '销售'
				  }, {
				    value: 3,
				    label: '销售经理'
				  }, {
				    value: 4,
				    label: '销售总监'
				  }],
      pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 表单数据默认绑定
      temp: {
        userId: undefined,
        departmentId: '',
        storeId: '',
        username: '',
        nickName: '',
        mobile: '',
        password: '',
        isAvailable: '',
        userIcon: '',
        roleType: '',
        crm_username: '',
        reId: '',
        roles: [],
        rekebackRule: [],
        ruleList: [],
        enclosureId: undefined
      },
      option1s: [],
      rekebackRule2: [],
      dialogFormVisible2: false,
      dialogFormVisible: false, // 添加弹窗
      dialogFormEditVisible: false, // 编辑弹窗
      dialogFormResetPasswordVisible: false, // 重置密码弹窗

      rules: {// 验证消息
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
          { min: 3, max: 70, message: '请输入 3 到 70个字符', trigger: 'change' }
        ],

        nickName: [
          { required: true, message: '请输入员工姓名', trigger: 'change' },
          { max: 30, message: '最多输入 30 个字符', trigger: 'change' }
        ],

        roleType: [
          { required: true, message: '请选择用户类型', trigger: 'change' }
        ],
        mobile: [
          { required: true, message: '请输入电话号码', trigger: 'change' },
          { max: 30, message: '最多输入 30 个字符', trigger: 'change' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'change' },
          { min: 6, max: 30, message: '请输入 6 到 20个字符', trigger: 'change' }
        ],
        isAvailable: [
          { required: true, message: '请选择用户状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
     	if (response.code == 20000) {
        this.dqryType = response.data.roleType
     	} else {
     	}
    })

    this.getList()
    this.getRoles()

    this.option1s = []
    listshowRule().then(response => {
      var datas4 = response.data.items
         	for (var j = 0; j < datas4.length; j++) {
         		var id = datas4[j].id
        console.log(id)
         		var name = datas4[j].ruleName
         		var per = []
         		per.value = id
         		per.label = name
        this.option1s.push(per)
      }
    })
  },
  methods: {

    async getDepartments() {
			 const res = await fetchDepartmentList()
			 this.departments = []
			 var data = res.data
			 	for (var j = 0; j < data.length; j++) {
			 		var id = data[j].id
        var name = data[j].departmentName
        var per = []
        per.value = id
        per.label = name
			 		this.departments.push(per)
			 }
    }, fmtUserType(row, column) {
      if (row.roleType == 0) {
        return 'Administrator'
      } else if (row.roleType == 1) {
        return 'Agent/IB'
      } else if (row.roleType == 2) {
        return 'Sales'
      } else if (row.roleType == 3) {
        return 'Sales manager'
      } else if (row.roleType == 4) {
        return 'Sales director'
      } else if (row.roleType == 999) {
					   return 'Super administrator'
      }
    },

    async getStores() {
			 const res = await fetchStoreList()
			 this.stores = []
			 var data = res.data
			 	for (var j = 0; j < data.length; j++) {
			 		var id = data[j].id
        var name = data[j].name
        var per = []
        per.value = id
        per.label = name
			 		this.stores.push(per)
			 }
    },
    handleCheckAllChange(val) {
      checkedRoles = []
      if (val == true) {
        for (var i = 0; i < roles.length; i++) {
          checkedRoles.push(roles[i].roleId)
        }
      }
      this.checkedRoles = checkedRoles
		    this.isIndeterminate = false
      this.checkAll = val
		  },
		  handleCheckedCitiesChange(value) {
		    const checkedCount = value.length
		    this.checkAll = checkedCount === count
		    this.isIndeterminate = checkedCount > 0 && checkedCount < count
      checkedRoles = value
      this.checkedRoles = checkedRoles
		  },
    handleIsAvailableChange(row) {
				 updateIsAvailable(row.userId, row.isAvailable).then(response => {
					 if (response.code != 20000) {
							 this.$message({
							  message: response.message,
							  type: 'error'
          })
					 }
      })
    }, handleExport() {
					 exportUserExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    async getRoles() {
		  const res = await fetchRoleList()
      roles = []
      // 所有权限的数据集合
				 var data = res.data
      for (var j = 0; j < data.length; j++) {
        var per = data[j]
        roles.push(per)
      }
      this.roles = roles
      // 数据总数
      count = this.roles.length
    },
    handleAvatarSuccess(res, file) {
      console.log('上传成功 :' + res.data.id)
      console.log('==========:' + res.data.endixUrl)
      // this.imageUrl = URL.createObjectURL(file.raw);
      this.temp.userIcon = res.data.endixUrl
      this.temp.enclosureId = res.data.id
    },
    beforeAvatarUpload(file) {
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2

      /*
			if (!isJPG) {
				this.$message.error('上传头像图片只能是 JPG 格式!');
				return false;
			}
			* */
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList2(this.listQuery).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        this.listLoading = false// 关闭加载动画
        // Just to simulate the time of the request
      })
    },
    handleFilter() {
      this.listQuery.page = 1

			 if (this.valueGmtGreate) {
				 this.listQuery.gmtCreateBegin = parseTime(new Date(this.valueGmtGreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
				 this.listQuery.gmtCreateEnd = parseTime(new Date(this.valueGmtGreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
			 } else {
				 	 this.listQuery.gmtCreateBegin = undefined
					 this.listQuery.gmtCreateEnd = undefined
			 }
      this.getList()
    },

    getSaleMgn(query) {
					 this.saleMgns = []

						  fetchSaleMgn().then(res => {
							 this.saleMgns = []
							 var data = res.data.items
							 	for (var j = 0; j < data.length; j++) {
							 		var id = data[j].userId
							 		var name = data[j].nickName
							 		var per = []
							 		per.value = id
							 		per.label = name
							 		this.saleMgns.push(per)
							 }
      })
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
      }

      if (prop === 'username') {
        this.sortByUserName(order)
      }
      if (prop === 'nickName') {
        this.sortByNickName(order)
      }
      if (prop === 'storeId') {
        this.sortByStoreId(order)
      }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    sortByUserName(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+username'
      } else {
        this.listQuery.sort = '-username'
      }
      this.handleFilter()
    },
    sortByNickName(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+nickName'
      } else {
        this.listQuery.sort = '-nickName'
      }
      this.handleFilter()
    },
    sortByStoreId(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+storeId'
      } else {
        this.listQuery.sort = '-storeId'
      }
      this.handleFilter()
    },
    // 还原表单 temp
    resetTemp() {
      this.temp = {
        userId: undefined,
			 departmentId: undefined,
			 storeId: undefined,
        username: '',
        nickName: '',
        mobile: '',
        password: '',
        isAvailable: '',
			 userIcon: '',
			 	roleType: '',
			 reId: '',
			 roles: [],
        rekebackRule: [],
        ruleList: [],
			 enclosureId: undefined
      }
    },
    // 重置密码页面
    handleResetPassword(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogFormResetPasswordVisible = true
      this.$nextTick(() => {
			  this.$refs['dataForm'].clearValidate()
      })
    },
    resetPassword() {
			 this.$refs['dataPassForm'].validate((valid) => {
			  if (valid) {
          const tempData = Object.assign({}, this.temp)
			    resetPassword(tempData.userId, tempData.password).then(result => {
            if (result.code == 20000) {
							 this.$notify({
							  title: 'success',
							  message: 'update success',
							  type: 'success',
							  duration: 2000
              })
              this.dialogFormResetPasswordVisible = false
            } else {
              this.$message.error(response.message)
            }
			    })
			  }
      })
    },
    // 新增数据页面
    handleCreate() {
      this.resetTemp()
      checkedRoles = []
      this.checkedRoles = []
      this.isIndeterminate = false
      this.checkAll = false
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, // 新增数据页面
    handleSub(row) {
      this.resetTemp()
      checkedRoles = []
      this.checkedRoles = []
      this.isIndeterminate = false
      this.checkAll = false
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.reId = row.userId
      console.log(row.userId + '    ' + this.temp.reId)
    }, handleCRM(row) {
      this.dialogFormVisible2 = true
      this.temp.userId = row.userId
    }, ep_crm() {
      bindCrmUser(this.temp).then(result => {
        // this.list.unshift(this.temp)
      	if (result.code == 20000) {
      		 this.$notify({
      		  title: 'success',
      		  message: '分配成功',
      		  type: 'success',
      		  duration: 2000
      		})
      		this.dialogFormVisible2 = false
      	} else {
      		this.$message.error(response.msg)
      	}
      })
    },
    // 保存数据
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          		this.temp.roles = this.checkedRoles
          createUser(this.temp).then(result => {
            // this.list.unshift(this.temp)
            if (result.code == 20000) {
							 this.$notify({
							  title: 'success',
							  message: '创建成功',
							  type: 'success',
							  duration: 2000
              })
              this.getList()
              this.dialogFormVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
    },
    // 编辑数据页面
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.dialogFormVisible = false
      this.dialogFormEditVisible = true

      this.isIndeterminate = false
      this.checkAll = false
      this.temp.storeId = row.storeId

      console.log(this.temp.storeId + ':' + this.temp.departmentId)
      console.log()

      // 选中用户参数
      fetchUserRoleList(this.temp.userId).then(result => {
        if (result.code == 20000) {
          checkedRoles = []
          this.checkedRoles = []
          for (var j = 0; j < result.data.length; j++) {
            var per = result.data[j].roleId
            checkedRoles.push(per)
          }

          this.checkedRoles = checkedRoles
          if (this.checkedRoles.length > 0) {
            if (this.checkedRoles.length == this.roles.length) {
              this.checkAll = true
            } else {
              this.isIndeterminate = true
            }
          }
        } else {
          this.$message.error(result.msg)
        }
      })

      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })

      if (row.ruleList != null) {
        this.rekebackRule2 = []
        var datas13 = row.ruleList
        for (var j = 0; j < datas13.length; j++) {
          this.rekebackRule2.push(datas13[j].ruleId)
        }
      }
    },
    // 更新数据
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.roles = this.checkedRoles
          const tempData = Object.assign({}, this.temp)

          tempData.timestamp = +new Date(tempData.timestamp) // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
          tempData.rekebackRule = this.rekebackRule2
          updateUser(tempData).then(() => {
            this.$notify({
              title: 'success',
              	message: 'update success',
              type: 'success',
              duration: 2000
            })
            this.dialogFormEditVisible = false
            this.getList()
          })
        }
      })
    }, loadNode1(node, resolve) {
      if (node.level === 0) {
        return resolve([{ name: 'AllAgent' }])
      }
      fetchUserTreeList(node.label).then(result => {
        return resolve(result.data)
      })
    }, handleNodeClick(data) {
      this.listQuery.reId = data.value
      this.getList()
    },
    // 删除数据
    handleDelete(row) {
      // 提示用户是否确定删除
			 this.$confirm('您确定要删除该用户吗?', 'INFO', {
			  confirmButtonText: 'OK',
			  cancelButtonText: 'CANCEL',
			  type: 'warning'
      })
			  .then(async() => {
			    await removeUser(row.userId).then(result => {
            if (result.code == 20000) {
											 	this.$notify({
											 		title: 'success',
											 		message: 'delete success',
											 		type: 'success',
											 		duration: 2000
											 	})
											 const index = this.list.indexOf(row)
											 this.list.splice(index, 1)
            } else {
											 this.$message.error(result.msg)
            }
          })
			  })
			  .catch(err => { console.error(err) })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>

<style>
	.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>


package com.ews.crm.controller;

import java.io.File;
import java.net.URI;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.ews.common.EncryptUtils;
import com.ews.HttpClient;
import com.ews.common.DateUtil;
import com.ews.common.LoginResult;
import com.ews.common.MyWebsocketClient4TradeProd;
import com.ews.common.MyWebsocketClient4UserGroup;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.common.Result;

import com.alibaba.fastjson.JSONObject; 
import com.alibaba.fastjson.JSONArray;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.UserGroupService;
import com.wx.core.util.HttpClientUtil;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeProd;
import com.ews.crm.entity.UserGroup;



@RestController
@RequestMapping("/admin/userGroup")
public class UserGroupController {
	@Autowired
	private UserGroupService userGroupService;

	@Autowired
	private ServerSettingService serverSettingService;


   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	UserGroup query  = new UserGroup();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "asc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
               	query.setGroupName(request.getParameter("groupName").trim());
        	}
        	Page<UserGroup> pages = userGroupService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<UserGroup> userGroups = pages.getContent();
        	for(int i=0;i<userGroups.size();i++) {
        		UserGroup entity  = userGroups.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", userGroups);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			UserGroup userGroup = new UserGroup();
        	userGroup.setGroupName(data.getString("groupName"));
        	userGroup.setGroupDescribe(data.getString("groupDescribe"));
			Result re = userGroupService.saveOrUpdate(userGroup);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(userGroup);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			UserGroup userGroup = this.userGroupService.findById(id);
				if (userGroup != null) {
					userGroup.setGroupName(data.getString("groupName"));
					userGroup.setGroupDescribe(data.getString("groupDescribe"));
					Result re = userGroupService.saveOrUpdate(userGroup);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = userGroupService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = userGroupService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	
	    @PostMapping("/listshow")
	    public ResponseData listshow(HttpServletRequest request) {
	    	try {
	        	UserGroup query  = new UserGroup();
	        	Page<UserGroup> pages = userGroupService.findAll(0, 1000, "id", "asc", query);
	        	JSONObject datas = new JSONObject();
	        	List<UserGroup> userGroups = pages.getContent();
	        	datas.put("items", userGroups);
	        	return ResponseDataUtil.buildSuccess(datas);
	    	} catch (Exception e) {
	        	e.printStackTrace();
	        	return ResponseDataUtil.buildError(e.getMessage());
	    	}
		}
	    
		@GetMapping("/synchroGroup")
		public ResponseData synchroGroup( HttpServletRequest request) {
			 try {
				 ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
			
				 
				 
				       HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
				     
				      if(httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api")) {
				    	  org.json.JSONObject json=httpClient.getGroupTotal();
				    	  if(json.getString("retcode").equals("0 Done")) {
				    		  org.json.JSONObject json1=json.getJSONObject("answer");
				    		  if(json1.get("total")!=null&&json1.getInt("total")>0) {
				    			  for(int i=0;i<json1.getInt("total");i++) {
				    				  org.json.JSONObject json2=httpClient.getGroupInfoByIndex(i);
				    				  if(json2.getString("retcode").equals("0 Done")) {
							    		  org.json.JSONObject json3=json2.getJSONObject("answer");
							    		  UserGroup ug_query=new UserGroup();
										  ug_query.setGroupName(json3.getString("Group"));
										  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
										  if(page.getContent().size()<=0) {
											UserGroup ug_t=new UserGroup();
											ug_t.setGroupName(json3.getString("Group"));
											ug_t.setBackup1(json3.getString("Currency"));
											this.userGroupService.saveOrUpdate(ug_t);
										 }
				    				  }
				    			  }
				    		  }
				    	  }
				      }
					}catch(Exception e) {
						System.out.println(e);
					}
			    return ResponseDataUtil.buildSuccess(); 
					
		}


}


package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.TradeProd;
import com.ews.crm.service.TradeProdService;


public class MyWebsocketClient4TradeProd extends WebSocketClient {
	
	
	
	public TradeProdService tradeProdService;
	
	
	public TradeProdService getTradeProdService() {
		return tradeProdService;
	}

	public void setTradeProdService(TradeProdService tradeProdService) {
		this.tradeProdService = tradeProdService;
	}

	public MyWebsocketClient4TradeProd(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
            		JSONArray ja=JSONArray.parseArray(jsStr.getString("symbolListInfos"));
					for(int m=0;m<ja.size();m++) {
						JSONObject ob=(JSONObject)ja.get(m);
						try {
							
						TradeProd tp_query=new TradeProd();
						tp_query.setProdName2(ob.getString("symbolname"));
						Page<TradeProd> tradeProd_page=this.tradeProdService.findAll(0,10,"id","asc", tp_query);
						if(tradeProd_page.getContent().size()<=0) {//没有查到交易品种则同步到库里
							TradeProd tradeProd=new TradeProd();
							tradeProd.setProdName(ob.getString("symbolname"));
							tradeProd.setProdDescribe(ob.getString("symboldes"));
							this.tradeProdService.saveOrUpdate(tradeProd);
						}
						}catch(Exception e) {
							System.out.println(e);
						}
                     }
				
			
		}
		new Thread(){
    		public void run(){
    			try {
    			Thread.sleep(60000L);
    			     close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

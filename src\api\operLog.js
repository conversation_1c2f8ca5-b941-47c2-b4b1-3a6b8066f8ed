import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/operLog/list',
    method: 'post',
    params: query
  })
}

export function fetchOperLog(id) {
  return request({
    url: '/operLog/detail',
    method: 'get',
    params: { id }
  })
}

export function createOperLog(data) {
  return request({
    url: '/operLog/add',
    method: 'post',
    data
  })
}

export function updateOperLog(data) {
  return request({
    url: '/operLog/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/operLog/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeOperLog(id) {
  return request({
    url: '/operLog/remove',
    method: 'get',
    params: { id }
  })
}


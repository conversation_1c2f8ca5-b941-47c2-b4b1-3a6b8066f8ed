{"name": "vue-element-admin", "version": "4.0.1", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "dev:linux": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "build:stage": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}, "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "dependencies": {"@toast-ui/editor": "^3.1.3", "@vue/cli-shared-utils": "^5.0.8", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "^3.23.3", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "^2.13.2", "file-saver": "2.0.1", "fuse.js": "3.4.4", "is-docker": "^3.0.0", "jquery": "^3.6.0", "js-cookie": "2.2.0", "jsonlint": "1.6.3", "jszip": "3.2.1", "markdown-it": "^13.0.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "0.0.2", "screenfull": "4.2.0", "showdown": "^1.9.1", "sortablejs": "1.8.4", "squire-rte": "^1.11.3", "to-mark": "^1.1.9", "tui-editor": "^1.4.10", "vue": "2.6.10", "vue-clipboard2": "^0.3.3", "vue-count-to": "1.0.13", "vue-i18n": "7.3.2", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "xlsx": "0.14.1"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "runjs": "^4.3.2", "sass": "^1.26.5", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}
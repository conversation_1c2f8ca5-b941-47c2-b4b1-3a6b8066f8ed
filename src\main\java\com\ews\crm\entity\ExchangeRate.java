package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "exchange_rate")
public class ExchangeRate implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *货币对
    **/
	@Column(name = "currency_pairs")
	protected  String currencyPairs;

    /**
    *买入价
    **/
	@Column(name = "purchase_price")
	protected  Double purchasePrice;

    /**
    *卖出价
    **/
	@Column(name = "sell_price")
	protected  Double sellPrice;

    /**
    *手续费   --当前汇率
    **/
	@Column(name = "fee")
	protected  Double fee;

    /**
    *backup1    --汇率计算形式      1按汇率定时调整    2按加点固定调整
    **/
	@Column(name = "backup1")
	protected  Integer backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  Integer backup2;

    /**
    *backup3 --资金账户类型    CASH  现金账户   DIGICCY 数字货币  
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4    PAY  是否第三方支付
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5     买入加点
    **/
	@Column(name = "backup5")
	protected  Double backup5;

    /**
    *backup6         卖出加点
    **/
	@Column(name = "backup6")
	protected  Double backup6;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getCurrencyPairs()
    {
        return currencyPairs;
    }
    public void setCurrencyPairs(String  currencyPairs)
    {
        this.currencyPairs = currencyPairs;
    }
    public Double  getPurchasePrice()
    {
        return purchasePrice;
    }
    public void setPurchasePrice(Double  purchasePrice)
    {
        this.purchasePrice = purchasePrice;
    }
    public Double  getSellPrice()
    {
        return sellPrice;
    }
    public void setSellPrice(Double  sellPrice)
    {
        this.sellPrice = sellPrice;
    }
    public Double  getFee()
    {
        return fee;
    }
    public void setFee(Double  fee)
    {
        this.fee = fee;
    }
    public Integer  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(Integer  backup1)
    {
        this.backup1 = backup1;
    }
    public Integer  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(Integer  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Double  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(Double  backup5)
    {
        this.backup5 = backup5;
    }
    public Double  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(Double  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.apiName" placeholder="接口名称" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.requestType" style="width: 120px" placeholder="请求类型" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in requestTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.signType" style="width: 120px" placeholder="签名方式" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in signTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="接口名称" prop="apiName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.apiName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求地址" prop="requestUrl" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.requestUrl }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button type="success" size="mini" @click="handleUpdate2(scope.row)">参数</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="接口名称" prop="apiName">
          <el-input v-model="temp.apiName" />
        </el-form-item>
        <el-form-item label="请求地址" prop="requestUrl">
          <el-input v-model="temp.requestUrl" />
        </el-form-item>
        <el-form-item label="请求类型" prop="requestType">
          <el-select v-model="temp.requestType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="requestType in requestTypes"
              :key="requestType.value"
              :label="requestType.label"
              :value="requestType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单前缀" prop="orderPrefix">
          <el-input v-model="temp.orderPrefix" />
        </el-form-item>
        <el-form-item label="签名方式" prop="signType">
          <el-select v-model="temp.signType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="signType in signTypes"
              :key="signType.value"
              :label="signType.label"
              :value="signType.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="接口名称" prop="apiName">
          <el-input v-model="temp.apiName" />
        </el-form-item>
        <el-form-item label="请求地址" prop="requestUrl">
          <el-input v-model="temp.requestUrl" />
        </el-form-item>
        <el-form-item label="请求类型" prop="requestType">
          <el-select v-model="temp.requestType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="requestType in requestTypes"
              :key="requestType.value"
              :label="requestType.label"
              :value="requestType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单前缀" prop="orderPrefix">
          <el-input v-model="temp.orderPrefix" />
        </el-form-item>
        <el-form-item label="签名方式" prop="signType">
          <el-select v-model="temp.signType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="signType in signTypes"
              :key="signType.value"
              :label="signType.label"
              :value="signType.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchThirdPartyPayment, createThirdPartyPayment, updateThirdPartyPayment, updateIsAvailable, removeThirdPartyPayment } from '@/api/thirdPartyPayment'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'ThirdPartyPaymentTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        apiName: undefined,
        requestUrl: undefined,
        requestType: undefined,
        signType: undefined
      },
      temp: {
        id: undefined,
        apiName: '',
        requestUrl: '',
        requestType: '',
        orderPrefix: '',
        signType: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 requestTypes: [
        {
          value: 1,
          label: '三方跳转'
        },
        {
          value: 2,
          label: '直接跳转'
        },
        {
          value: 3,
          label: '站外跳转'
        }
      ],
				 signTypes: [
        {
          value: 1,
          label: '方式1（ASCII_SHA1）'
        },
        {
          value: 2,
          label: '方式2（MD5）'
        },
        {
          value: 3,
          label: '方式3'
        },
        {
          value: 4,
          label: '方式4'
        },
        {
          value: 5,
          label: '方式5'
        }
      ],
      rules: {
        apiName: [
          { required: true, message: '接口名称不能为空', trigger: 'change' },,
        ],
        requestUrl: [
          { required: true, message: '请求地址不能为空', trigger: 'change' },,
        ],
        requestType: [
          { required: true, message: '请求类型不能为空', trigger: 'change' },,
        ],
        timeStr: [
        ],
        orderPrefix: [
        ],
        signType: [
          { required: true, message: '签名方式不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        apiName: '',
        requestUrl: '',
        requestType: '',
        orderPrefix: '',
        signType: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createThirdPartyPayment(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateThirdPartyPayment(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate2(row) {
      this.$router.push({ path: '/deposit/paymentParams/' + row.id })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeThirdPartyPayment(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

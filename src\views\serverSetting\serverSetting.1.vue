<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.platformType" style="width: 120px" placeholder="交易平台" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in platformTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.serverType" style="width: 120px" placeholder="服务器类型" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in serverTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易平台" prop="platformType" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.platformType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务器类型" prop="serverType" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.serverType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务器名称" prop="serverName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.serverName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="接口端口" prop="apiPort" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.apiPort }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="交易平台" prop="platformType">
          <el-select v-model="temp.platformType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="platformType in platformTypes"
              :key="platformType.value"
              :label="platformType.label"
              :value="platformType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器类型" prop="serverType">
          <el-select v-model="temp.serverType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="serverType in serverTypes"
              :key="serverType.value"
              :label="serverType.label"
              :value="serverType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器名称" prop="serverName">
          <el-input v-model="temp.serverName" />
        </el-form-item>
        <el-form-item label="接口地址" prop="apiAddress">
          <el-input v-model="temp.apiAddress" />
        </el-form-item>
        <el-form-item label="接口端口" prop="apiPort">
          <el-input v-model="temp.apiPort" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="交易平台" prop="platformType">
          <el-select v-model="temp.platformType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="platformType in platformTypes"
              :key="platformType.value"
              :label="platformType.label"
              :value="platformType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器类型" prop="serverType">
          <el-select v-model="temp.serverType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="serverType in serverTypes"
              :key="serverType.value"
              :label="serverType.label"
              :value="serverType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器名称" prop="serverName">
          <el-input v-model="temp.serverName" />
        </el-form-item>
        <el-form-item label="接口地址" prop="apiAddress">
          <el-input v-model="temp.apiAddress" />
        </el-form-item>
        <el-form-item label="接口端口" prop="apiPort">
          <el-input v-model="temp.apiPort" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchServerSetting, createServerSetting, updateServerSetting, updateIsAvailable, removeServerSetting } from '@/api/serverSetting'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'ServerSettingTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        platformType: undefined,
        serverType: undefined
      },
      temp: {
        id: undefined,
        platformType: '',
        serverType: '',
        serverName: '',
        apiAddress: '',
        apiPort: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 platformTypes: [
        {
          value: 1,
          label: 'MetaTrader 4'
        },
        {
          value: 2,
          label: 'MetaTrader 5'
        }
      ],
				 serverTypes: [
        {
          value: 1,
          label: 'Live'
        },
        {
          value: 2,
          label: 'Demo'
        }
      ],
      rules: {
        platformType: [
          { required: true, message: '交易平台不能为空', trigger: 'change' },,
        ],
        serverType: [
          { required: true, message: '服务器类型不能为空', trigger: 'change' },,
        ],
        serverName: [
          { required: true, message: '服务器名称不能为空', trigger: 'change' },,
        ],
        apiAddress: [
          { required: true, message: '接口地址不能为空', trigger: 'change' },,
        ],
        apiPort: [
          { required: true, message: '接口端口不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        platformType: '',
        serverType: '',
        serverName: '',
        apiAddress: '',
        apiPort: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createServerSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateServerSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeServerSetting(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/depositBank/list',
    method: 'post',
    params: query
  })
}

export function fetchDepositBank(id) {
  return request({
    url: '/depositBank/detail',
    method: 'get',
    params: { id }
  })
}

export function createDepositBank(data) {
  return request({
    url: '/depositBank/add',
    method: 'post',
    data
  })
}

export function updateDepositBank(data) {
  return request({
    url: '/depositBank/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/depositBank/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeDepositBank(id) {
  return request({
    url: '/depositBank/remove',
    method: 'get',
    params: { id }
  })
}


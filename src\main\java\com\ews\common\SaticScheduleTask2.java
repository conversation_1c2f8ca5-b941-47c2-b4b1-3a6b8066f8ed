package com.ews.common;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.ews.crm.controller.UserInfoController;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class SaticScheduleTask2 {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private UserService userService;
	@Autowired
	private ReckbackInfoService reckbackInfoService;
	
	
	/**
	 * 导入CRM数据
	 */
	/**/
    @Scheduled(fixedRate=********)
    private void configureTasks() {
    	
    	/*  
    	 System.out.println("import crmInfo begin");
    	List crmUserList=this.dataSqlService.getImportCrmUserList();
    	for(int i=0;i<crmUserList.size();i++) {
    		Object[]  obj=(Object[])crmUserList.get(i);
    		UserInfo query_userInfo=new UserInfo();
			query_userInfo.setUserName(obj[0].toString());
			Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);
			if(userInfo_page.getContent().size()<=0) {
	        UserInfo userInfo=new UserInfo();
			userInfo.setSurname(obj[10].toString());
			userInfo.setName(obj[9].toString());	
			
			userInfo.setTel(obj[3].toString());	
			userInfo.setUserName(obj[0].toString());
			userInfo.setIsAvailable(1);
			userInfo.setPassword(obj[8].toString());
		    userInfo.setFullname(userInfo.getName()+userInfo.getSurname());
		    
		    
		    userInfo.setCountry(obj[6].toString());
		    userInfo.setProvince(obj[4].toString());
		    
		    userInfo.setAdress(obj[7].toString());
		    userInfo.setIdentityNum(obj[5].toString());
		 
		    userInfo.setBackup1(obj[1].toString());
		    userInfo.setIsAgent(1);
		    userInfo.setBackup5("[]");

		    try {
		    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		    Date date = sdf.parse(obj[2].toString());
		    userInfo.setBirthday(date);
		    }catch(Exception e)
		    {
		    	
		    }
		    this.userInfoService.saveOrUpdate(userInfo);
			}
    		
    	}
    	
    	 System.out.println("import crmInfo end");
    	 
    	 
    	 System.out.println("import crmTrade begin");
    	 
    	 List tradecrmUserList=this.dataSqlService.getTradeAndCrmUserBindInfoList();
		
		 for(int i=0;i<tradecrmUserList.size();i++) {
	    		Object[]  obj=(Object[])tradecrmUserList.get(i);
	    		System.out.println(obj[0].toString()+"    "+obj[1].toString());
	    		TradeAccount ta_query=new TradeAccount();
	 		    ta_query.setTradeId(obj[0].toString());
	 		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1, "id", "asc", ta_query);
	 		    
	 		    if(ta_page.getContent().size()>0) {
	 		    	
	 		    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
	 		    	UserInfo query_userInfo=new UserInfo();
	 				query_userInfo.setUserName(obj[1].toString());
	 				Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);
	 				
	 				if(userInfo_page.getContent().size()>0) {
	 					UserInfo ui=(UserInfo)userInfo_page.getContent().get(0);
	 					
	 					ta.setUserId(ui.getId());
	 					ta.setUserAccount(ui.getUserName());
	 					ta.setType(new Integer(obj[2].toString()));
	 					this.tradeAccountService.saveOrUpdate(ta);
	 				}
	 				
	 		    	
	 		    }
	    		
		 }
		
    	 System.out.println("import crmTrade end");
    	
    	

		
    */

	System.out.println("import crmAgent begin");
    	 
		List crmUserAgentList=this.dataSqlService.getAgentAndCrmUserBindInfoList();
	   
		for(int i=0;i<crmUserAgentList.size();i++) {
			   Object[]  obj=(Object[])crmUserAgentList.get(i);

			   User agentUser=this.userService.findByUserName(obj[0].toString());
			   if(agentUser==null) {
				try {
				User user = new User();
				user.setUsername(obj[0].toString());//代理邮箱（账号）
				user.setSalt(RandomStrUtil.generate(32));
				user.setGmtCreate(new Date());
				user.setGmtModified(new Date());
				user.setUserCreate(1L);
				user.setNickName(obj[2].toString());//代理名称
				user.setMobile(obj[3].toString());
				user.setRoleType(1);
				user.setReId(1L);
				
				user.setPassword(EncryptUtils.encrypt("abc123", user.getCredentialsSalt()));
				user.setSaleSerial(new UserInfoController().genRandomNum(10,1));
				
				user.setIsAvailable(1);
				user.setIsDeleted(0);
				user.setUserType(1);
				
				user.setStoreId(new Long(obj[4].toString()));//交易账号
				this.userService.save(user);

				user.setSortStr("$"+String.format("%010d",user.getUserId()));//本身排序字段
				user.setOrderStr("$0000000001$"+user.getSortStr());//完整排序字段
				this.userService.save(user);


				/* 
				UserInfo userInfo = new UserInfo();
				userInfo.setUserName(obj[1].toString());
				Page<UserInfo> userInfoPage = this.userInfoService.findAll(0, 1, "id", "asc", userInfo);
				if(userInfoPage.getContent().size()>0) {
					userInfo = userInfoPage.getContent().get(0);
					userInfo.setParentId(user.getUserId());
					userInfoService.saveOrUpdate(userInfo);
				}
				*/
				
				List<Long> roleIds = new ArrayList<Long>();
				roleIds.add(Long.valueOf(7));
				this.userService.grantUserRole(user.getUserId(), roleIds);
				} catch (Exception e) {
					e.printStackTrace();
				}
			   }else{
				/* 
				UserInfo userInfo = new UserInfo();
				userInfo.setUserName(obj[1].toString());
				Page<UserInfo> userInfoPage = this.userInfoService.findAll(0, 1, "id", "asc", userInfo);
				if(userInfoPage.getContent().size()>0) {
					userInfo = userInfoPage.getContent().get(0);
					userInfo.setParentId(agentUser.getUserId());
					userInfoService.saveOrUpdate(userInfo);
				}
				*/
			   }
			   
		}
	   
		System.out.println("import crmAgent end");


	  
    }
    
	
	
    
    
}
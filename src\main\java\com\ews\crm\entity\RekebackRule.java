package com.ews.crm.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "rekeback_rule")
public class RekebackRule implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *规则名称
    **/
	@Column(name = "rule_name")
	protected  String ruleName;

    /**
    *总监返佣
    **/
	@Column(name = "rekeback_a1")
	protected  Double rekebackA1;

    /**
    *销售经理返佣
    **/
	@Column(name = "rekeback_a2")
	protected  Double rekebackA2;

    /**
    *销售返佣
    **/
	@Column(name = "rekeback_a3")
	protected  Double rekebackA3;

    /**
    *备用内部返佣1
    **/
	@Column(name = "rekeback_a4")
	protected  Double rekebackA4;

    /**
    *备用内部返佣2
    **/
	@Column(name = "rekeback_a5")
	protected  Double rekebackA5;

    /**
    *上一级代理返佣（每手）
    **/
	@Column(name = "rekeback_b1")
	protected  Double rekebackB1;

    /**
    *上二级代理返佣(每手)
    **/
	@Column(name = "rekeback_b2")
	protected  Double rekebackB2;

    /**
    *上三级代理返佣（每手）
    **/
	@Column(name = "rekeback_b3")
	protected  Double rekebackB3;

    /**
    *备用代理返佣1
    **/
	@Column(name = "rekeback_b4")
	protected  Double rekebackB4;

    /**
    *备用代理返佣2
    **/
	@Column(name = "rekeback_b5")
	protected  Double rekebackB5;

    /**
    *备注
    **/
	@Column(name = "remark")
	protected  String remark;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5
    **/
	@Column(name = "backup5")
	protected  Integer backup5;

    /**
    *backup6
    **/
	@Column(name = "backup6")
	protected  Integer backup6;

	@Transient
	protected  Integer sortNum;
	
	@Transient
	protected  List prodList;
	@Transient
	protected  List groupList;
	
	
	@Column(name = "rekeback_b6")
	protected  Double rekebackB6;
	
	@Column(name = "rekeback_b7")
	protected  Double rekebackB7;
	
	@Column(name = "rekeback_b8")
	protected  Double rekebackB8;
	
	@Column(name = "rekeback_b9")
	protected  Double rekebackB9;
	
	@Column(name = "rekeback_b10")
	protected  Double rekebackB10;
	
	@Column(name = "rekeback_b11")
	protected  Double rekebackB11;
	
	@Column(name = "rekeback_b12")
	protected  Double rekebackB12;
	
	@Column(name = "rekeback_b13")
	protected  Double rekebackB13;
	
	@Column(name = "rekeback_b14")
	protected  Double rekebackB14;
	
	@Column(name = "rekeback_b15")
	protected  Double rekebackB15;
	
	@Column(name = "rekeback_b16")
	protected  Double rekebackB16;
	
	@Column(name = "rekeback_b17")
	protected  Double rekebackB17;
	
	@Column(name = "rekeback_b18")
	protected  Double rekebackB18;
	
	@Column(name = "rekeback_b19")
	protected  Double rekebackB19;
	
	@Column(name = "rekeback_b20")
	protected  Double rekebackB20;
	
	@Column(name = "rekeback_b21")
	protected  Double rekebackB21;
	
	@Column(name = "rekeback_b22")
	protected  Double rekebackB22;
	
	@Column(name = "rekeback_b23")
	protected  Double rekebackB23;
	
	@Column(name = "rekeback_b24")
	protected  Double rekebackB24;
	
	@Column(name = "rekeback_b25")
	protected  Double rekebackB25;
	
	@Column(name = "rekeback_b26")
	protected  Double rekebackB26;
	
	@Column(name = "rekeback_b27")
	protected  Double rekebackB27;
	
	@Column(name = "rekeback_b28")
	protected  Double rekebackB28;
	
	@Column(name = "rekeback_b29")
	protected  Double rekebackB29;
	
	@Column(name = "rekeback_b30")
	protected  Double rekebackB30;


    public List getProdList() {
		return prodList;
	}
	public void setProdList(List prodList) {
		this.prodList = prodList;
	}
	public List getGroupList() {
		return groupList;
	}
	public void setGroupList(List groupList) {
		this.groupList = groupList;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getRuleName()
    {
        return ruleName;
    }
    public void setRuleName(String  ruleName)
    {
        this.ruleName = ruleName;
    }
    public Double  getRekebackA1()
    {
        return rekebackA1;
    }
    public void setRekebackA1(Double  rekebackA1)
    {
        this.rekebackA1 = rekebackA1;
    }
    public Double  getRekebackA2()
    {
        return rekebackA2;
    }
    public void setRekebackA2(Double  rekebackA2)
    {
        this.rekebackA2 = rekebackA2;
    }
    public Double  getRekebackA3()
    {
        return rekebackA3;
    }
    public void setRekebackA3(Double  rekebackA3)
    {
        this.rekebackA3 = rekebackA3;
    }
    public Double  getRekebackA4()
    {
        return rekebackA4;
    }
    public void setRekebackA4(Double  rekebackA4)
    {
        this.rekebackA4 = rekebackA4;
    }
    public Double  getRekebackA5()
    {
        return rekebackA5;
    }
    public void setRekebackA5(Double  rekebackA5)
    {
        this.rekebackA5 = rekebackA5;
    }
    public Double  getRekebackB1()
    {
        return rekebackB1;
    }
    public void setRekebackB1(Double  rekebackB1)
    {
        this.rekebackB1 = rekebackB1;
    }
    public Double  getRekebackB2()
    {
        return rekebackB2;
    }
    public void setRekebackB2(Double  rekebackB2)
    {
        this.rekebackB2 = rekebackB2;
    }
    public Double  getRekebackB3()
    {
        return rekebackB3;
    }
    public void setRekebackB3(Double  rekebackB3)
    {
        this.rekebackB3 = rekebackB3;
    }
    public Double  getRekebackB4()
    {
        return rekebackB4;
    }
    public void setRekebackB4(Double  rekebackB4)
    {
        this.rekebackB4 = rekebackB4;
    }
    public Double  getRekebackB5()
    {
        return rekebackB5;
    }
    public void setRekebackB5(Double  rekebackB5)
    {
        this.rekebackB5 = rekebackB5;
    }
    public String  getRemark()
    {
        return remark;
    }
    public void setRemark(String  remark)
    {
        this.remark = remark;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(Integer  backup5)
    {
        this.backup5 = backup5;
    }
    public Integer  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(Integer  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public Double getRekebackB6() {
		return rekebackB6;
	}
	public void setRekebackB6(Double rekebackB6) {
		this.rekebackB6 = rekebackB6;
	}
	public Double getRekebackB7() {
		return rekebackB7;
	}
	public void setRekebackB7(Double rekebackB7) {
		this.rekebackB7 = rekebackB7;
	}
	public Double getRekebackB8() {
		return rekebackB8;
	}
	public void setRekebackB8(Double rekebackB8) {
		this.rekebackB8 = rekebackB8;
	}
	public Double getRekebackB9() {
		return rekebackB9;
	}
	public void setRekebackB9(Double rekebackB9) {
		this.rekebackB9 = rekebackB9;
	}
	public Double getRekebackB10() {
		return rekebackB10;
	}
	public void setRekebackB10(Double rekebackB10) {
		this.rekebackB10 = rekebackB10;
	}
	public Double getRekebackB11() {
		return rekebackB11;
	}
	public void setRekebackB11(Double rekebackB11) {
		this.rekebackB11 = rekebackB11;
	}
	public Double getRekebackB12() {
		return rekebackB12;
	}
	public void setRekebackB12(Double rekebackB12) {
		this.rekebackB12 = rekebackB12;
	}
	public Double getRekebackB13() {
		return rekebackB13;
	}
	public void setRekebackB13(Double rekebackB13) {
		this.rekebackB13 = rekebackB13;
	}
	public Double getRekebackB14() {
		return rekebackB14;
	}
	public void setRekebackB14(Double rekebackB14) {
		this.rekebackB14 = rekebackB14;
	}
	public Double getRekebackB15() {
		return rekebackB15;
	}
	public void setRekebackB15(Double rekebackB15) {
		this.rekebackB15 = rekebackB15;
	}
	public Double getRekebackB16() {
		return rekebackB16;
	}
	public void setRekebackB16(Double rekebackB16) {
		this.rekebackB16 = rekebackB16;
	}
	public Double getRekebackB17() {
		return rekebackB17;
	}
	public void setRekebackB17(Double rekebackB17) {
		this.rekebackB17 = rekebackB17;
	}
	public Double getRekebackB18() {
		return rekebackB18;
	}
	public void setRekebackB18(Double rekebackB18) {
		this.rekebackB18 = rekebackB18;
	}
	public Double getRekebackB19() {
		return rekebackB19;
	}
	public void setRekebackB19(Double rekebackB19) {
		this.rekebackB19 = rekebackB19;
	}
	public Double getRekebackB20() {
		return rekebackB20;
	}
	public void setRekebackB20(Double rekebackB20) {
		this.rekebackB20 = rekebackB20;
	}
	public Double getRekebackB21() {
		return rekebackB21;
	}
	public void setRekebackB21(Double rekebackB21) {
		this.rekebackB21 = rekebackB21;
	}
	public Double getRekebackB22() {
		return rekebackB22;
	}
	public void setRekebackB22(Double rekebackB22) {
		this.rekebackB22 = rekebackB22;
	}
	public Double getRekebackB23() {
		return rekebackB23;
	}
	public void setRekebackB23(Double rekebackB23) {
		this.rekebackB23 = rekebackB23;
	}
	public Double getRekebackB24() {
		return rekebackB24;
	}
	public void setRekebackB24(Double rekebackB24) {
		this.rekebackB24 = rekebackB24;
	}
	public Double getRekebackB25() {
		return rekebackB25;
	}
	public void setRekebackB25(Double rekebackB25) {
		this.rekebackB25 = rekebackB25;
	}
	public Double getRekebackB26() {
		return rekebackB26;
	}
	public void setRekebackB26(Double rekebackB26) {
		this.rekebackB26 = rekebackB26;
	}
	public Double getRekebackB27() {
		return rekebackB27;
	}
	public void setRekebackB27(Double rekebackB27) {
		this.rekebackB27 = rekebackB27;
	}
	public Double getRekebackB28() {
		return rekebackB28;
	}
	public void setRekebackB28(Double rekebackB28) {
		this.rekebackB28 = rekebackB28;
	}
	public Double getRekebackB29() {
		return rekebackB29;
	}
	public void setRekebackB29(Double rekebackB29) {
		this.rekebackB29 = rekebackB29;
	}
	public Double getRekebackB30() {
		return rekebackB30;
	}
	public void setRekebackB30(Double rekebackB30) {
		this.rekebackB30 = rekebackB30;
	}
    
    
    
    

}

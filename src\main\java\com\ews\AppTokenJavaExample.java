package com.ews;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Hex;

import com.alibaba.fastjson.JSONObject;
import com.ews.model.Applicant;
import com.ews.model.DocType;
import com.ews.model.HttpMethod;
import com.ews.model.Metadata;
import com.fasterxml.jackson.databind.ObjectMapper;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

public class AppTokenJavaExample {
    // The description of the authorization method is available here: https://developers.sumsub.com/api-reference/#app-tokens
    private static final String SUMSUB_SECRET_KEY = "dBbalrmABk6dZEKApaeqROh2KR7iBYqg"; // Example: Hej2ch71kG2kTd1iIUDZFNsO5C1lh5Gq
    private static final String SUMSUB_APP_TOKEN = "prd:yLXmlxzPKr0syXBV8fZTRZ25.5IZS0E0D2lDcpihCYEkAmjWJ1S9NUecQ"; // Example: sbx:uY0CgwELmgUAEyl4hNWxLngb.0WSeQeiYny4WEqmAALEAiK2qTC96fBad
    private static final String SUMSUB_TEST_BASE_URL = "https://api.sumsub.com";
    //Please don't forget to change token and secret key values to production ones when switching to production

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws IOException, InvalidKeyException, NoSuchAlgorithmException {

        String externalUserId = UUID.randomUUID().toString();
      
        String levelName = "basic-kyc-level";
        //1) Creating an applicant
       String applicantId = createApplicant(externalUserId, levelName);
        System.out.println("The applicant (" + externalUserId + ") was successfully created: " + applicantId);
        
    //String applicantId="648933a2bdd4e34d5f6e7cdd";
        // 2) Getting access token
        //String accessTokenStr = getAccessToken(externalUserId, levelName);
        //System.out.println("Access token (json string): " + accessTokenStr);
        
        // 3) Getting applicant status
       //String applicantStatusStr = getApplicantStatus(applicantId);
        //System.out.println("Applicant status (json string): " + applicantStatusStr);
        
	    
        
        // 4) Getting applicant data
       //String applicantDatasStr = getApplicantData(applicantId);
        //System.out.println("Applicant Data (json string): " + applicantDatasStr);
        
        
       // 5) Getting VerificationLink
        String verifyLink = getVerificationLink(externalUserId,levelName);
        System.out.println("verifyLink: " + verifyLink);
    }

    public static String createApplicant(String externalUserId, String levelName) throws IOException, NoSuchAlgorithmException, InvalidKeyException {
        // https://developers.sumsub.com/api-reference/#creating-an-applicant

        Applicant applicant = new Applicant(externalUserId);
        
        applicant.setSourceKey("quotech");
        applicant.setEmail("<EMAIL>");
        applicant.setPhone("18611782981");
       
        
        System.out.println(objectMapper.writeValueAsString(applicant));
        Response response = sendPost(
                "/resources/applicants?levelName=" + levelName,
                RequestBody.create( MediaType.parse("application/json; charset=utf-8"),objectMapper.writeValueAsString(applicant)));

        ResponseBody responseBody = response.body();

        return responseBody != null ? objectMapper.readValue(responseBody.string(), Applicant.class).getId() : null;
    }
    
    public static String getVerificationLink(String externalUserId, String levelName) throws IOException, NoSuchAlgorithmException, InvalidKeyException {
        

       
        Response response = sendPost(
                "/resources/sdkIntegrations/levels/basic-kyc-level/websdkLink?ttlInSecs=1800&externalUserId="+externalUserId+"&lang=en" ,
                RequestBody.create( MediaType.parse("application/json; charset=utf-8"),"{}"));

        ResponseBody responseBody = response.body();

        return responseBody != null ? objectMapper.readValue(responseBody.string(), Applicant.class).getUrl() : null;
    }

 

    public static String getApplicantStatus(String applicantId) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        // https://developers.sumsub.com/api-reference/#getting-applicant-status-api

        Response response = sendGet("/resources/applicants/" + applicantId + "/requiredIdDocsStatus");

        ResponseBody responseBody = response.body();
        return responseBody != null ? responseBody.string() : null;
    }
    
    
    public static String getApplicantData(String applicantId) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        // https://developers.sumsub.com/api-reference/#getting-applicant-status-api

        Response response = sendGet("/resources/applicants/" + applicantId + "/one");

        ResponseBody responseBody = response.body();
        return responseBody != null ? responseBody.string() : null;
    }

    public static String getAccessToken(String externalUserId, String levelName) throws NoSuchAlgorithmException, InvalidKeyException, IOException {
        // https://developers.sumsub.com/api-reference/#access-tokens-for-sdks

        Response response = sendPost("/resources/accessTokens?userId=" + externalUserId + "&levelName=" + levelName, RequestBody.create(null,new byte[0]));

        ResponseBody responseBody = response.body();
        return responseBody != null ? responseBody.string() : null;
    }

    private static Response sendPost(String url, RequestBody requestBody) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
        long ts = Instant.now().getEpochSecond();

        Request request = new Request.Builder()
                .url(SUMSUB_TEST_BASE_URL + url)
                .header("X-App-Token", SUMSUB_APP_TOKEN)
                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.POST, url, requestBodyToBytes(requestBody)))
                .header("X-App-Access-Ts", String.valueOf(ts))
                .post(requestBody)
                .build();

        Response response = new OkHttpClient().newCall(request).execute();

        if (response.code() != 200 && response.code() != 201) {
            // https://developers.sumsub.com/api-reference/#errors
            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
            // Then perhaps you should throw the exception. (depends on the logic of your code)
        }
        return response;
    }

    private static Response sendGet(String url) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
        long ts = Instant.now().getEpochSecond();

        Request request = new Request.Builder()
                .url(SUMSUB_TEST_BASE_URL + url)
                .header("X-App-Token", SUMSUB_APP_TOKEN)
                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.GET, url, null))
                .header("X-App-Access-Ts", String.valueOf(ts))
                .get()
                .build();

        Response response = new OkHttpClient().newCall(request).execute();

        if (response.code() != 200 && response.code() != 201) {
            // https://developers.sumsub.com/api-reference/#errors
            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
            // Then perhaps you should throw the exception. (depends on the logic of your code)
        }
        return response;
    }

    private static String createSignature(long ts, HttpMethod httpMethod, String path, byte[] body) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        hmacSha256.init(new SecretKeySpec(SUMSUB_SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        hmacSha256.update((ts + httpMethod.name() + path).getBytes(StandardCharsets.UTF_8));
        byte[] bytes = body == null ? hmacSha256.doFinal() : hmacSha256.doFinal(body);
        return Hex.encodeHexString(bytes);
    }

    public static byte[] requestBodyToBytes(RequestBody requestBody) throws IOException {
        Buffer buffer = new Buffer();
        requestBody.writeTo(buffer);
        return buffer.readByteArray();
    }

}

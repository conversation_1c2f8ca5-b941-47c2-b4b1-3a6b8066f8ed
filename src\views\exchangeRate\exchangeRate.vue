<template>
  <div class="app-container">
    <!--div class="filter-container">
			<el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
		</div-->
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="货币对" prop="currencyPairs" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.currencyPairs }}</span>
        </template>
      </el-table-column>
      <el-table-column label="买入价" prop="purchasePrice" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.purchasePrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卖出价" prop="sellPrice" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sellPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="backup1" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup1==1?'实时汇率':'固定汇率' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前汇率" prop="fee" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.fee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="买入加点" prop="backup5" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup5 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卖出加点" prop="backup6" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup6 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="gmtModified" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtModified | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">更新</el-button>
          <!--el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button-->
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="更新价格" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="货币对" prop="currencyPairs">
          <el-input v-model="temp.currencyPairs" />
        </el-form-item>
        <el-form-item label="汇率类型" prop="backup1">
          <el-select v-model="temp.backup1" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="type in types"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.backup1 ==1" label="买入加点" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
        <el-form-item v-if="temp.backup1 ==1" label="卖出加点" prop="backup6">
          <el-input v-model="temp.backup6" />
        </el-form-item>
        <el-form-item v-if="temp.backup1 ==2" label="买入价" prop="purchasePrice">
          <el-input v-model="temp.purchasePrice" />
        </el-form-item>
        <el-form-item v-if="temp.backup1 ==2" label="卖出价" prop="sellPrice">
          <el-input v-model="temp.sellPrice" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchExchangeRate, createExchangeRate, updateExchangeRate, updateIsAvailable, removeExchangeRate } from '@/api/exchangeRate'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'ExchangeRateTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      },
      temp: {
        id: undefined,
        currencyPairs: '',
        purchasePrice: '',
        sellPrice: '',
        backup1: '',
        backup5: undefined,
        backup6: undefined
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      types: [
        					{
        						value: 1,
        						label: '按实时汇率计算'
        					},
        					{
        						value: 2,
        						label: '按固定汇率计算'
        					}
        					],
      rules: {
        currencyPairs: [
          { required: true, message: '货币对不能为空', trigger: 'change' },,
        ],
        purchasePrice: [
          { required: true, message: '买入价不能为空', trigger: 'change' },,
        ],
        sellPrice: [
          { required: true, message: '卖出价不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        currencyPairs: '',
        purchasePrice: '',
        sellPrice: '',
        backup1: '',
        backup5: undefined,
        backup6: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createExchangeRate(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateExchangeRate(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeExchangeRate(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

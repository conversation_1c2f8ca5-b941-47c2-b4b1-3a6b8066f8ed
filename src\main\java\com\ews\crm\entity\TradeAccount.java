package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "trade_account")
public class TradeAccount implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

	/**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchBegin;

	    /**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchEnd;
		
    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *交易账号
    **/
	@Column(name = "trade_id")
	protected  String tradeId;

    /**
    *用户ID
    **/
	@Column(name = "user_id")
	protected  Long userId;

    /**
    *所属用户
    **/
	@Column(name = "user_account")
	protected  String userAccount;

    /**
    *类型
    **/
	@Column(name = "type")
	protected  Integer type;

    /**
    *交易组别
    **/
	@Column(name = "group_name")
	protected  String groupName;

    /**
    *杠杆ID
    **/
	@Column(name = "lever_id")
	protected  Long leverId;

    /**
    *交易杠杆
    **/
	@Column(name = "lever_show")
	protected  String leverShow;

    /**
    *交易权限
    **/
	@Column(name = "trade_status")
	protected  Integer tradeStatus;

    /**
    *余额
    **/
	@Column(name = "balance1")
	protected  Double balance1;

    /**
    *信用
    **/
	@Column(name = "balance2")
	protected  Double balance2;

    /**
    *账户净值
    **/
	@Column(name = "balance3")
	protected  Double balance3;

    /**
    *已用预付款
    **/
	@Column(name = "balance4")
	protected  Double balance4;

    /**
    *可用预付款
    **/
	@Column(name = "balance5")
	protected  Double balance5;

    /**
    *备用1--出金冻结
    **/
	@Column(name = "balance6")
	protected  Double balance6;

    /**
    *备用2--保证金比例
    **/
	@Column(name = "balance7")
	protected  Double balance7;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  Integer backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  Integer backup2;

    /**
    *backup3   --临时存放 交易用户的类型
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4  --临时存放 crm_id+ trade_id 的MD5
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5--判断交易密码是否正确
    **/
	@Column(name = "backup5")
	protected  String backup5;

    /**
    *backup6-初始密钥
    **/
	@Column(name = "backup6")
	protected  String backup6;

	@Transient
	protected  Integer sortNum;
	
	@Transient
	protected  List orderList;
	
	@Transient
	protected  UserInfo userInfo;
	
	
	@Transient
	protected  List userInfoList;
	
	
	@Transient
	protected  Double volume;
	

    public List getUserInfoList() {
		return userInfoList;
	}
	public void setUserInfoList(List userInfoList) {
		this.userInfoList = userInfoList;
	}
	public UserInfo getUserInfo() {
		return userInfo;
	}
	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getTradeId()
    {
        return tradeId;
    }
    public void setTradeId(String  tradeId)
    {
        this.tradeId = tradeId;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public String  getUserAccount()
    {
        return userAccount;
    }
    public void setUserAccount(String  userAccount)
    {
        this.userAccount = userAccount;
    }
    public Integer  getType()
    {
        return type;
    }
    public void setType(Integer  type)
    {
        this.type = type;
    }
    public String  getGroupName()
    {
        return groupName;
    }
    public void setGroupName(String  groupName)
    {
        this.groupName = groupName;
    }
    public Long  getLeverId()
    {
        return leverId;
    }
    public void setLeverId(Long  leverId)
    {
        this.leverId = leverId;
    }
    public String  getLeverShow()
    {
        return leverShow;
    }
    public void setLeverShow(String  leverShow)
    {
        this.leverShow = leverShow;
    }
    public Integer  getTradeStatus()
    {
        return tradeStatus;
    }
    public void setTradeStatus(Integer  tradeStatus)
    {
        this.tradeStatus = tradeStatus;
    }
    public Double  getBalance1()
    {
        return balance1;
    }
    public void setBalance1(Double  balance1)
    {
        this.balance1 = balance1;
    }
    public Double  getBalance2()
    {
        return balance2;
    }
    public void setBalance2(Double  balance2)
    {
        this.balance2 = balance2;
    }
    public Double  getBalance3()
    {
        return balance3;
    }
    public void setBalance3(Double  balance3)
    {
        this.balance3 = balance3;
    }
    public Double  getBalance4()
    {
        return balance4;
    }
    public void setBalance4(Double  balance4)
    {
        this.balance4 = balance4;
    }
    public Double  getBalance5()
    {
        return balance5;
    }
    public void setBalance5(Double  balance5)
    {
        this.balance5 = balance5;
    }
    public Double  getBalance6()
    {
        return balance6;
    }
    public void setBalance6(Double  balance6)
    {
        this.balance6 = balance6;
    }
    public Double  getBalance7()
    {
        return balance7;
    }
    public void setBalance7(Double  balance7)
    {
        this.balance7 = balance7;
    }
    public Integer  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(Integer  backup1)
    {
        this.backup1 = backup1;
    }
    public Integer  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(Integer  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public String  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(String  backup5)
    {
        this.backup5 = backup5;
    }
    public String  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(String  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public List getOrderList() {
		return orderList;
	}
	public void setOrderList(List orderList) {
		this.orderList = orderList;
	}
	public String getGmtCreateSearchBegin() {
		return gmtCreateSearchBegin;
	}
	public void setGmtCreateSearchBegin(String gmtCreateSearchBegin) {
		this.gmtCreateSearchBegin = gmtCreateSearchBegin;
	}
	public String getGmtCreateSearchEnd() {
		return gmtCreateSearchEnd;
	}
	public void setGmtCreateSearchEnd(String gmtCreateSearchEnd) {
		this.gmtCreateSearchEnd = gmtCreateSearchEnd;
	}
	public Double getVolume() {
		return volume;
	}
	public void setVolume(Double volume) {
		this.volume = volume;
	}
    
	
	
    

}

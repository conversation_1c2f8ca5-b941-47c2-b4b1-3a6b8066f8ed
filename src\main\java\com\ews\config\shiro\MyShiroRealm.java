package com.ews.config.shiro;

import java.util.List;

import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;

import com.ews.config.jwt.JWTToken;
import com.ews.config.jwt.JWTUtil;
import com.ews.system.entity.Permission;
import com.ews.system.entity.Role;
import com.ews.system.entity.User;
import com.ews.system.model.IRolePermission;
import com.ews.system.model.IUserRole;
import com.ews.system.service.PermissionService;
import com.ews.system.service.RoleService;
import com.ews.system.service.UserService;

public class MyShiroRealm extends AuthorizingRealm {
	@Autowired
	private UserService userService;

	@Autowired
	private RoleService roleService;
	
	@Autowired
	private PermissionService permissionService;
	

	// 权限信息，包括角色以及权限
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
		SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
		// 如果身份认证的时候没有传入User对象，这里只能取到userName
		// 也就是SimpleAuthenticationInfo构造的时候第一个参数传递需要User对象
		try {
			//LoginUser user = (LoginUser) principals.getPrimaryPrincipal();
			
			String username = JWTUtil.getUsername(principals.toString());
			User user = this.userService.findByUserName(username);
			List<IUserRole> iuList = userService.findAllUserRoleByUserId(user.getUserId());
			for (IUserRole iur : iuList) {//角色
			if(iur.getUserId()!=null) {
				authorizationInfo.addRole(iur.getRole());
				Role role  = this.roleService.findById(iur.getRoleId());
				if(role.getRoleSign().equals("-10000")) {
					List<Permission> permissions = this.permissionService.findAll();
					for(Permission p:permissions) {
						authorizationInfo.addStringPermission(p.getPermissionSign());
					}
				}else {
					List<IRolePermission> irpList = roleService.findRolePermissionByRoleId(iur.getRoleId());
					for(IRolePermission irp:irpList) {//权限
						if(irp.getRoleId()!=null) {
							authorizationInfo.addStringPermission(irp.getPermission());
						}
					}
				}
				
				
			}
		}
		}catch(Exception e) {
			e.printStackTrace();
		}
		return authorizationInfo;
	}

	
	 /**
     * 使用JWT替代原生Token
     *
     * @param token
     * @return
     */
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof JWTToken;
    }
	
	
	/**
	 * 用户身份认证
	 */
	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken)  {
		// 获取用户的输入的账号.
		String token = (String) authcToken.getCredentials();
		
		String userName = JWTUtil.getUsername(token);
		
		
		// 通过username从数据库中查找 User对象.
		// 实际项目中，这里可以根据实际情况做缓存，如果不做，Shiro自己也是有时间间隔机制，2分钟内不会重复执行该方法
		User user = userService.findByUserName(userName);

		if(user!=null && user.getIsDeleted() != 1) {
			//用户被删除或者禁用
			if ( user.getIsAvailable() == 0) {
				 throw new DisabledAccountException("901");
			}
			// 密码验证
	        if (!JWTUtil.verify(token, userName, user.getPassword())) {
	            throw new UnknownAccountException("900");
	        }
	         return new SimpleAuthenticationInfo(token, token, "realm");
		}else {
			 throw new UnknownAccountException("900");
		}
	}
}

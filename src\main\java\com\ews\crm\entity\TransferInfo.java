package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "transfer_info")
public class TransferInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *用户ID
    **/
	@Column(name = "user_id")
	protected  Long userId;

    /**
    *转出账号
    **/
	@Column(name = "transfer_out")
	protected  Integer transferOut;

    /**
    *转入账号
    **/
	@Column(name = "transfer_in")
	protected  Integer transferIn;

    /**
    *转账金额
    **/
	@Column(name = "transfer_amount")
	protected  Double transferAmount;

    /**
    *转出状态
    **/
	@Column(name = "out_status")
	protected  Integer outStatus;

    /**
    *转入状态
    **/
	@Column(name = "in_status")
	protected  Integer inStatus;

    /**
    *转账状态
    **/
	@Column(name = "transfer_status")
	protected  Integer transferStatus;

    /**
    *审批状态
    **/
	@Column(name = "audit_status")
	protected  Integer auditStatus;

    /**
    *备注
    **/
	@Column(name = "remark")
	protected  String remark;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *转出订单ID
    **/
	@Column(name = "out_order_id")
	protected  String outOrderId;

    /**
    *转入订单ID
    **/
	@Column(name = "in_order_id")
	protected  String inOrderId;

	@Transient
	protected  Integer sortNum;
	
	@Transient
	protected  UserInfo userInfo;
	
	
	@Transient
	protected  List userInfoList;
	
	
	

    public List getUserInfoList() {
		return userInfoList;
	}
	public void setUserInfoList(List userInfoList) {
		this.userInfoList = userInfoList;
	}
	public UserInfo getUserInfo() {
		return userInfo;
	}
	public void setUserInfo(UserInfo userInfo) {
		this.userInfo = userInfo;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public Integer  getTransferOut()
    {
        return transferOut;
    }
    public void setTransferOut(Integer  transferOut)
    {
        this.transferOut = transferOut;
    }
    public Integer  getTransferIn()
    {
        return transferIn;
    }
    public void setTransferIn(Integer  transferIn)
    {
        this.transferIn = transferIn;
    }
    public Double  getTransferAmount()
    {
        return transferAmount;
    }
    public void setTransferAmount(Double  transferAmount)
    {
        this.transferAmount = transferAmount;
    }
    public Integer  getOutStatus()
    {
        return outStatus;
    }
    public void setOutStatus(Integer  outStatus)
    {
        this.outStatus = outStatus;
    }
    public Integer  getInStatus()
    {
        return inStatus;
    }
    public void setInStatus(Integer  inStatus)
    {
        this.inStatus = inStatus;
    }
    public Integer  getTransferStatus()
    {
        return transferStatus;
    }
    public void setTransferStatus(Integer  transferStatus)
    {
        this.transferStatus = transferStatus;
    }
    public Integer  getAuditStatus()
    {
        return auditStatus;
    }
    public void setAuditStatus(Integer  auditStatus)
    {
        this.auditStatus = auditStatus;
    }
    public String  getRemark()
    {
        return remark;
    }
    public void setRemark(String  remark)
    {
        this.remark = remark;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getOutOrderId()
    {
        return outOrderId;
    }
    public void setOutOrderId(String  outOrderId)
    {
        this.outOrderId = outOrderId;
    }
    public String  getInOrderId()
    {
        return inOrderId;
    }
    public void setInOrderId(String  inOrderId)
    {
        this.inOrderId = inOrderId;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

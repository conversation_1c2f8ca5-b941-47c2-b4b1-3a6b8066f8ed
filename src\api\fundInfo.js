import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/fundInfo/list',
    method: 'post',
    params: query
  })
}

export function fetchDepositList(query) {
  return request({
    url: '/fundInfo/depositList',
    method: 'post',
    params: query
  })
}
export function fetchWithdrawList(query) {
  return request({
    url: '/fundInfo/withdrawList',
    method: 'post',
    params: query
  })
}

export function fetchDepositAuditList(query) {
  return request({
    url: '/fundInfo/depositAuditList',
    method: 'post',
    params: query
  })
}
export function fetchWithdrawAuditList(query) {
  return request({
    url: '/fundInfo/withdrawAuditList',
    method: 'post',
    params: query
  })
}

export function fetchFundInfo(id) {
  return request({
    url: '/fundInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createFundInfo(data) {
  return request({
    url: '/fundInfo/add',
    method: 'post',
    data
  })
}

export function updateFundInfo(data) {
  return request({
    url: '/fundInfo/update',
    method: 'post',
    data
  })
}

export function auditDepositFundInfo(data) {
  return request({
    url: '/fundInfo/auditDepositFundInfo',
    method: 'post',
    data
  })
}

export function auditWithdrawFundInfo(data) {
  return request({
    url: '/fundInfo/auditWithdrawFundInfo',
    method: 'post',
    data
  })
}
export function auditDepositFundInfo2(data) {
  return request({
    url: '/fundInfo/auditDepositFundInfo2',
    method: 'post',
    data
  })
}

export function auditWithdrawFundInfo2(data) {
  return request({
    url: '/fundInfo/auditWithdrawFundInfo2',
    method: 'post',
    data
  })
}

export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/fundInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}

export function cxzxs(id) {
  return request({
    url: '/fundInfo/cxzxs',
    method: 'get',
    params: { id }
  })
}

export function qxzxs(id) {
  return request({
    url: '/fundInfo/qxzxs',
    method: 'get',
    params: { id }
  })
}
export function removeFundInfo(id) {
  return request({
    url: '/fundInfo/remove',
    method: 'get',
    params: { id }
  })
}

export function exportDepositeExcel(query) {
  return request({
    url: '/fundInfo/exportDepositeExcel',
    method: 'post',
    params: query
  })
}

export function exportWithdrawExcel(query) {
  return request({
    url: '/fundInfo/exportWithdrawExcel',
    method: 'post',
    params: query
  })
}

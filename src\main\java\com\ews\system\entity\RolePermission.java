package com.ews.system.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sys_role_permission")
public class RolePermission implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *表id
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.AUTO)
	@Column(name = "role_permission_id")
	private  Long rolePermissionId;
    /**
    *角色id
    **/
	@Column(name = "role_id")
	private  Long roleId;
    /**
    *权限id
    **/
	@Column(name = "permission_id")
	private  Long permissionId;

    public Long  getRolePermissionId()
    {
        return rolePermissionId;
    }
    public void setRolePermissionId(Long  rolePermissionId)
    {
        this.rolePermissionId = rolePermissionId;
    }
    public Long  getRoleId()
    {
        return roleId;
    }
    public void setRoleId(Long  roleId)
    {
        this.roleId = roleId;
    }
    public Long  getPermissionId()
    {
        return permissionId;
    }
    public void setPermissionId(Long  permissionId)
    {
        this.permissionId = permissionId;
    }

}

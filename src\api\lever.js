import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/lever/list',
    method: 'post',
    params: query
  })
}
export function fetchLeverList(query) {
  return request({
    url: '/lever/listALL',
    method: 'post',
    params: query
  })
}
export function fetchLeverList2(query) {
  return request({
    url: '/accountLeverMap/list2',
    method: 'post',
    params: { query }
  })
}

export function fetchLever(id) {
  return request({
    url: '/lever/detail',
    method: 'get',
    params: { id }
  })
}

export function createLever(data) {
  return request({
    url: '/lever/add',
    method: 'post',
    data
  })
}

export function updateLever(data) {
  return request({
    url: '/lever/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/lever/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeLever(id) {
  return request({
    url: '/lever/remove',
    method: 'get',
    params: { id }
  })
}

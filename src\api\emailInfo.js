import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/emailInfo/list',
    method: 'post',
    params: query
  })
}

export function fetchEmailInfo(id) {
  return request({
    url: '/emailInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createEmailInfo(data) {
  return request({
    url: '/emailInfo/add',
    method: 'post',
    data
  })
}

export function updateEmailInfo(data) {
  return request({
    url: '/emailInfo/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/emailInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeEmailInfo(id) {
  return request({
    url: '/emailInfo/remove',
    method: 'get',
    params: { id }
  })
}


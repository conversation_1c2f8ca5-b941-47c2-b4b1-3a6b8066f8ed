package com.ews.system.service;

import java.util.List;

import org.springframework.stereotype.Component;

import com.ews.system.entity.UserRole;


@Component
public interface UserRoleService
{
	/**
	 * 通过userid查询权限关联信息
	 * @param userId
	 * @return
	 */
	List<UserRole> findByUserId(Long userId);
	
	/**
	 * 通过userid查询用户的权限字符串
	 * @param userId
	 * @return
	 */
	List<String> findRoleStrsByUserId(Long userId);
	
	/**
	 * 通过userid和roleid查询userorle
	 * @param userId
	 * @param roleId
	 * @return
	 */
	public UserRole findByUserIdAndRoleId(Long userId,Long roleId);
	
	public void deleteByRoleId(Long roleId);
}

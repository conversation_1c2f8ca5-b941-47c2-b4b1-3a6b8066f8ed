package com.ews.common;

import java.net.URI;
import java.util.Date;
import java.util.Random;

import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.java_websocket.exceptions.WebsocketNotConnectedException;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserInfoService;

@Component
public class StartService implements ApplicationRunner {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Override
	public void run(ApplicationArguments args) throws Exception {
		
		
		 
	}
}


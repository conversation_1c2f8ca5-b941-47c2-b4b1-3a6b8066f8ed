package com.ews.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import com.ews.crm.entity.Lever;

public interface LeverRepository extends  JpaRepository<Lever, Long>,JpaSpecificationExecutor<Lever> {
    /**
     * 更新可用状态
     * @param id
     * @param isAvailable
     * @return
     */
    @Transactional
    @Modifying
    @Query(value = "update lever set is_available=?2 where id=?1",nativeQuery = true)
    int updateIsAvailableById(Long id,Integer isAvailable);

    /**
     * 查询是否存在
     * @param id
     * @param isDeleted
     * @return
     */
    public boolean existsByIdAndIsDeleted(Long id,Integer isDeleted);





}

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.crmAccount" :placeholder="$t('depositAudit.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositAudit.label12')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('depositAudit.label1')" prop="crmAccount" min-width="250px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositAudit.label2')" prop="tradeId" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositAudit.label4')" prop="rateRmb" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.rateRmb }}</span>
        </template>
      </el-table-column>

      <el-table-column fixed="right" :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">{{ $t('depositAudit.label10') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="Approval of deposit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('depositAudit.label1')" prop="userName">
          {{ temp.userName }}    ({{ temp.crmAccount }})
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label2')" prop="tradeId">
          {{ temp.tradeId }}   ({{ temp.groupInfo }})
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label3')" prop="backup6">
          {{ temp.backup6 }}
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label4')" prop="rateRmb">
          {{ temp.rateRmb }}
        </el-form-item>
        <el-form-item v-if="temp.bankAddress!=''" :label="$t('depositAudit.label5')" prop="bankAddress">
          {{ temp.bankAddress }}
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label6')" prop="Annex">
          <img v-if="temp.annex" :src="temp.annex" style="width:400px;">
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label7')" prop="remark">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label8')" prop="amount">
          <el-input v-model="temp.amount" />
        </el-form-item>
        <el-form-item :label="$t('depositAudit.label9')" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()">{{ $t('depositAudit.label10') }}</el-button>
        <el-button type="danger" @click="updateData2()">{{ $t('depositAudit.label11') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchDepositAuditList, auditDepositFundInfo, fetchFundInfo, createFundInfo, updateFundInfo, updateIsAvailable, removeFundInfo, auditDepositFundInfo2 } from '@/api/fundInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'FundInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        userName: undefined,
        crmAccount: undefined,
        tradeId: undefined,
        depositBankId: undefined,
        withdrawBankId: undefined,
        auditStatus: undefined,
        operStatus: undefined,
        auditId: undefined,
        orderId: undefined,
        bankName: undefined,
        bankNum: undefined,
        accountName: undefined,
        mobile: undefined,
        bankAddress: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: '',
        backup5: 'Deposit'
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        userName: [
        ],
        crmAccount: [
        ],
        tradeId: [
        ],
        amount: [
              	{ required: true, message: '入金金额不能为空', trigger: 'change' }
        ],
        depositBankId: [
        ],
        withdrawBankId: [
        ],
        type: [
        ],
        allocationAmount: [
        ],
        auditStatus: [
        ],
        operStatus: [
        ],
        auditId: [
        ],
        orderId: [
        ],
        bankName: [
        ],
        bankNum: [
        ],
        accountName: [
        ],
        mobile: [
        ],
        bankAddress: [
        ],
        remark: [
        ],
        annex: [
        ],
        fee: [
        ],
        actualAmount: [
        ],
        originalAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ],
        rate: [
        ],
        rateRmb: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchDepositAuditList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          auditDepositFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData2() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          auditDepositFundInfo2(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.backup5 = 'Deposit'
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeFundInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

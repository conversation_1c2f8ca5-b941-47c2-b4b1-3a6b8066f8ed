package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "third_party_payment")
public class ThirdPartyPayment implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *接口名称
    **/
	@Column(name = "api_name")
	protected  String apiName;

    /**
    *请求地址
    **/
	@Column(name = "request_url")
	protected  String requestUrl;

    /**
    *请求类型
    **/
	@Column(name = "request_type")
	protected  Integer requestType;

    /**
    *时间戳
    **/
	@Column(name = "time_str")
	protected  String timeStr;

    /**
    *订单前缀
    **/
	@Column(name = "order_prefix")
	protected  String orderPrefix;

    /**
    *签名方式
    **/
	@Column(name = "sign_type")
	protected  Integer signType;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getApiName()
    {
        return apiName;
    }
    public void setApiName(String  apiName)
    {
        this.apiName = apiName;
    }
    public String  getRequestUrl()
    {
        return requestUrl;
    }
    public void setRequestUrl(String  requestUrl)
    {
        this.requestUrl = requestUrl;
    }
    public Integer  getRequestType()
    {
        return requestType;
    }
    public void setRequestType(Integer  requestType)
    {
        this.requestType = requestType;
    }
    public String  getTimeStr()
    {
        return timeStr;
    }
    public void setTimeStr(String  timeStr)
    {
        this.timeStr = timeStr;
    }
    public String  getOrderPrefix()
    {
        return orderPrefix;
    }
    public void setOrderPrefix(String  orderPrefix)
    {
        this.orderPrefix = orderPrefix;
    }
    public Integer  getSignType()
    {
        return signType;
    }
    public void setSignType(Integer  signType)
    {
        this.signType = signType;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

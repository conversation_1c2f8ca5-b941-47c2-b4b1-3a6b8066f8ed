import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/companyInfo/list',
    method: 'post',
    params: query
  })
}

export function fetchCompanyInfo(id) {
  return request({
    url: '/companyInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createCompanyInfo(data) {
  return request({
    url: '/companyInfo/add',
    method: 'post',
    data
  })
}

export function updateCompanyInfo(data) {
  return request({
    url: '/companyInfo/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/companyInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeCompanyInfo(id) {
  return request({
    url: '/companyInfo/remove',
    method: 'get',
    params: { id }
  })
}


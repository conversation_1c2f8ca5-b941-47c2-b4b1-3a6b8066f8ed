import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/withdrawalSetting/list',
    method: 'post',
    params: query
  })
}

export function fetchWithdrawalSetting(id) {
  return request({
    url: '/withdrawalSetting/detail',
    method: 'get',
    params: { id }
  })
}

export function createWithdrawalSetting(data) {
  return request({
    url: '/withdrawalSetting/add',
    method: 'post',
    data
  })
}

export function updateWithdrawalSetting(data) {
  return request({
    url: '/withdrawalSetting/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/withdrawalSetting/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeWithdrawalSetting(id) {
  return request({
    url: '/withdrawalSetting/remove',
    method: 'get',
    params: { id }
  })
}


package com.ews.system.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.ews.common.Result;
import com.ews.config.jwt.JWTUtil;
import com.ews.system.entity.Role;
import com.ews.system.entity.User;
import com.ews.system.entity.UserRole;
import com.ews.system.model.IRolePermission;
import com.ews.system.repository.RoleRepository;
import com.ews.system.service.RolePermissionService;
import com.ews.system.service.RoleService;
import com.ews.system.service.UserRoleService;
import com.ews.system.service.UserService;

@Service
public class RoleServiceImpl implements RoleService {
	@Autowired
	private RoleRepository roleRepository;
	@Autowired
	private UserService userService;
	@Autowired
	private UserRoleService userRoleService;
	
	@Autowired
	private RolePermissionService rolePermissionService;

	@SuppressWarnings("serial")
	@Override
	public Page<Role> findAll(Integer page, Integer size, String sortName, String sortOrder, Role role) {
		// TODO Auto-generated method stub
		Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC,
				sortName);
		Pageable pageable = PageRequest.of(page, size, sortLocal);

		Page<Role> pages = roleRepository.findAll(new Specification<Role>() {
			@Override
			public Predicate toPredicate(Root<Role> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				List<Predicate> predicates = new ArrayList<>();

				if (!StringUtils.isEmpty(role.getRoleName())) {
					predicates.add(criteriaBuilder.like(root.get("roleName").as(String.class),
							"%" + role.getRoleName() + "%"));
				}
				if (!StringUtils.isEmpty(role.getRoleSign())) {
					predicates.add(criteriaBuilder.like(root.get("roleSign").as(String.class), role.getRoleSign()));
				}

				if (!StringUtils.isEmpty(role.getDescription())) {
					predicates.add(criteriaBuilder.like(root.get("description").as(String.class),
							"%" + role.getDescription() + "%"));
				}
				if (!StringUtils.isEmpty(role.getIsAvailable())) {
					predicates.add(
							criteriaBuilder.equal(root.get("isAvailable").as(Integer.class), role.getIsAvailable()));
				}
				
				if (!StringUtils.isEmpty(role.getUserCreate())) {
					predicates.add(
							criteriaBuilder.equal(root.get("userCreate").as(Long.class), role.getUserCreate()));
				}
				
				predicates.add(criteriaBuilder.notEqual(root.get("roleSign").as(String.class),"-10000"));//系统内置的初始化用户权限不查询出来
				
				SimpleDateFormat fm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

				query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
				return query.getRestriction();
			}

		}, pageable);
		return pages;
	}

	@Override
	public Role findById(Long roleId) {
		Optional<Role> r = roleRepository.findById(roleId);
		if (r != null && r.get() != null) {
			return r.get();
		}
		return null;
	}

	@Transactional
	@Override
	public Role saveOrUpdate(Role role) {
		try {
			if (role.getRoleId() == null) {
				
				String token = SecurityUtils.getSubject().getPrincipal().toString();
				String username = JWTUtil.getUsername(token);
				if(!StringUtils.isEmpty(username)) {
					User user = this.userService.findByUserName(username);
					role.setGmtCreate(new Date());
					role.setIsDeleted(0);
					role.setUserCreate(user.getUserId());
					role.setIsAvailable(1);
					role.setGmtModified(new Date());
					return roleRepository.save(role);
				}
			} else {
				Role old = this.roleRepository.findRoleByRoleId(role.getRoleId());
				old.setDescription(role.getDescription());
				old.setGmtModified(new Date());
				old.setRoleName(role.getRoleName());
				old.setRoleSign(role.getRoleSign());
				return roleRepository.save(old);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return null;
	}

	// 删除角色权限和角色
	@Transactional
	@Override
	public boolean deleteAllByRoleIdIn(List<Long> roleIdList) {
		try {
			for (Long roleId : roleIdList) {
				roleRepository.deleteRolePermission(roleId);
			}
			roleRepository.deleteAllByRoleIdList(roleIdList);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	@Override
	public List<IRolePermission> findRolePermissionByRoleId(Long roleId) {
		return roleRepository.findRolePermissionByRoleId(roleId);
	}

	// 授权前先清除原角色权限，然后重新新增授权
	@Transactional
	@Override
	public void grantAuthorization(Long roleId, Long[] permissionList) {
		try {
			System.out.println("开始删除所选角色的权限信息------------------> " + roleId);
			roleRepository.deleteRolePermission(roleId);
			for (Long permissionId : permissionList) {
				System.out.println("插入角色权限信息 --------------------->" + permissionId);

				roleRepository.insertRolePermission(roleId, permissionId);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Transactional
	@Override
	public void clearAuthorization(Long roleId) {
		roleRepository.deleteRolePermission(roleId);
	}

	@Transactional
	@Override
	public Result removeRoleByRoleId(Long roleId) {
		// TODO Auto-generated method stub

		Result vr = new Result();

		if (this.roleRepository.existsById(roleId)) {
			
			//删除角色
			this.roleRepository.deleteById(roleId);
			//删除角色-权限关联
			this.rolePermissionService.deleteByRoleId(roleId);
			//删除角色-用户关联
			this.userRoleService.deleteByRoleId(roleId);
			
			
			vr.setCode(Result.CODESUCCESS.toString());
			vr.setMessage("删除成功");
		} else {
			vr.setCode(Result.CODEFAIL);
			vr.setErrType(1);
			vr.setMessage("删除失败，数据不存在");
		}
		return vr;
	}

	@Override
	@Transactional
	public boolean updateIsAvailableById(Long roleId, Integer isAvailable) {
		// TODO Auto-generated method stub
		try {
			if (this.roleRepository.updateIsAvailableById(roleId, isAvailable) == 1) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return false;
	}

	@Override
	public List<Role> findAll(Role queryRole) {
		// TODO Auto-generated method stub
		//queryRole.setIsAvailable(1);
		Sort sortLocal = new Sort(Sort.Direction.DESC, "roleId");
		return this.roleRepository.findAll(new Specification<Role>() {
			@Override
			public Predicate toPredicate(Root<Role> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				List<Predicate> predicates = new ArrayList<>();

				if (!StringUtils.isEmpty(queryRole.getIsAvailable())) {
					predicates.add(criteriaBuilder.equal(root.get("isAvailable").as(Integer.class),
							queryRole.getIsAvailable()));
				}
				if (!StringUtils.isEmpty(queryRole.getUserCreate())) {
					predicates.add(
							criteriaBuilder.equal(root.get("userCreate").as(Long.class), queryRole.getUserCreate()));
				}
				//系统内置的初始化用户权限不查询出来
				predicates.add(criteriaBuilder.or(criteriaBuilder.isNull(root.get("roleSign")),criteriaBuilder.notEqual(root.get("roleSign").as(String.class),"-10000")));
				
				query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
				return query.getRestriction();
			}

		}, sortLocal);
	}

	@Override
	public List<Role> findAllByUserId(Long userId) {
		List<Role> list  = new ArrayList();
		if(userId == null) {
			return null;
		}
		List<UserRole> urs = this.userRoleService.findByUserId(userId);
		for(UserRole ur :urs) {
			Role role = this.findById(ur.getRoleId());
			if(role.getIsAvailable()==1) {
				list.add(role);
			}
		}
		return list;
	}

	@Override
	public boolean checkExistedByIdAndRoleSign(Long roleId, String roleSign) {
		// TODO Auto-generated method stub
		Role role = this.roleRepository.findRoleExistsByIdAndRoleSign(roleId, roleSign);
		if (role != null) {
			return true;
		}
		return false;
	}

	@Override
	public boolean checkExistedByIdAndRoleName(Long roleId, String roleName) {
		Role role = this.roleRepository.findRoleExistsByIdAndRoleName(roleId, roleName);
		if (role != null) {
			return true;
		}
		return false;
	}

	@Override
	public Map<String, String> checkExistedByIdAndRoleNameAndRoleSign(Long roleId, String roleName, String roleSign) {
		Map<String, String> map = new HashMap();
		if (roleId == null) {
			roleId = Long.MIN_VALUE;
		}
		map.put("existed", "false");
		if (this.checkExistedByIdAndRoleName(roleId, roleName)) {
			map.put("existed", "true");
			map.put("type", "1");
		} 
		
		/*取消判断标识
		else if (this.checkExistedByIdAndRoleSign(roleId, roleSign)) {
			map.put("existed", "true");
			map.put("type", "2");
		}
		*/
		return map;
	}

	@Transactional
	@Override
	public Result saveOrUpdateRoleAndPermission(Role role, String[] permissionIdsArr) {
		Result vr = new Result();
		Map result = this.checkExistedByIdAndRoleNameAndRoleSign(role.getRoleId(), role.getRoleName(),
				role.getRoleSign());
		if (result.get("existed").equals("false")) {
			try {
				System.out.println("开始更新角色信息---------------------------->");
				Role re = this.saveOrUpdate(role);
				if(re!=null) {
					System.out.println("准备处理的 permission数量：" + permissionIdsArr.length);
					if (permissionIdsArr != null && permissionIdsArr.length > 0) {

						Long[] permissionIds = new Long[permissionIdsArr.length];
						for (int i = 0; i < permissionIdsArr.length; i++) {
							permissionIds[i] = Long.parseLong(permissionIdsArr[i]);
						}
						System.out.println("数组类型转换完成，准备授权------------------>");
						// 授权
						this.grantAuthorization(role.getRoleId(), permissionIds);
					} else {
						// 授权
						this.grantAuthorization(role.getRoleId(), new Long[] {});
					}
					vr.setCode(Result.CODESUCCESS.toString());
				}else {
					vr.setMessage("系统异常，更新失败");
					vr.setErrType(4);
				}
			} catch (Exception ex) {
				ex.printStackTrace();
				vr.setCode(Result.CODEFAIL);
				vr.setMessage("系统异常，更新失败");
				vr.setErrType(4);
			}

		} else {
			vr.setCode(Result.CODEFAIL);
			if (result.get("type").equals("1")) {// 用户名重复
				vr.setErrType(2);
				vr.setMessage("角色名重复");
			} else if (result.get("type").equals("2")) {// 权限标识已存在
				vr.setMessage("权限标识已存在");
				vr.setErrType(3);
			} else {// 更新失败，其他异常
				vr.setMessage("系统异常，更新失败");
				vr.setErrType(4);
			}
		}

		return vr;
	}

}

import request from '@/utils/request'

export function getRoutes() {
  return request({
    url: '/permission/permissions',
    method: 'get'
  })
}

export function getRoles() {
  return request({
    url: '/role/roles',
    method: 'get'
  })
}

export function addRole(data) {
  return request({
    url: '/role/add',
    method: 'post',
    data
  })
}

export function updateRole(id, data) {
  return request({
    url: `/role/edit?roleId=${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/role/remove?roleId=${id}`,
    method: 'get'
  })
}

export function disableRole(id) {
  return request({
    url: `/role/disableRole?id=${id}`,
    method: 'disableRole',
		 method: 'get'
  })
}

export function enableRole(id) {
  return request({
    url: `/role/enableRole?id=${id}`,
    method: 'enableRole',
		 method: 'get'
  })
}
export function getRoutesByRoleId(roleId) {
  return request({
    url: '/permission/permissions?roleId=' + roleId,
    method: 'get'
  })
}
// 更新角色状态
export function updateIsAvailable(roleId, isAvailable) {
  return request({
    url: '/role/updateIsAvailable',
    method: 'get',
    params: { roleId, isAvailable }
  })
}

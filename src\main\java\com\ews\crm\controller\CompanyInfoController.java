
package com.ews.crm.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.service.CompanyInfoService;



@RestController
@RequestMapping("/admin/companyInfo")
public class CompanyInfoController {
	@Autowired
	private CompanyInfoService companyInfoService;


   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	CompanyInfo query  = new CompanyInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("companyName"))) {
               	query.setCompanyName(request.getParameter("companyName").trim());
        	}
        	Page<CompanyInfo> pages = companyInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<CompanyInfo> companyInfos = pages.getContent();
        	for(int i=0;i<companyInfos.size();i++) {
        		CompanyInfo entity  = companyInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", companyInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			CompanyInfo companyInfo = new CompanyInfo();
        	companyInfo.setCompanyName(data.getString("companyName"));
        	companyInfo.setCompanyLogo(data.getString("companyLogo"));
        	companyInfo.setLoginLogo(data.getString("loginLogo"));
        	companyInfo.setSystemName(data.getString("systemName"));
        	companyInfo.setBackup1(data.getString("backup1"));
        	companyInfo.setBackup3(data.getString("backup3"));
        	companyInfo.setWebUrl(data.getString("webUrl"));
        	companyInfo.setCommunityUrl(data.getString("communityUrl"));
        	companyInfo.setEmail(data.getString("email"));
        	companyInfo.setTelephone(data.getString("telephone"));
			Result re = companyInfoService.saveOrUpdate(companyInfo);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(companyInfo);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			CompanyInfo companyInfo = this.companyInfoService.findById(id);
				if (companyInfo != null) {
					companyInfo.setCompanyName(data.getString("companyName"));
					companyInfo.setCompanyLogo(data.getString("companyLogo"));
					companyInfo.setLoginLogo(data.getString("loginLogo"));
					companyInfo.setSystemName(data.getString("systemName"));
					companyInfo.setBackup1(data.getString("backup1"));
					companyInfo.setBackup3(data.getString("backup3"));
					companyInfo.setWebUrl(data.getString("webUrl"));
					companyInfo.setCommunityUrl(data.getString("communityUrl"));
					companyInfo.setEmail(data.getString("email"));
					companyInfo.setTelephone(data.getString("telephone"));
					Result re = companyInfoService.saveOrUpdate(companyInfo);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update error");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = companyInfoService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = companyInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	@GetMapping("/detail")
	public ResponseData detail(@Valid Long id, HttpServletRequest request) {
		if (id != null) {
			try {
				
				CompanyInfo companyInfo = this.companyInfoService.findById(id);
				return ResponseDataUtil.buildSuccess(companyInfo);
				
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError("param error");
		}
	}


}



package com.ews.crm.controller;

import java.io.File;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.ews.common.EncryptUtils;
import com.ews.common.DateUtil;
import com.ews.common.LoginResult;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.common.Result;

import com.alibaba.fastjson.JSONObject; 
import com.alibaba.fastjson.JSONArray;


import com.ews.crm.service.DepositBankService;
import com.ews.crm.entity.DepositBank;



@RestController
@RequestMapping("/admin/depositBank")
public class DepositBankController {
	@Autowired
	private DepositBankService depositBankService;




   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	DepositBank query  = new DepositBank();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	Page<DepositBank> pages = depositBankService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<DepositBank> depositBanks = pages.getContent();
        	for(int i=0;i<depositBanks.size();i++) {
        		DepositBank entity  = depositBanks.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", depositBanks);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			DepositBank depositBank = new DepositBank();
        	depositBank.setBankName(data.getString("bankName"));
        	depositBank.setBankAccount(data.getString("bankAccount"));
        	depositBank.setBankAddress(data.getString("bankAddress"));
        	depositBank.setAccountName(data.getString("accountName"));
        	depositBank.setTel(data.getString("tel"));
        	depositBank.setSwift(data.getString("swift"));
        	depositBank.setCurrencyType(data.getInteger("currencyType"));
        	depositBank.setIsShow(data.getInteger("isShow"));
        	depositBank.setBackup1(data.getString("backup1"));
        	depositBank.setBackup2(data.getString("backup2"));
        	depositBank.setBackup3(data.getString("backup3"));
        	depositBank.setBackup4(data.getString("backup4"));
        	depositBank.setBackup5(data.getString("backup5"));
        	depositBank.setBackup6(data.getString("backup6"));
        	depositBank.setBackup7(data.getString("backup7"));
        	depositBank.setBackup8(data.getString("backup8"));
        	depositBank.setBackup9(data.getString("backup9"));
        	depositBank.setBackup10(data.getString("backup10"));
        	depositBank.setBackup11(data.getString("backup11"));
        	depositBank.setBackup12(data.getString("backup12"));
        	depositBank.setBackup13(data.getString("backup13"));
        	depositBank.setBackup14(data.getString("backup14"));
        	depositBank.setBackup15(data.getString("backup15"));
        	depositBank.setBackup16(data.getString("backup16"));
        	depositBank.setBackup17(data.getString("backup17"));
        	depositBank.setBackup18(data.getString("backup18"));
        	depositBank.setBackup18(data.getString("backup19"));
        	depositBank.setBackup20(data.getString("backup20"));
			Result re = depositBankService.saveOrUpdate(depositBank);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(depositBank);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			DepositBank depositBank = this.depositBankService.findById(id);
				if (depositBank != null) {
					depositBank.setBankName(data.getString("bankName"));
					depositBank.setBankAccount(data.getString("bankAccount"));
					depositBank.setBankAddress(data.getString("bankAddress"));
					depositBank.setAccountName(data.getString("accountName"));
					depositBank.setTel(data.getString("tel"));
					depositBank.setSwift(data.getString("swift"));
					depositBank.setCurrencyType(data.getInteger("currencyType"));
					depositBank.setIsShow(data.getInteger("isShow"));
					depositBank.setBackup1(data.getString("backup1"));
		        	depositBank.setBackup2(data.getString("backup2"));
		        	depositBank.setBackup3(data.getString("backup3"));
		        	depositBank.setBackup4(data.getString("backup4"));
		        	depositBank.setBackup5(data.getString("backup5"));
		        	depositBank.setBackup6(data.getString("backup6"));
		        	depositBank.setBackup7(data.getString("backup7"));
		        	depositBank.setBackup8(data.getString("backup8"));
		        	depositBank.setBackup9(data.getString("backup9"));
		        	depositBank.setBackup10(data.getString("backup10"));
		        	depositBank.setBackup11(data.getString("backup11"));
		        	depositBank.setBackup12(data.getString("backup12"));
		        	depositBank.setBackup13(data.getString("backup13"));
		        	depositBank.setBackup14(data.getString("backup14"));
		        	depositBank.setBackup15(data.getString("backup15"));
		        	depositBank.setBackup16(data.getString("backup16"));
		        	depositBank.setBackup17(data.getString("backup17"));
		        	depositBank.setBackup18(data.getString("backup18"));
		        	depositBank.setBackup18(data.getString("backup19"));
		        	depositBank.setBackup20(data.getString("backup20"));
					Result re = depositBankService.saveOrUpdate(depositBank);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update error");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = depositBankService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = depositBankService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}


}


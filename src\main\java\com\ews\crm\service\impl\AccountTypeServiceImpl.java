package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.AccountType;
import com.ews.crm.repository.AccountTypeRepository;
import com.ews.crm.service.AccountTypeService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class AccountTypeServiceImpl implements AccountTypeService 
{
	@Autowired
	private AccountTypeRepository accountTypeRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<AccountType> findAll(Integer page, Integer size,String sortName,String sortOrder, AccountType accountType) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<AccountType> pages = accountTypeRepository.findAll(new Specification<AccountType>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<AccountType> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(accountType.getTypeName())) { 
                predicates.add(criteriaBuilder.like(root.get("typeName").as(String.class),"%"+accountType.getTypeName()+"%"));
             }
             if(!StringUtils.isEmpty(accountType.getGroupName())) { 
                predicates.add(criteriaBuilder.like(root.get("groupName").as(String.class),"%"+accountType.getGroupName()+"%"));
             }
             if(!StringUtils.isEmpty(accountType.getIsShow())) { 
                predicates.add(criteriaBuilder.equal(root.get("isShow").as(Integer.class), accountType.getIsShow()));
             }
             if(!StringUtils.isEmpty(accountType.getOpenType())) { 
                predicates.add(criteriaBuilder.equal(root.get("openType").as(Integer.class), accountType.getOpenType()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public AccountType findById(Long id) {
      Optional<AccountType> op = accountTypeRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.accountTypeRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         AccountType old = accountTypeRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 accountTypeRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 accountTypeRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.accountTypeRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(AccountType accountType) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (accountType.getId()== null) {
            	accountType.setGmtCreate(new Date());
            	accountType.setGmtModified(new Date());
            	accountType.setIsDeleted(0);
            	if(accountType.getIsAvailable() == null) {
            		accountType.setIsAvailable(1);
            	}
            	accountType.setUserCreate(loginUser.getUserId());
	    	} else {
            	accountType.setGmtModified(new Date());
        	}
            accountTypeRepository.save(accountType);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



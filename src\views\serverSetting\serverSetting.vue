<template>
  <div class="app-container">

    <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
      <el-form-item label="交易平台" prop="platformType">
        <el-select v-model="temp.platformType" :placeholder="$t('userTable.placeholder3')">
          <el-option
            v-for="platformType in platformTypes"
            :key="platformType.value"
            :label="platformType.label"
            :value="platformType.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服务器类型" prop="serverType">
        <el-select v-model="temp.serverType" :placeholder="$t('userTable.placeholder3')">
          <el-option
            v-for="serverType in serverTypes"
            :key="serverType.value"
            :label="serverType.label"
            :value="serverType.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服务器名称" prop="serverName">
        <el-input v-model="temp.serverName" />
      </el-form-item>
      <el-form-item label="接口地址" prop="apiAddress">
        <el-input v-model="temp.apiAddress" readonly />
      </el-form-item>
      <el-form-item label="接口端口" prop="apiPort">
        <el-input v-model="temp.apiPort" readonly />
      </el-form-item>
      <el-form-item label="权限用户" prop="backup1">
        <el-input v-model="temp.backup1" readonly />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateData()">保存</el-button>
    </div>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchServerSetting, createServerSetting, updateServerSetting, updateIsAvailable, removeServerSetting } from '@/api/serverSetting'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'ServerSettingTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        platformType: undefined,
        serverType: undefined
      },
      temp: {
        id: undefined,
        platformType: '',
        serverType: '',
        serverName: '',
        apiAddress: '',
        apiPort: '',
        backup1: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 platformTypes: [
        {
          value: 1,
          label: 'MetaTrader 4'
        },
        {
          value: 2,
          label: 'MetaTrader 5'
        }
      ],
				 serverTypes: [
        {
          value: 1,
          label: 'Live'
        },
        {
          value: 2,
          label: 'Demo'
        }
      ],
      rules: {
        platformType: [
          { required: true, message: '交易平台不能为空', trigger: 'change' },,
        ],
        serverType: [
          { required: true, message: '服务器类型不能为空', trigger: 'change' },,
        ],
        serverName: [
          { required: true, message: '服务器名称不能为空', trigger: 'change' },,
        ],
        apiAddress: [
          { required: true, message: '接口地址不能为空', trigger: 'change' },,
        ],
        apiPort: [
          { required: true, message: '接口端口不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    fetchServerSetting(1).then(res => {
				  this.temp = Object.assign({}, res.data)
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        platformType: '',
        serverType: '',
        serverName: '',
        apiAddress: '',
        apiPort: '',
        backup1: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createServerSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateServerSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeServerSetting(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

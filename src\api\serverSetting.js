import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/serverSetting/list',
    method: 'post',
    params: query
  })
}

export function fetchServerSetting(id) {
  return request({
    url: '/serverSetting/detail',
    method: 'get',
    params: { id }
  })
}

export function createServerSetting(data) {
  return request({
    url: '/serverSetting/add',
    method: 'post',
    data
  })
}

export function updateServerSetting(data) {
  return request({
    url: '/serverSetting/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/serverSetting/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeServerSetting(id) {
  return request({
    url: '/serverSetting/remove',
    method: 'get',
    params: { id }
  })
}


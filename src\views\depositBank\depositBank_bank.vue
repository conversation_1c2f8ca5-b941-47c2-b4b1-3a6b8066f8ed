<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开户银行" prop="bankName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="银行账号" prop="bankAccount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收款人" prop="accountName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.accountName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="swift" prop="swift" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.swift }}</span>
        </template>
      </el-table-column>
      <el-table-column label="货币类型" prop="currencyType" min-width="150px" align="center" :formatter="formatCurrencyType" />
      <el-table-column label="是否显示" prop="isShow" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.isShow==1?'显示':'不显示' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="开户银行" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankAccount">
          <el-input v-model="temp.bankAccount" />
        </el-form-item>
        <el-form-item label="银行地址" prop="bankAddress">
          <el-input v-model="temp.bankAddress" />
        </el-form-item>
        <el-form-item label="收款人" prop="accountName">
          <el-input v-model="temp.accountName" />
        </el-form-item>
        <el-form-item label="联系电话" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item label="swift" prop="swift">
          <el-input v-model="temp.swift" />
        </el-form-item>
        <el-form-item label="货币类型" prop="currencyType">
          <el-select v-model="temp.currencyType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="currencyType in currencyTypes"
              :key="currencyType.value"
              :label="currencyType.label"
              :value="currencyType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否显示" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="开户银行" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankAccount">
          <el-input v-model="temp.bankAccount" />
        </el-form-item>
        <el-form-item label="银行地址" prop="bankAddress">
          <el-input v-model="temp.bankAddress" />
        </el-form-item>
        <el-form-item label="收款人" prop="accountName">
          <el-input v-model="temp.accountName" />
        </el-form-item>
        <el-form-item label="联系电话" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item label="swift" prop="swift">
          <el-input v-model="temp.swift" />
        </el-form-item>
        <el-form-item label="货币类型" prop="currencyType">
          <el-select v-model="temp.currencyType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="currencyType in currencyTypes"
              :key="currencyType.value"
              :label="currencyType.label"
              :value="currencyType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否显示" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchDepositBank, createDepositBank, updateDepositBank, updateIsAvailable, removeDepositBank } from '@/api/depositBank'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'DepositBankTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      },
      temp: {
        id: undefined,
        bankName: '',
        bankAccount: '',
        bankAddress: '',
        accountName: '',
        tel: '',
        swift: '',
        currencyType: '',
        isShow: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 currencyTypes: [
        {
          value: 1,
          label: 'USD'
        },
        {
          value: 2,
          label: 'CNY'
        }
        /*,
					{
						value: 3,
						label: 'HKD'
					},
					{
						value: 4,
						label: 'EUR'
					}*/
      ],
				 isShows: [
        {
          value: 1,
          label: '显示'
        },
        {
          value: 2,
          label: '不显示'
        }
      ],
      rules: {
        bankName: [
          { required: true, message: '开户银行不能为空', trigger: 'change' },,
        ],
        bankAccount: [
          { required: true, message: '银行账号不能为空', trigger: 'change' },,
        ],
        bankAddress: [
          { required: true, message: '银行地址不能为空', trigger: 'change' },,
        ],
        accountName: [
          { required: true, message: '收款人不能为空', trigger: 'change' },,
        ],
        tel: [
          { required: true, message: '联系电话不能为空', trigger: 'change' },,
        ],
        swift: [
        ],
        currencyType: [
          { required: true, message: '货币类型不能为空', trigger: 'change' },,
        ],
        isShow: [
          { required: true, message: '是否显示不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        bankName: '',
        bankAccount: '',
        bankAddress: '',
        accountName: '',
        tel: '',
        swift: '',
        currencyType: '',
        isShow: ''
      }
    },
    formatCurrencyType(row, column) {
      if (row.currencyType == 1) {
        return 'USD'
      } else if (row.currencyType == 2) {
        return 'CNY'
      } else if (row.currencyType == 3) {
        return 'HKD'
      } else if (row.currencyType == 4) {
        return 'EUR'
      } else {
        return 'CNY'
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createDepositBank(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateDepositBank(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeDepositBank(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

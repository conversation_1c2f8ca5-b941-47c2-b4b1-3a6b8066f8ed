<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.username" :placeholder="$t('userTable.userName')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.nickName" :placeholder="$t('userTable.nickName')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.parent_ID" :placeholder="$t('userTable.superiorAgent')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <!--
      <el-select v-model="listQuery.type" :placeholder="$t('table.type')" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in calendarTypeOptions" :key="item.key" :label="item.display_name+'('+item.key+')'" :value="item.key" />
      </el-select>
      <el-select v-model="listQuery.sort" style="width: 140px" class="filter-item" @change="handleFilter">
        <el-option v-for="item in sortOptions" :key="item.key" :label="item.label" :value="item.key" />
      </el-select>
			-->

      <el-select v-model="listQuery.isAvailable" style="width: 100px" :placeholder="$t('userTable.status')" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker v-model="valueGmtGreate" class="filter-item" type="daterange" align="right" unlink-panels :range-separator="$t('userTable.zhi')" :start-placeholder="$t('userTable.beginDate')" :end-placeholder="$t('userTable.endDate')" :picker-options="pickerOptions" style="width: 380px" />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        {{ $t('userTable.search') }}
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        {{ $t('userTable.add') }}
      </el-button>
    </div>

    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <!--id序号-->
      <el-table-column :label="$t('userTable.number')" align="center" width="50">
        <template slot-scope="scope">
          <span>{{ scope.row.sort }}</span>
        </template>
      </el-table-column>
      <!--用户名-->
      <el-table-column :label="$t('userTable.userName')" width="100px" align="center" sortable="custom" prop="username">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <!--姓名-->
      <el-table-column :label="$t('userTable.nickName')" width="100px" align="center" sortable="custom" prop="nickName">
        <template slot-scope="scope">
          <span>{{ scope.row.nickName }}</span>
        </template>
      </el-table-column>
      <!--所属角色-->
      <el-table-column :label="$t('userTable.roleName')" min-width="100px" align="center">
        <template slot-scope="scope">
          <div v-for="roleName in scope.row.rolesNameArr" style="margin-top: 0.13rem;">{{ roleName }}</div>
        </template>
      </el-table-column>
      <!--联系电话-->
      <el-table-column :label="$t('userTable.mobile')" width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>

      <!--创建时间-->
      <el-table-column :label="$t('userTable.createDate')" prop="gmtCreate" sortable="custom" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreateDateLong | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.userType')" width="100px" align="center" :formatter="fmtUserType" />
      <el-table-column :label="$t('userTable.superAgent')" width="200px" align="center" prop="storeName">
        <template slot-scope="scope">
          <span>{{ scope.row.storeName }}</span>
        </template>
      </el-table-column>
      <!--状态  active-value前加: 标识字段类型为数字 -->

      <el-table-column :label="$t('userTable.status')" class-name="status-col" width="100">
        <template v-if="isSuper==999||(scope.row.roleType !=0&&scope.row.roleType !=999)" slot-scope="scope">
          <el-switch v-model="scope.row.isAvailable" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" @change="handleIsAvailableChange(scope.row)" />
        </template>
      </el-table-column>
      <!--操作  -->
      <el-table-column :label="$t('userTable.actions')" align="left" width="520" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="isSuper==999||(scope.row.roleType !=0&&scope.row.roleType !=999)" type="primary" size="mini" @click="handleUpdate(scope.row)">
            {{ $t('userTable.edit') }}
          </el-button>
          <el-button v-if="isSuper==999||(scope.row.roleType !=0&&scope.row.roleType !=999)" type="warning" size="small" @click="handleResetPassword(scope.row)">
            {{ $t('userTable.resetPassword') }}
          </el-button>
          <el-button v-if="isSuper==999||(scope.row.roleType !=0&&scope.row.roleType !=999)" size="mini" type="danger" @click="handleDelete(scope.row)">
            {{ $t('userTable.delete') }}
          </el-button>
          <el-button v-if="(scope.row.roleType !=0)?true:false" size="small" type="success" @click="handleSub(scope.row)">
            {{ $t('userTable.button1') }}
          </el-button>
          <el-button v-if="(scope.row.roleType !=0)?true:false" size="small" type="Info" @click="handleCRM(scope.row)">
            {{ $t('userTable.button2') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页显示-->
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <el-dialog :title="$t('userTable.button2')" :visible.sync="dialogFormVisible2" :close-on-click-modal="false">
      <el-form ref="dataForm" :model="temp" label-position="right" label-width="180px" style="max-width: 400px; margin-left:10px;">
        <el-form-item :label="$t('userTable.crmUserEmail')" prop="crm_username">
          <el-input v-model="temp.crm_username" :placeholder="$t('userTable.placeholder1')" style="width:300px;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible2 = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="ep_crm()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <!--添加弹窗-->
    <el-dialog :title="$t('userTable.add')" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 400px; margin-left:10px;">
        <el-form-item :label="$t('userTable.userName')" prop="username">
          <el-input v-model="temp.username" :placeholder="$t('userTable.userName')" />
        </el-form-item>

        <el-form-item :label="$t('userTable.nickName')" prop="nickName">
          <el-input v-model="temp.nickName" :placeholder="$t('userTable.nickName')" />
        </el-form-item>

        <el-form-item :label="$t('userTable.mobile')" prop="mobile">
          <el-input v-model="temp.mobile" :placeholder="$t('userTable.mobile')" />
        </el-form-item>

        <el-form-item :label="$t('userTable.roleName')">
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-for="role in roles" v-model="checkedRoles" max="1" @change="handleCheckedCitiesChange">
            <el-checkbox :key="role.roleId" :label="role.roleId">{{ role.roleName }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('userTable.initialPassword')" prop="password">
          <el-input v-model="temp.password" :placeholder="$t('userTable.initialPassword')" />
        </el-form-item>
        <el-form-item :label="$t('userTable.userType')" prop="roleType">
          <el-select v-model="temp.roleType">
            <el-option
              v-for="item in roleTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="(temp.roleType !=0)?true:false" :label="$t('userTable.label1')" prop="storeId">
          <el-input v-model="temp.storeId" :placeholder="$t('userTable.placeholder2')" />
        </el-form-item>

        <el-form-item v-if="(temp.roleType !=0)?true:false" :label="$t('userTable.label2')">
          <el-select v-model="temp.rekebackRule" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="(temp.roleType !=0&&temp.reId=='')?true:false" :label="$t('userTable.label3')" prop="parent_ID">
          <el-input v-model="temp.parent_ID":placeholder="$t('userTable.placeholder4')" />
        </el-form-item>
        <el-form-item :label="$t('userTable.status')" prop="isAvailable">
          <el-select v-model="temp.isAvailable" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="createData()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>

    <!--编辑弹窗-->
    <el-dialog :title="$t('userTable.edit')" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 600px; margin-left:10px;">

        <el-form-item :label="$t('userTable.userName')" prop="username">
          <el-input v-model="temp.username" :placeholder="$t('userTable.userName')" readonly />
        </el-form-item>

        <el-form-item :label="$t('userTable.nickName')" prop="nickName">
          <el-input v-model="temp.nickName" :placeholder="$t('userTable.nickName')" />
        </el-form-item>

        <el-form-item :label="$t('userTable.mobile')" prop="mobile">
          <el-input v-model="temp.mobile" :placeholder="$t('userTable.mobile')" />
        </el-form-item>
        <el-form-item :label="$t('userTable.roleName')">
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-for="role in roles" v-model="checkedRoles" max="1" @change="handleCheckedCitiesChange">
            <el-checkbox :key="role.roleId" :label="role.roleId">{{ role.roleName }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item :label="$t('userTable.userType')" prop="roleType">
          <el-select v-model="temp.roleType">
            <el-option
              v-for="item in roleTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="(temp.roleType !=0)?true:false" :label="$t('userTable.label1')" prop="storeId">
          <el-input v-model="temp.storeId" :placeholder="$t('userTable.placeholder2')" />
        </el-form-item>
        <el-form-item v-if="(temp.roleType !=0)?true:false" :label="$t('userTable.label2')">
          <el-select v-model="rekebackRule2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="(temp.roleType !=0)?true:false" :label="$t('userTable.label3')" prop="parent_ID">
          <el-input v-model="parent_ID2" :placeholder="$t('userTable.placeholder4')" />
        </el-form-item>

        <el-form-item :label="$t('userTable.status')" prop="isAvailable">
          <el-select v-model="temp.isAvailable" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="updateData()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>
    <!--重置密码弹窗-->
    <el-dialog :title="$t('userTable.resetPassword')" :visible.sync="dialogFormResetPasswordVisible" :close-on-click-modal="false">
      <el-form ref="dataPassForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 400px; margin-left:10px;">
        <el-form-item :label="$t('userTable.newPassword')" prop="password">
          <el-input v-model="temp.password" :placeholder="$t('userTable.newPassword')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormResetPasswordVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="resetPassword()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { fetchList, fetchUser, createUser, updateUser, updateIsAvailable, resetPassword, removeUser, fetchRoleList, fetchUserRoleList, fetchSaleMgn, bindCrmUser } from '@/api/user'
import { listshowRule } from '@/api/rekebackRule'
// import {fetchDepartmentList} from '@/api/department'
// import {fetchStoreList} from '@//api/store'
import waves from '@/directive/waves' // waves directive
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth.js'

import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import { getInfo } from '@/api/navbar'

var roles = []
var count = 0
var checkedRoles = []
export default {
  name: 'UserTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      uploadUrl: 'http://localhost:8099/user/uploadUserIcon',
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      parent_ID2:'',
      listQuery: {
        page: 1,
        limit: 10,
        parent_ID: undefined,
        username: undefined,
        nickName: undefined,
        sort: '-gmtCreate',
        isAvailable: undefined,
        gmtCreateBegin: undefined,
        gmtCreateEnd: undefined
      },
      uploadData: {
        id: undefined,
        type: 1
      },
      myHeaders: {
				 'X-Token': getToken()
      },
      valueGmtGreate: undefined,
      isIndeterminate: false,
      checkAll: false,
      checkedRoles: checkedRoles,
      roles: roles,
      isSuper: 0,
      departments: [],
      stores: [],
      saleMgns: [],
		  options: [{
        value: 1,
        label: this.$t('permission.enable')
      }, {
        value: 0,
        label: this.$t('permission.disable')
      }],
      roleTypes: [
        /*
          {
				     value: 0,
				     label: '后台管理员'
				  },*/
        {
				    value: 1,
				    label: this.$t('userType.agentIB')
				  }, {
				    value: 2,
				    label: this.$t('userType.sales')
				  }, {
				    value: 3,
				    label: this.$t('userType.salesManager')
				  }, {
				    value: 4,
				    label: this.$t('userType.salesDirector')
				  }],
      pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Months',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      // 表单数据默认绑定
      temp: {
        userId: undefined,
        departmentId: '',
        storeId: '',
        username: '',
        nickName: '',
        mobile: '',
        password: '',
        isAvailable: '',
        userIcon: '',
        roleType: '',
        crm_username: '',
        parent_ID: undefined,
        reId: '',
        roles: [],
        rekebackRule: [],
        ruleList: [],
        enclosureId: undefined,
        dqryType: undefined
      },
      option1s: [],
      rekebackRule2: [],
      dialogFormVisible2: false,
      dialogFormVisible: false, // 添加弹窗
      dialogFormEditVisible: false, // 编辑弹窗
      dialogFormResetPasswordVisible: false, // 重置密码弹窗

      rules: {// 验证消息
        username: [
          { required: true, message: 'enter one user name', trigger: 'change' },
          { min: 3, max: 50, message: 'Please enter 3 to 50 characters', trigger: 'change' }
        ],

        nickName: [
          { required: true, message: 'Please enter the employee name', trigger: 'change' },
          { max: 50, message: 'Enter up to 50 characters', trigger: 'change' }
        ],

        roleType: [
          { required: true, message: 'Please select a user type', trigger: 'change' }
        ],
        mobile: [
          { required: true, message: 'Please enter a phone number', trigger: 'change' },
          { max: 30, message: 'Enter up to 30 characters', trigger: 'change' }
        ],
        password: [
          { required: true, message: 'Please enter the password', trigger: 'change' },
          { min: 6, max: 30, message: 'Please enter 6 to 20 characters', trigger: 'change' }
        ],
        isAvailable: [
          { required: true, message: 'Please select user status', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getRoles()

    this.option1s = []

    listshowRule().then(response => {
      var datas4 = response.data.items
         	for (var j = 0; j < datas4.length; j++) {
         		var id = datas4[j].id

         		var name = datas4[j].ruleName
         		var per = []
         		per.value = id
         		per.label = name
        this.option1s.push(per)
      }
    })

    getInfo().then(response => {
    	console.log('userInfo:' + response.data.roleType)
    	if (response.code == 20000) {
        this.temp.dqryType = response.data.roleType
        this.isSuper = response.data.roleType
        if (response.data.roleType == 999) {
          var per = []
          per.value = 0
          per.label = this.$t('userType.administrator')
          this.roleTypes.push(per)
        } else {
        }
    	} else {
    		 this.$message({
    		  message: response.msg,
    		  type: 'error'
    		})
    	}
    })
  },
  methods: {

    async getDepartments() {
			 const res = await fetchDepartmentList()
			 this.departments = []
			 var data = res.data
			 	for (var j = 0; j < data.length; j++) {
			 		var id = data[j].id
        var name = data[j].departmentName
        var per = []
        per.value = id
        per.label = name
			 		this.departments.push(per)
			 }
    }, fmtUserType(row, column) {
      if (row.roleType == 0) {
        return this.$t('userType.administrator')
      } else if (row.roleType == 1) {
        return this.$t('userType.agentIB')
      } else if (row.roleType == 2) {
        return this.$t('userType.sales')
      } else if (row.roleType == 3) {
        return this.$t('userType.salesManager')
      } else if (row.roleType == 4) {
        return this.$t('userType.salesDirector')
      } else if (row.roleType == 999) {
        return this.$t('userType.superAdministrator')
      }
    },

    async getStores() {
			 const res = await fetchStoreList()
			 this.stores = []
			 var data = res.data
			 	for (var j = 0; j < data.length; j++) {
			 		var id = data[j].id
        var name = data[j].name
        var per = []
        per.value = id
        per.label = name
			 		this.stores.push(per)
			 }
    },
    handleCheckAllChange(val) {
      checkedRoles = []
      if (val == true) {
        for (var i = 0; i < roles.length; i++) {
          checkedRoles.push(roles[i].roleId)
        }
      }
      this.checkedRoles = checkedRoles
		    this.isIndeterminate = false
      this.checkAll = val
		  },
		  handleCheckedCitiesChange(value) {
		    const checkedCount = value.length
		    this.checkAll = checkedCount === count
		    this.isIndeterminate = checkedCount > 0 && checkedCount < count
      checkedRoles = value
      this.checkedRoles = checkedRoles
		  },
    handleIsAvailableChange(row) {
				 updateIsAvailable(row.userId, row.isAvailable).then(response => {
					 if (response.code != 20000) {
							 this.$message({
							  message: response.message,
							  type: 'error'
          })
					 }
      })
    },
    async getRoles() {
		  const res = await fetchRoleList()
      roles = []
      // 所有权限的数据集合
				 var data = res.data
      for (var j = 0; j < data.length; j++) {
        var per = data[j]
                	roles.push(per)
				   }

      if (data.length == 0) {
        var per2 = []
        per2.roleId = -100
        per2.roleName = 'Default Role'
        roles.push(per2)
      }

      this.roles = roles
      // 数据总数
      count = this.roles.length
    },
    handleAvatarSuccess(res, file) {
      console.log('上传成功 :' + res.data.id)
      console.log('==========:' + res.data.endixUrl)
      // this.imageUrl = URL.createObjectURL(file.raw);
      this.temp.userIcon = res.data.endixUrl
      this.temp.enclosureId = res.data.id
    },
    beforeAvatarUpload(file) {
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2

      /*
			if (!isJPG) {
				this.$message.error('上传头像图片只能是 JPG 格式!');
				return false;
			}
			* */
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        this.listLoading = false// 关闭加载动画
        // Just to simulate the time of the request
      })
    },
    handleFilter() {
      this.listQuery.page = 1

			 if (this.valueGmtGreate) {
				 this.listQuery.gmtCreateBegin = parseTime(new Date(this.valueGmtGreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
				 this.listQuery.gmtCreateEnd = parseTime(new Date(this.valueGmtGreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
			 } else {
				 	 this.listQuery.gmtCreateBegin = undefined
					 this.listQuery.gmtCreateEnd = undefined
			 }
      this.getList()
    },

    getSaleMgn(query) {
					 this.saleMgns = []

						  fetchSaleMgn().then(res => {
							 this.saleMgns = []
							 var data = res.data.items
							 	for (var j = 0; j < data.length; j++) {
							 		var id = data[j].userId
							 		var name = data[j].nickName
							 		var per = []
							 		per.value = id
							 		per.label = name
							 		this.saleMgns.push(per)
							 }
      })
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
      }
      if (prop === 'username') {
        this.sortByUserName(order)
      }
      if (prop === 'nickName') {
        this.sortByNickName(order)
      }
      if (prop === 'storeId') {
        this.sortByStoreId(order)
      }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    sortByUserName(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+username'
      } else {
        this.listQuery.sort = '-username'
      }
      this.handleFilter()
    },
    sortByNickName(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+nickName'
      } else {
        this.listQuery.sort = '-nickName'
      }
      this.handleFilter()
    },
    sortByStoreId(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+storeId'
      } else {
        this.listQuery.sort = '-storeId'
      }
      this.handleFilter()
    },
    // 还原表单 temp
    resetTemp() {
      this.temp = {
        userId: undefined,
			 departmentId: undefined,
			 storeId: undefined,
        username: '',
        nickName: '',
        mobile: '',
        password: '',
        isAvailable: '',
	   crm_username: '',
			 userIcon: '',
			 	roleType: '',
        parent_ID: undefined,
			 reId: '',
			 roles: [],
        rekebackRule: [],
        ruleList: [],
			 enclosureId: undefined
      }
    },
    // 重置密码页面
    handleResetPassword(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogFormResetPasswordVisible = true
      this.$nextTick(() => {
			  this.$refs['dataForm'].clearValidate()
      })
    },
        resetPassword() {
		 this.$refs['dataPassForm'].validate((valid) => {
		  if (valid) {
          const tempData = Object.assign({}, this.temp)
		    resetPassword(tempData.userId, tempData.password).then(result => {
            if (result.code == 20000) {
				 this.$notify({
				  title: 'success',
				  message: 'update success',
				  type: 'success',
				  duration: 2000
              })
              this.dialogFormResetPasswordVisible = false
              // 重置 temp 对象，确保不影响其他弹窗
              this.resetTemp()
            } else {
              this.$message.error(response.message)
            }
		    })
		  }
      })
    },
    // 新增数据页面
    handleCreate() {
      this.resetTemp()
      checkedRoles = []
      this.checkedRoles = []
      this.isIndeterminate = false
      this.checkAll = false
      this.dialogFormVisible = true
	  this.temp.password = this.generateRandomString()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    generateRandomString() {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < 8; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length)
        result += characters[randomIndex]
      }
      return result
    },

    // 新增数据页面
    handleSub(row) {
      this.resetTemp()
      checkedRoles = []
      this.checkedRoles = []
      this.isIndeterminate = false
      this.checkAll = false
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.reId = row.userId
      console.log(row.userId + '    ' + this.temp.reId)
    }, handleCRM(row) {
      this.dialogFormVisible2 = true
      this.temp.crm_username = ''
      this.temp.userId = row.userId
    }, ep_crm() {
      bindCrmUser(this.temp).then(result => {
        // this.list.unshift(this.temp)
      	if (result.code == 20000) {
      		 this.$notify({
      		  title: 'success',
      		  message: 'Assigned successfully',
      		  type: 'success',
      		  duration: 2000
      		})
      		this.dialogFormVisible2 = false
      	} else {
      		this.$message.error(response.msg)
      	}
      })
    },
    // 保存数据
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          		this.temp.roles = this.checkedRoles
          createUser(this.temp).then(result => {
            // this.list.unshift(this.temp)
            if (result.code == 20000) {
							 this.$notify({
							  title: 'success',
							  message: 'Created successfully',
							  type: 'success',
							  duration: 2000
              })
              this.getList()
              this.dialogFormVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
    },
    // 编辑数据页面
    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({}, row) // copy obj
      this.dialogFormVisible = false
      this.dialogFormEditVisible = true

      this.isIndeterminate = false
      this.checkAll = false
      this.temp.storeId = row.storeId
      this.temp.parent_ID=row.depaName
      this.parent_ID2=row.depaName

      console.log(this.temp.storeId + ':' + this.temp.departmentId)
      console.log()

      // 选中用户参数
      fetchUserRoleList(this.temp.userId).then(result => {
        if (result.code == 20000) {
          checkedRoles = []
          this.checkedRoles = []
          for (var j = 0; j < result.data.length; j++) {
            var per = result.data[j].roleId
            checkedRoles.push(per)
          }

          this.checkedRoles = checkedRoles
          if (this.checkedRoles.length > 0) {
            if (this.checkedRoles.length == this.roles.length) {
              this.checkAll = true
            } else {
              this.isIndeterminate = true
            }
          }
        } else {
          this.$message.error(result.msg)
        }
      })

      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })

      if (row.ruleList != null) {
        this.rekebackRule2 = []
        var datas13 = row.ruleList
        for (var j = 0; j < datas13.length; j++) {
          this.rekebackRule2.push(datas13[j].ruleId)
        }
      }
    },
    // 更新数据
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.roles = this.checkedRoles
          const tempData = Object.assign({}, this.temp)

          tempData.timestamp = +new Date(tempData.timestamp) // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
          tempData.rekebackRule = this.rekebackRule2
          tempData.parent_ID=this.parent_ID2

          updateUser(tempData).then(() => {
            this.$notify({
              title: 'success',
              message: 'update success',
              type: 'success',
              duration: 2000
            })
            this.dialogFormEditVisible = false
            // 重置 temp 对象，确保不影响其他弹窗
            this.resetTemp()
            this.getList()
          })
        }
      })
    },
    // 删除数据
    handleDelete(row) {
      // 提示用户是否确定删除
			 this.$confirm('Are you sure you want to delete this user?', 'INFO', {
			  confirmButtonText: 'OK',
			  cancelButtonText: 'CANCEL',
			  type: 'warning'
      })
			  .then(async() => {
			    await removeUser(row.userId).then(result => {
            if (result.code == 20000) {
											 	this.$notify({
											 		title: 'success',
											 		message: 'delete success',
											 		type: 'success',
											 		duration: 2000
											 	})
											 const index = this.list.indexOf(row)
											 this.list.splice(index, 1)
            } else {
											 this.$message.error(result.msg)
            }
          })
			  })
			  .catch(err => { console.error(err) })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    }
  }
}
</script>

<style>
	.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>

import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/userTag/list',
    method: 'post',
    params: query
  })
}

export function fetchALLList(query) {
  return request({
    url: '/userTag/list',
    method: 'post',
    params: query
  })
}

export function fetchUserTag(id) {
  return request({
    url: '/userTag/detail',
    method: 'get',
    params: { id }
  })
}

export function createUserTag(data) {
  return request({
    url: '/userTag/add',
    method: 'post',
    data
  })
}

export function updateUserTag(data) {
  return request({
    url: '/userTag/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/userTag/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeUserTag(id) {
  return request({
    url: '/userTag/remove',
    method: 'get',
    params: { id }
  })
}


export default {
  route: {
    dashboard: 'Task Overview',
    welcome: 'Workstation',
    documentation: 'Documents',
    guide: 'Guide',
    permission: 'System Setting',
    rolePermission: 'Role Management',
    userPermission: 'Admin/Agent',
    companyInfo: 'Company Information',
    exchangeRate: 'Exchange Rate Settings',
    lever: 'Leverage Settings',
    webContent: 'System Notification',
    emailInfo: 'Email Settings',
    accountType: 'Account Type',
    serverSetting: 'Server Setup',
    tradeProd: 'Product Setup',
    operLog: 'Operation Log',
    userTag: 'User Tag',
    tradeSetting: 'Trading Setting',
    userGroup: 'User Group',
    rekebackRule: 'Commission Rule',
    addRekebackRule: 'Add Commission Rule',
    editRekebackRule: 'Edit Commission Rule',
    copyRekebackRule: 'Duplicate Commission Rule',
    deposit: 'Deposit Channel',
    withdrawal: 'Withdrawal Channel',
    withdrawalcontrol: 'Risk Management',
    auditManager: 'Approval Management',
    auditUser: 'User Approval',
    auditAccount: 'Trading Account Approval',
    depositAudit: 'Deposit Approval',
    withdrawalAudit: 'Withdrawal Approval',
    withdrawalAudit2: 'Payment Approval',
    withdrawalAudit3: 'Payment Failure',
    auditTransfer: 'Internal Transfer Approval',
    userManager: 'User Management',
    userList: 'Agent Account',
    agentAchievement: 'Asset Under Management',
    crmUserList: 'CRM User',
    tradeUserList: 'Trading Account',
    financeManager: 'Finance Log & Alert',
    depositList: 'Deposit Log',
    withdrawalList: 'Withdrawal Log',
    transferList: 'Internal Transfer Log',
    rekebackList: 'Commission Log',
    crmUserListquery: 'View CRM User',
    tradeUserListquery: 'View Trading Accounts',
    label1: 'View CRM User',
    label2: 'View Trading Account',
    label3: 'Pending Deposit',
    label4: 'Pending Withdrawal',
    label5: 'Payment Approval',
    label6: 'Payment Failure'

  },
  navbar: {
    logOut: 'Log out',
	 userInfo: 'User Info',
	 changePass: 'Change Password',
    theme: 'Theme',
    size: 'Size Setting',
	  userName: 'Username',
    password: 'Password',
    initialPassword: 'Initial Password',
    newPassword: 'New Password',
    againPassword: 'Re-enter New Password',
		 nickName: 'Name',
		 storeName: 'Agency Name',
		  depaName: 'Department Name',
    roleName: 'Role',
    mobile: 'Mobile Number',
		 edit: 'Edit',
    invitationLink: 'Invitation Link',
    qrcode: 'Invitation QR Code'
  },
  dashboard: {
    title1: 'Approval Pending',
    title2: 'User Approval Pending',
    title3: 'Deposit Pending',
    title4: 'Withdrawal Pending',
    title5: 'Trading Account Pending',
    title6: 'Internal Transfer Pending'

  },
  login: {
    title: 'MyClient-SeventyBrokers Backoffice',
    logIn: 'Login',
    username: 'Username',
    password: 'Password',
    verifycode: 'Verification Code',
    refreshcode: 'Refresh Verification Code',
    prompt1: 'Please enter your username',
    prompt2: 'Please enter your password'

  },
  role: {
    roleName: 'Role',
    status: 'Status',
    roleDescription: 'Role Description',
    oper: 'Actions',
    permission: 'Permission',
    allSelect: 'Select All'
  },
  documentation: {
    documentation: 'Documentation',
    github: 'Github link'
  },
  permission: {
    addRole: 'Add',
    editPermission: 'Edit',
    'Delete': 'Delete',
    enable: 'Enable',
    disable: 'Disabled',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  guide: {
  },
  components: {
    documentation: 'Documentation'
  },
  table: {
    dynamicTips1: 'Fixed title, sort according to title',
    dynamicTips2: 'Unfixed title, sort according to click order',
    dragTips1: 'Default Order',
    dragTips2: 'Order after arrangement',
    title: 'Title',
    importance: 'Importance',
    type: 'Type',
    remark: 'Remark',
    search: 'Search',
    add: 'Add',
    'export': 'Export',
    reviewer: 'Approver',
    id: 'No.',
    date: 'Time',
    author: 'Author',
    readings: 'Views',
    status: 'Status',
    actions: 'Actions',
    edit: 'Edits',
    publish: 'Publish',
    draft: 'Draft',
    'delete': 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm',
    close: 'Close',
    save: 'Save'
  },
  userTable: {
    userName: 'Username',
    password: 'Password',
    initialPassword: 'Initial Password',
    newPassword: 'New Password',
    resetPassword: 'Reset Password',
    nickName: 'Name',
    storeName: 'Agency Name',
    depaName: 'Department Name',
    roleName: 'Role',
    permission: 'Permission',
    saleType: 'Agent type',
    saleMgn: 'Sales Manager',
    saleSerial: 'Agent ID',
    mobile: 'Contact Number',
    search: 'Search',
    add: 'Add',
    enable: 'Enabled',
    disable: 'Disabled',
    doEnable: 'Enable',
    doDisable: 'Disable',
    'export': 'Export',
    id: 'No.',
    createDate: 'Created on',
    author: 'Created by',
    status: 'Status',
    actions: 'Actions',
    edit: 'Edit',
    'delete': 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm',
    superiorAgent: 'Supervioser ID',
    zhi: 'To',
    beginDate: 'Start Date',
    endDate: 'End Date',
    number: 'No.',
    userType: 'Role',
    superAgent: 'Supervisor',
    button1: 'Create Sub Agent',
    button2: 'Assign CRM User',
    crmUserEmail: 'CRM User Email',
    placeholder1: 'Please enter CRM user Email address',
    label1: 'Commission Account',
    placeholder2: 'Please enter your Commission Account No.',
    label2: 'Commission Type',
    placeholder3: 'Please Select',
    placeholder4: 'Please Enter Supervisor Name',
    label3: 'Supervisor Name'

  },
  userType: {
    administrator: 'Administrator',
    agentIB: 'Agent/IB',
    sales: 'Sales',
    salesManager: 'Sales Manager',
    salesDirector: 'Sales Director',
    superAdministrator: 'Super Administrator'
  },
  companyInfo: {
    label1: 'CRM Name',
    label2: 'Welcome Message',
    label3: 'Company Name',
    label4: 'Company Logo',
    label5: 'Login Page Banner',
    label6: 'Official Website Link',
    label7: 'Community Link',
    label8: 'Email Address',
    label9: 'Contact Number',
    label10: 'More Info',
    button1: 'Update'

  },
  webContent: {
    placeholder1: 'Content Title',
    label1: 'Content Title',
    label2: 'Content Type',
    label3: 'Published Date',
    label4: 'Content'
  },
  userTag: {
    placeholder1: 'Tag',
    label1: 'Tag Description'
  },
  operLog: {
    placeholder1: 'Done by',
    placeholder2: 'Type of Action',
    placeholder3: 'Action',
    placeholder4: 'Log Details',
    placeholder5: 'Time',
    busType1: 'Agent/IB/Administrator',
    busType2: 'CRM users',
    busType3: 'Cash deposit',
    busType4: 'Cash out',
    busType5: 'Account with the same name',
    busType6: 'Transfer with the same name',
    busType7: 'User Account',
    operType1: 'Add',
    operType2: 'Edit',
    operType3: 'Delete',
    operType4: 'Approve'
  }, lever: {
    label1: 'Leverage Ratio',
    label2: 'Remark'
  }, tradeProd: {
    label1: 'Product Name',
    label2: 'Description',
    button1: 'Synchronize Product'
  },
  userGroup: {
    label1: 'MT5 Group Name',
    label2: 'Currency Type',
    label3: 'Description',
    button1: 'Sync Group'
  },
  accountType: {
    label1: 'Account Type',
    label2: 'MT5 Group',
    label3: 'Show on CRM',
    label4: 'Credit Type',
    label5: 'Credit Amount',
    label6: 'MT5 Number from',
    label7: 'MT5 Number To',
    label8: 'Select Leverage',
    label9: 'Automated Transfer Approval',
    label10: 'Automated Account Approval',
    show: 'Show',
    hidden: 'Hidden',
    scale: 'Scale',
    regular: 'Regular',
    selectAll: 'Select All',
    dialogTitleAdd: 'Add Account Type',
    dialogTitleEdit: 'Edit Account Type',
    typeNameEmpty: 'Type name cannot be empty',
    groupNameEmpty: 'MT group name cannot be empty',
    isShowEmpty: 'The foreground display cannot be empty',
    openTypeEmpty: 'Credit type cannot be empty',
    creditValueEmpty: 'Credit value cannot be empty',
    autoAudit: 'Auto Audit',
    manualReview: 'Manual Review',
    maxAccounts: 'Max Accounts',
    operSuccess: 'Operation Successful',
    deleteConfirm: 'Are you sure you want to delete this data?',
    deleteSuccess: 'Delete Successful'
  },
  rekebackRule: {
    label1: 'Rule Name',
    label2: 'Created on',
    label3: 'Director Commission',
    label4: 'Manager Commission',
    label5: 'Sales Rebate',
    label6: 'Commission 1 tier above',
    label7: 'Commission 2 tier above',
    label8: 'Commission 3 tier above',
    label9: 'Total Commission',
    label10: 'Copy',
    label11: 'External Commission Tier',
    label12: 'Commission 4 tier above',
    label13: 'Commission 5 tier above',
    label14: 'Commission 6 tier above',
    label15: 'Commission 7 tier above',
    label16: 'Commission 8 tier above',
    label17: 'Commission 9 tier above',
    label18: 'Commission 10 tier above',
    label19: 'Commission 11 tier above',
    label20: 'Commission 12 tier above',
    label21: 'Commission 13 tier above',
    label22: 'Commission 14 tier above',
    label23: 'Commission 15 tier above',
    label24: 'Commission 16 tier above',
    label25: 'Commission 17 tier above',
    label26: 'Commission 18 tier above',
    label27: 'Commission 19 tier above',
    label28: 'Commission 20 tier above',
    label29: 'Commission 21 tier above',
    label30: 'Commission 22 tier above',
    label31: 'Commission 23 tier above',
    label32: 'Commission 24 tier above',
    label33: 'Commission 25 tier above',
    label34: 'Commission 26 tier above',
    label35: 'Commission 27 tier above',
    label36: 'Commission 28 tier above',
    label37: 'Commission 29 tier above',
    label38: 'Commission 30 tier above',
    label39: 'Related Product',
    label40: 'Associated Group',
    label41: 'Remarks',
    level3: '3 tier',
    level4: '4 tier',
    level5: '5 tier',
    level6: '6 tier',
    level7: '7 tier',
    level8: '8 tier',
    level9: '9 tier',
    level10: '10 tier',
    level11: '11 tier',
    level12: '12 tier',
    level13: '13 tier',
    level14: '14 tier',
    level15: '15 tier',
    level16: '16 tier',
    level17: '17 tier',
    level18: '18 tier',
    level19: '19 tier',
    level20: '20 tier',
    level21: '21 tier',
    level22: '22 tier',
    level23: '23 tier',
    level24: '24 tier',
    level25: '25 tier',
    level26: '26 tier',
    level27: '27 tier',
    level28: '28 tier',
    level29: '29 tier',
    level30: '30 tier'
  },
  depositBank: {
    label1: 'Deposit Method',
    label2: 'Channel Type',
    label3: 'Payment Link',
    label4: 'Currency',
    label5: 'Bank Name',
    label6: 'Bank Address',
    label7: 'Account Number',
    label8: 'Receiving Party',
    label9: 'Digital Wallet Address',
    label10: 'Mark up (%)',
    label11: 'Min. Deposit',
    label12: 'Max Deposit',
    label13: 'KYC Status',
    label14: 'Allowed Country',
    label15: 'Hide from Account',
    label16: 'Hide from Tag',
    label17: 'Witelisted IP'
  },
  withdrawalSetting: {
    label1: 'Allowed Countries',
    label2: 'Hide from Account',
    label3: 'Hide from Tag',
    label4: 'Max. Withdrawal Amount',
    label5: 'Min. Traders\' Own Cash',
    label6: 'Min. % of Free Cash Margin'

  },
  withdrawalBank: {
    label1: 'Withdrawal Method',
    label2: 'Withdrawal Link',
    label3: 'Priority',
    label4: 'Currency',
    label5: 'Markdown(%)',
    label6: 'Min. Withdrawal',
    label7: 'Max. Withdrawal',
    label8: 'Allowed Countries',
    label9: 'Hide from Accounts Types',
    label10: 'Hide from Tag',
    label11: 'Whitedlisted IP'

  }, userInfoAudit: {
    label1: 'Username',
    label2: 'Name',
    label3: 'Mobile Number',
    label4: 'Nationality',
    label5: 'Home Address',
    label6: 'ID Number',
    label7: 'Photo',
    label8: 'Date of Birth',
    label9: 'Country of Residence',
    label10: 'City',
    label11: 'Mobile Number',
    label12: 'Address',
    label13: 'Application Rejected',
    label14: 'Reason for Rejection',
    label15: 'Approve',
    label16: 'Reject',
    label17: 'Time of Registration',
    label18: 'Review',
    label19: 'Cancel'

  },
  tradeAccountAudit: {
    label1: 'Application Time',
    label2: 'Name',
    label3: 'Email Address',
    label4: 'Mobile Number',
    label5: 'Country',
    label6: 'Trading Group',
    label7: 'Leverage',
    label8: 'Account Type',
    label9: 'Approve',
    label10: 'Reject'

  }, depositAudit: {
    label1: 'CRM User',
    label2: 'Trading Account',
    label3: 'Deposit Channel Details',
    label4: 'Deposit Amount',
    label5: 'Digital Wallet Address',
    label6: 'Attachment',
    label7: 'Remarks',
    label8: 'Deposit Amount',
    label9: 'Review Remark',
    label10: 'Approve',
    label11: 'Reject',
    label12: 'Application Time'
  }, withdrawalAudit: {
    label1: 'CRM User',
    label2: 'Trading Account',
    label3: 'Method',
    label4: 'Withdrawal Amount',
    label5: 'Currency',
    label6: 'Amount User Received',
    label7: 'Payment Account',
    label8: 'Approval Remark',
    label9: 'Current Balance',
    label10: 'Approve',
    label11: 'Reject',
    label12: 'Application Time',
    label13: 'Remark',
    label14: 'Payment Account'
  }, transferInfoAudit: {
    label1: 'Application Time',
    label2: 'Transfer from',
    label3: 'Transfer to',
    label4: 'Name',
    label5: 'Email Address',
    label6: 'Time',
    label7: 'Amount',
    label8: 'Approve',
    label9: 'Reject'
  }, user2: {
    label1: 'Supervisor Account ID',
    label2: 'Commission Account',
    label3: 'Direct Superior',
    label4: 'Invitation Code'
  },
  user3: {
    label1: 'No. of Agent',
    label2: 'No. of CRM User',
    label3: 'No. of Trading Accounts',
    label4: 'Trading Account Cash balance',
    label5: 'Total Deposit',
    label6: 'Total Withdrawal'

  }, userInfo: {
    label1: 'Email Address',
    label2: 'Name',
    label3: 'Mobile Number',
    label4: 'Nationality',
    label5: 'Address',
    label6: 'ID Number',
    label7: 'Photo',
    label8: 'Date of Birth',
    label9: 'Country of Residence',
    label10: 'City',
    label11: 'Mobile Number',
    label12: 'Address',
    label13: 'Account Managers/ ID',
    label14: 'Account Tags',
    label15: 'View',
    label16: 'Edit',
    label17: 'Created on',
    addCrmUser: 'Add CRM User',
    updateInfo: 'Update Info',
    viewInfo: 'User Information',
    deleteConfirmMessage: 'Are you sure you want to delete this CRM user?',
    deleteConfirmTitle: 'INFO',
    bankName: 'Bank/Branch Name',
    bankAccount: 'Bank Account Number',
    inputFirstName: 'Enter first name',
    inputLastName: 'Enter last name',
    notifications: {
      success: 'Success',
      passwordResetSuccess: 'Successfully modified password',
      operSuccess: 'Operation successful',
      deleteSuccess: 'Delete successful'
    },
    validation: {
      surnameRequired: 'Surname is required',
      nameRequired: 'Name is required',
      userNameRequired: 'Email address is required',
      passwordRequired: 'Password is required',
      fileSizeLimit: 'Image size cannot exceed 2MB!',
      countryRequired: 'Country of residence is required',
      provinceRequired: 'Nationality is required'
    }
  }, tradeAccount: {
    label1: 'Trading Account',
    label2: 'Account Type',
    label3: 'Created on',
    label4: 'Linked CRM User',
    label5: 'Email Address',
    label6: 'Leverage',
    label7: 'Balance',
    label8: 'Credits',
    label9: 'Total Margin',
    label10: 'Trading Volume(Last 30 Days)',
    label11: 'Free Magin',
    label12: 'Used Margin',
    label13: 'First Password',
    label14: 'Linked CRM User',
    label15: 'Leverage',
    label16: 'Trading Group',
    label17: 'Add Trading Account',
    label18: 'Edit'
  }, depositList: {
    label1: 'Submitted on',
    label2: 'Processed on',
    label3: 'Email Address',
    label4: 'MT5 Account',
    label5: 'Deposit Amount',
    label6: 'Approval Status',
    label7: 'Status',
    label8: 'Remark',
    label9: 'Ticket No.'
  }, withdrawalList: {
    label1: 'Sumitted on',
    label2: 'Processed on',
    label3: 'Email Address',
    label4: 'MT5 Account',
    label5: 'Withdrawal Amount',
    label6: 'Status',
    label7: 'Request Status',
    label8: 'Remark',
    label9: 'Ticket No.',
    label10: 'Risk Control',
    label11: 'Finance Approval',
    label12: 'Payment Status',
    label13: 'MT5 Deduction Status'

  }, transferInfo: {
    label1: 'Submitted on',
    label2: 'From',
    label3: 'To',
    label4: 'Name',
    label5: 'Email Address',
    label6: 'Tranfer time',
    label7: 'Amount',
    label8: 'Processed Time',
    label9: 'Remark'
  }, reckbackInfo: {
    label1: 'Trading No.',
    label2: 'MT5 Account',
    label3: 'Lot Size',
    label4: 'Product Type',
    label5: 'Sales No.',
    label6: 'Commission Account',
    label7: 'Status',
    label8: 'Rebate Type',
    label9: 'Amount',
    label10: 'Submitted on',
    label11: 'Payout on'

  },
  example: {
  },
  errorLog: {
  },
  excel: {
    'export': 'Export',
    selectedExport: 'Export Selected'
  },
  zip: {
    'export': 'Export'
  },
  pdf: {
  },
  theme: {
    change: 'Theme'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  },
  settings: {
    title: 'System Setting',
    theme: 'Theme Color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'Fix Header',
    sidebarLogo: 'Sidebar Logo'
  },
  qt: {
    qt1: 'Retry',
    qt2: 'Cancel'
  },
  supplement: {
    label1: 'View CRM User',
    label2: 'View MT5 Account',
    label3: 'Deposit Pending Approval',
    label4: 'Withdrawal Pending Approval',
    label5: 'Payment Review',
    label6: 'Payment Failed',
    label7: 'Max. Number of account'

  }
}

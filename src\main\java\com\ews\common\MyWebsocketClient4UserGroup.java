package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.TradeProd;
import com.ews.crm.entity.UserGroup;
import com.ews.crm.service.TradeProdService;
import com.ews.crm.service.UserGroupService;


public class MyWebsocketClient4UserGroup extends WebSocketClient {
	
	
	
	public UserGroupService userGroupService;
	

	public UserGroupService getUserGroupService() {
		return userGroupService;
	}

	public void setUserGroupService(UserGroupService userGroupService) {
		this.userGroupService = userGroupService;
	}

	public MyWebsocketClient4UserGroup(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
            		JSONArray ja=JSONArray.parseArray(jsStr.getString("groupInfos"));
					for(int m=0;m<ja.size();m++) {
						JSONObject ob=(JSONObject)ja.get(m);
						try {
							
							UserGroup ug_query=new UserGroup();
							ug_query.setGroupName(ob.getString("groupName"));
						Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
						if(page.getContent().size()<=0) {
							UserGroup ug_t=new UserGroup();
							ug_t.setGroupName(ob.getString("groupName"));
							this.userGroupService.saveOrUpdate(ug_t);
						}
						}catch(Exception e) {
							System.out.println(e);
						}
                     }
				
			
		}
		new Thread(){
    		public void run(){
    			try {
    			Thread.sleep(180000L);
    			     close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

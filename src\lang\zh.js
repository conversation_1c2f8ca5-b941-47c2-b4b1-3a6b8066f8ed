export default {
  route: {
    dashboard: '首页',
    welcome: '工作台',
    documentation: '文档',
    guide: '引导页',
    permission: '系统设置',
    rolePermission: '角色管理',
    userPermission: '用户管理',
    companyInfo: '网站信息',
    exchangeRate: '汇率设置',
    lever: '杠杆设置',
    webContent: '公告管理',
    emailInfo: '邮箱设置',
    accountType: '账号类型',
    serverSetting: '服务器设置',
    tradeProd: '交易品种',
    operLog: '操作日志',
    userTag: '用户标签',
    tradeSetting: '交易设置',
    userGroup: '用户组',
    rekebackRule: '返佣规则组',
    addRekebackRule: '新增返佣规则组',
    editRekebackRule: '编辑返佣规则组',
    copyRekebackRule: '复制返佣规则组',
    deposit: '入金通道',
    withdrawal: '出金通道',
    withdrawalcontrol: '出金风控',
    auditManager: '审批管理',
    auditUser: '用户审批',
    auditAccount: '同名账号审批',
    depositAudit: '入金审批',
    withdrawalAudit: '出金审批',
    withdrawalAudit2: '支付审批',
    withdrawalAudit3: '支付失败',
    auditTransfer: '同名转账审批',
    userManager: '代理管理',
    userList: '代理用户',
    agentAchievement: '代理业绩',
    crmUserList: 'CRM用户',
    tradeUserList: '交易用户',
    financeManager: '财务管理',
    depositList: '入金记录',
    withdrawalList: '出金记录',
    transferList: '同名转账记录',
    rekebackList: '佣金记录',
    crmUserListquery: 'CRM用户查看',
    tradeUserListquery: '交易用户查看',
    label1: 'CRM用户查看',
    label2: '交易用户查看',
    label3: '入金待确认列表',
    label4: '出金待确认列表',
    label5: '支付审核',
    label6: '支付失败',
    orderList: '我的订单',
    orderManager: '订单管理',
    teamist: '团队订单',
    TeamList: '团队订单',
   

  },
  navbar: {
    logOut: '退出登录',
	 userInfo: '个人信息',
	 changePass: '修改密码',
    theme: '换肤',
    size: '布局大小',
	  userName: '账户ID',
    password: '登录密码',
    initialPassword: '初始密码',
    newPassword: '新密码',
    againPassword: '再次输入新密码',
		 nickName: '姓名',
		 storeName: '代理机构名称',
		  depaName: '所属部门',
    roleName: '角色',
    mobile: '联系电话',
		 edit: '编辑',
    invitationLink: '邀请链接',
    qrcode: '邀请二维码'
  },
  dashboard: {
    title1: '等待批准',
    title2: '待审批用户',
    title3: '入金待审批',
    title4: '出金待审批',
    title5: '同名账号审批',
    title6: '同名转账审批'

  },
  login: {
    title: 'MyClient-SeventyBrokers Backoffice',
    logIn: '登录',
    username: '账户ID',
    password: '账户密码',
    verifycode: '验证码',
    refreshcode: '刷新验证码',
    prompt1: '请输入您的账户ID',
    prompt2: '请输入您的密码'

  },
  role: {
    roleName: '角色名称',
    status: '状态',
    roleDescription: '角色描述',
    oper: '操作',
    permission: '权限',
    allSelect: '全选'
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '添加',
    editPermission: '编辑',
    'delete': '删除',
    enable: '启用',
    disable: '禁用',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
  },
  components: {
    documentation: '文档'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '备注',
    search: '搜索',
    add: '添加',
    'export': '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    'delete': '删除',
    cancel: '取消',
    confirm: '确定',
    close: '关闭',
    save: '保存'
  },
  userTable: {
    userName: '账户ID',
    password: '账户密码',
    initialPassword: '初始密码',
    newPassword: '新密码',
    resetPassword: '重置密码',
    nickName: '姓名',
    storeName: '代理机构名称',
    depaName: '所属部门',
    roleName: '角色',
    permission: '权限',
    saleType: '销售类型',
    saleMgn: '销售主管',
    saleSerial: '销售编号',
    mobile: '联系电话',
    search: '搜索',
    add: '添加',
    enable: '已启用',
    disable: '已禁用',
    doEnable: '启用',
    doDisable: '禁用',
    'export': '导出',
    id: '序号',
    createDate: '创建时间',
    author: '作者',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    'delete': '删除',
    cancel: '取消',
    confirm: '确定',
    superiorAgent: '上级代理账户ID',
    zhi: '至',
    beginDate: '创建开始日期',
    endDate: '创建结束日期',
    number: '序号',
    userType: '用户类型',
    superAgent: '上级代理',
    button1: '创建子代理',
    button2: '分配CRM用户',
    crmUserEmail: 'CRM用户邮箱',
    placeholder1: '请输入CRM用户的邮箱地址',
    label1: '佣金账户',
    placeholder2: '请输入佣金账户',
    label2: '返佣规则',
    placeholder3: '请选择',
    placeholder4: '请输入上级账户ID',
    label3: '上级ID'

  },
  userType: {
    administrator: '管理员',
    agentIB: '代理/IB',
    sales: '销售',
    salesManager: '销售经理',
    salesDirector: '销售总监',
    superAdministrator: '超级管理员'
  },
  companyInfo: {
    label1: '系统名称',
    label2: '欢迎语',
    label3: '公司名称',
    label4: '活动图片',
    label5: '登陆页图片',
    label6: '官网地址',
    label7: '社区地址',
    label8: '电子邮箱',
    label9: '联系电话',
    label10: '配置协议',
    button1: '更新'

  },
  webContent: {
    placeholder1: '内容标题',
    label1: '内容标题',
    label2: '内容类型',
    label3: '发表日期',
    label4: '内容详情'
  },
  userTag: {
    placeholder1: '标签',
    label1: '标签描述'
  },
  operLog: {
    placeholder1: '操作人名称',
    placeholder2: '业务类型',
    placeholder3: '操作类型',
    placeholder4: '日志详情',
    placeholder5: '操作时间',
    busType1: '代理/IB/管理员',
    busType2: 'CRM用户',
    busType3: '入金',
    busType4: '出金',
    busType5: '同名账户',
    busType6: '同名转账',
    busType7: '用户账户',
    operType1: '新增',
    operType2: '编辑',
    operType3: '删除',
    operType4: '审核'
  }, lever: {
    label1: '杠杆率',
    label2: '备注'
  }, tradeProd: {
    label1: '品种名称',
    label2: '描述',
    button1: '同步品种'
  },
  userGroup: {
    label1: 'MT用户组名',
    label2: '货币',
    label3: '描述',
    button1: '同步用户组'
  },
  accountType: {
    label1: '账号类型',
    label2: 'MT5组',
    label3: 'CRM中显示',
    label4: '信用类型',
    label5: '信用金额',
    label6: 'MT5账号起始',
    label7: 'MT5账号截止',
    label8: '选择杠杆',
    label9: '自动转账审批',
    label10: '自动账号审批',
    show: '显示',
    hidden: '隐藏',
    scale: '比例',
    regular: '常规',
    selectAll: '全选',
    dialogTitleAdd: '添加账号类型',
    dialogTitleEdit: '编辑账号类型',
    typeNameEmpty: '类型名称不能为空',
    groupNameEmpty: 'MT组名称不能为空',
    isShowEmpty: '前台显示不能为空',
    openTypeEmpty: '信用类型不能为空',
    creditValueEmpty: '信用值不能为空',
    autoAudit: '自动审核',
    manualReview: '人工审核',
    maxAccounts: '最大账户数',
    operSuccess: '操作成功',
    deleteConfirm: '确定要删除这些数据吗？',
    deleteSuccess: '删除成功'
  },
  rekebackRule: {
    label1: '规则名称',
    label2: '创建时间',
    label3: '总监返佣',
    label4: '销售经理返佣',
    label5: '销售返佣',
    label6: '上一级代理返佣',
    label7: '上二级代理返佣',
    label8: '上三级代理返佣',
    label9: '返佣总数',
    label10: '复制',
    label11: '外部返佣层级',
    label12: '上四级代理返佣',
    label13: '上五级代理返佣',
    label14: '上六级代理返佣',
    label15: '上七级代理返佣',
    label16: '上八级代理返佣',
    label17: '上九级代理返佣',
    label18: '上十级代理返佣',
    label19: '上十一级代理返佣',
    label20: '上十二级代理返佣',
    label21: '上十三级代理返佣',
    label22: '上十四级代理返佣',
    label23: '上十五级代理返佣',
    label24: '上十六级代理返佣',
    label25: '上十七级代理返佣',
    label26: '上十八级代理返佣',
    label27: '上十九级代理返佣',
    label28: '上二十级代理返佣',
    label29: '上二十一级代理返佣',
    label30: '上二十二级代理返佣',
    label31: '上二十三级代理返佣',
    label32: '上二十四级代理返佣',
    label33: '上二十五级代理返佣',
    label34: '上二十六级代理返佣',
    label35: '上二十七级代理返佣',
    label36: '上二十八级代理返佣',
    label37: '上二十久级代理返佣',
    label38: '上三十级代理返佣',
    label39: '关联交易产品',
    label40: '关联用户组',
    label41: '备注',
    level3: '3层级',
    level4: '4层级',
    level5: '5层级',
    level6: '6层级',
    level7: '7层级',
    level8: '8层级',
    level9: '9层级',
    level10: '10层级',
    level11: '11层级',
    level12: '12层级',
    level13: '13层级',
    level14: '14层级',
    level15: '15层级',
    level16: '16层级',
    level17: '17层级',
    level18: '18层级',
    level19: '19层级',
    level20: '20层级',
    level21: '21层级',
    level22: '22层级',
    level23: '23层级',
    level24: '24层级',
    level25: '25层级',
    level26: '26层级',
    level27: '27层级',
    level28: '28层级',
    level29: '29层级',
    level30: '30层级'
  },
  depositBank: {
    label1: '通道名称',
    label2: '通道类型',
    label3: '请求地址',
    label4: '通道货币',
    label5: '银行名字',
    label6: '银行地址',
    label7: '银行账号',
    label8: '收款公司',
    label9: '数字货币地址',
    label10: '汇率加点(%)',
    label11: '最小入金',
    label12: '最大入金',
    label13: 'KYC状态',
    label14: '居住地可见',
    label15: '类型不可见',
    label16: '标签不可见',
    label17: 'IP白名单'
  },
  withdrawalSetting: {
    label1: '居住地可见',
    label2: '类型不可见',
    label3: '标签不可见',
    label4: '最大提款金额',
    label5: '交易者最低自有资金',
    label6: '可用保证金最低百分比'

  },
  withdrawalBank: {
    label1: '通道名称',
    label2: '请求地址',
    label3: '优先级',
    label4: '通道货币',
    label5: '汇率减点(%)',
    label6: '最小出金',
    label7: '最大出金',
    label8: '居住地可见',
    label9: '类型不可见',
    label10: '标签不可见',
    label11: 'IP白名单'

  }, userInfoAudit: {
    label1: '登陆账号',
    label2: '用户姓名',
    label3: '手机号',
    label4: '国籍',
    label5: '居住地',
    label6: '身份证号',
    label7: '照片',
    label8: '出生日期',
    label9: '居住国家',
    label10: '城市',
    label11: '手机号',
    label12: '地址',
    label13: '审核驳回',
    label14: '驳回原因',
    label15: '通过',
    label16: '驳回',
    label17: '注册时间',
    label18: '审核',
    label19: '取消'

  },
  tradeAccountAudit: {
    label1: '申请时间',
    label2: '所属用户',
    label3: 'CRM账号',
    label4: '手机号',
    label5: '地址',
    label6: '交易组别',
    label7: '交易杠杆',
    label8: '账户类型',
    label9: '审核',
    label10: '驳回'

  }, depositAudit: {
    label1: 'CRM用户',
    label2: '交易账号',
    label3: '入金通道信息',
    label4: '入金货币金额',
    label5: '数字货币地址',
    label6: '附件',
    label7: '备注',
    label8: '入金金额',
    label9: '审核备注',
    label10: '审核',
    label11: '驳回',
    label12: '申请时间'
  }, withdrawalAudit: {
    label1: 'CRM用户',
    label2: '交易账号',
    label3: '收款渠道',
    label4: '出金金额',
    label5: '到账货币',
    label6: '到账金额',
    label7: '付款账户',
    label8: '审核备注',
    label9: '当前余额',
    label10: '审核',
    label11: '驳回',
    label12: '申请时间',
    label13: '备注',
    label14: '付款账户'
  }, transferInfoAudit: {
    label1: '申请时间',
    label2: '转出账号',
    label3: '转入账号',
    label4: '所属CRM用户',
    label5: 'CRM账号',
    label6: '转账时间',
    label7: '转账金额',
    label8: '通过',
    label9: '驳回'
  }, user2: {
    label1: '上级代理账户ID',
    label2: '佣金账户',
    label3: '上级代理',
    label4: '邀请码'
  },
  user3: {
    label1: '代理数',
    label2: 'CRM用户数',
    label3: '交易账户数',
    label4: '交易账户余额',
    label5: '入金总额',
    label6: '出金总额'

  }, userInfo: {
    label1: '登陆账号',
    label2: '用户姓名',
    label3: '手机号',
    label4: '国籍',
    label5: '居住地',
    label6: '身份证号',
    label7: '照片',
    label8: '出生日期',
    label9: '居住国家',
    label10: '城市',
    label11: '手机号',
    label12: '地址',
    label13: '所属代理账户ID',
    label14: '用户标签',
    label15: '查看',
    label16: '修改',
    label17: '注册时间',
    addCrmUser: '新增CRM用户',
    updateInfo: '更新信息',
    viewInfo: '用户信息',
    deleteConfirmMessage: '确定要删除此CRM用户吗？',
    deleteConfirmTitle: '提示',
    bankName: '银行/支行名称',
    bankAccount: '银行账号',
    inputFirstName: '请输入名字',
    inputLastName: '请输入姓氏',
    notifications: {
      success: '成功',
      passwordResetSuccess: '密码修改成功',
      operSuccess: '操作成功',
      deleteSuccess: '删除成功'
    },
    validation: {
      surnameRequired: '姓名不能为空',
      nameRequired: '姓名不能为空',
      userNameRequired: '登陆邮箱不能为空',
      passwordRequired: '初始密码不能为空',
      fileSizeLimit: '上传图片大小不能超过 2MB!',
      countryRequired: '居住地不能为空',
      provinceRequired: '国籍不能为空'
    }
  }, tradeAccount: {
    label1: '交易账号',
    label2: '账户类型',
    label3: '开户时间',
    label4: '所属用户',
    label5: 'CRM账号',
    label6: '交易杠杆',
    label7: '余额',
    label8: '信用',
    label9: '净值',
    label10: '交易手数(30天)',
    label11: '已用预付款',
    label12: '可用预付款',
    label13: '初始密码',
    label14: '所属CRM用户',
    label15: '杠杆率',
    label16: '交易组',
    label17: '新增交易账号',
    label18: '修改'
  }, depositList: {
    label1: '申请时间',
    label2: '处理时间',
    label3: 'CRM账号',
    label4: '交易账号',
    label5: '入金金额',
    label6: '审核状态',
    label7: '处理状态',
    label8: '审核备注',
    label9: '订单号'
  }, withdrawalList: {
    label1: '申请时间',
    label2: '处理时间',
    label3: 'CRM账号',
    label4: '交易账号',
    label5: '出金金额',
    label6: '审核状态',
    label7: '处理状态',
    label8: '审核备注',
    label9: '订单号',
    label10: '风控审核',
    label11: '支付审核',
    label12: '支付情况',
    label13: 'MT出金状态'

  }, transferInfo: {
    label1: '申请时间',
    label2: '转出账号',
    label3: '转入账号',
    label4: '所属CRM用户',
    label5: 'CRM账号',
    label6: '转账时间',
    label7: '转账金额',
    label8: '处理时间',
    label9: '审核备注'
  }, reckbackInfo: {
    label1: '交易订单号',
    label2: '交易账号',
    label3: '交易手数',
    label4: '交易品种',
    label5: '返佣订单号',
    label6: '佣金账户',
    label7: '返佣状态',
    label8: '返佣类型',
    label9: '返佣金额',
    label10: '记录时间',
    label11: '返佣时间'

  },
  example: {
  },
  errorLog: {
  },
  excel: {
    'export': '导出',
    selectedExport: '导出已选择项'
  },
  zip: {
    'export': '导出'
  },
  pdf: {
  },
  theme: {
    change: '换肤'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  qt: {
    qt1: '重新执行',
    qt2: '取消执行'
  },
  supplement: {
    label1: 'CRM用户查看',
    label2: '交易用户查看',
    label3: '入金待确认列表',
    label4: '出金待确认列表',
    label5: '支付审核',
    label6: '支付失败',
    label7: '开通账号数(max)'

  }
}

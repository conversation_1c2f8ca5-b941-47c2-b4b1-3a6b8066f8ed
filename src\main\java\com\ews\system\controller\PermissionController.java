package com.ews.system.controller;

import java.util.List;
import java.util.Map;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.config.jwt.JWTUtil;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.system.entity.Permission;
import com.ews.system.entity.User;
import com.ews.system.service.PermissionService;
import com.ews.system.service.UserRoleService;
import com.ews.system.service.UserService;

@RestController
@RequestMapping("/admin/permission")
public class PermissionController {
	@Autowired
	private PermissionService permissionService;

	@Autowired
	private UserRoleService userRoleService;
	
	@Autowired
	private UserService userService;
	/**
	 * 用于角色编辑时的权限的查询
	 * 
	 * @param map
	 * @param token
	 * @return
	 */
	@RequestMapping(value = "/permissions", method = RequestMethod.GET)
	public Object permissions(Map<String, Object> map,Long roleId) {
		JSONObject result = new JSONObject();
		try {
			String token =SecurityUtils.getSubject().getPrincipal().toString();
			
			System.out.println("permission token:"+token);
			String username = JWTUtil.getUsername(token);
			
			System.out.println("转换出来的permission token:"+username);
			if(!StringUtils.hasText(username)) {
				return ResponseDataUtil.buildError(ResultEnums.VERIFY_CODE_ERROR);
			}
			User loginUser  = userService.findByUserName(username);
			
			JSONObject admin = new JSONObject();
			if (loginUser != null) {
				JSONArray permissionJSON = new JSONArray();
				if(roleId!=null) {//获取指定角色的权限信息
					List<Map> pers = permissionService.getAllPermissions(roleId);
					for (Map per : pers) {
						List<Permission> permissions = (List) per.get("permissions");
						for(Permission p :permissions) {
							if(p.getIsChecked()==1) {
								permissionJSON.add(p.getPermissionId());
							}
						}
					}
				}else {
					List<Map> pers = permissionService.getAllPermissions(null);
					for (Map per : pers) {
						List<Permission> permissions = (List) per.get("permissions");
						JSONObject group = new JSONObject();
						group.put("group", per.get("permissionGroup"));
						group.put("data",  JSONArray.toJSON(permissions));
						permissionJSON.add(group);
					}
				}
				
				result.put("data", permissionJSON);
				result.put("code", 20000);

			} else {
				result.put("code", 50008);
				result.put("message", "系统异常，请稍后再试！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.put("code", 60204);
			result.put("message", "系统异常，请稍后再试！");
		}
		return result;
	}

}

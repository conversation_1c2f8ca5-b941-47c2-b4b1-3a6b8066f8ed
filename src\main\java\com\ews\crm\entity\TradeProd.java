package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "trade_prod")
public class TradeProd implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *品种名称
    **/
	@Column(name = "prod_name")
	protected  String prodName;

    /**
    *描述
    **/
	@Column(name = "prod_describe")
	protected  String prodDescribe;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  Integer backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  Integer backup4;

	@Transient
	protected  Integer sortNum;
	
	@Transient
	protected  String prodName2;
	
	

    public String getProdName2() {
		return prodName2;
	}
	public void setProdName2(String prodName2) {
		this.prodName2 = prodName2;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getProdName()
    {
        return prodName;
    }
    public void setProdName(String  prodName)
    {
        this.prodName = prodName;
    }
    public String  getProdDescribe()
    {
        return prodDescribe;
    }
    public void setProdDescribe(String  prodDescribe)
    {
        this.prodDescribe = prodDescribe;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public Integer  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(Integer  backup3)
    {
        this.backup3 = backup3;
    }
    public Integer  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(Integer  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

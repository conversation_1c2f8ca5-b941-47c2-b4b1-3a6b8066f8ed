import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/userGroup/list',
    method: 'post',
    params: query
  })
}

export function fetchUserGroup(id) {
  return request({
    url: '/userGroup/detail',
    method: 'get',
    params: { id }
  })
}

export function createUserGroup(data) {
  return request({
    url: '/userGroup/add',
    method: 'post',
    data
  })
}

export function updateUserGroup(data) {
  return request({
    url: '/userGroup/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/userGroup/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeUserGroup(id) {
  return request({
    url: '/userGroup/remove',
    method: 'get',
    params: { id }
  })
}

export function listshow() {
  return request({
    url: '/userGroup/listshow',
    method: 'post'
  })
}
export function synchroGroup() {
  return request({
    url: '/userGroup/synchroGroup',
    method: 'get'
  })
}

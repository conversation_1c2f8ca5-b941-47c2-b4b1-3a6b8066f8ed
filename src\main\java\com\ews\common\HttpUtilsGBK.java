package com.ews.common;


import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.protocol.Protocol;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

public class HttpUtilsGBK {
//    @SuppressWarnings("deprecation")
//    public static String sendPostRequest(String url, String soapAction,
//                                         String body) throws IOException {
//        HttpClient httpClient = new HttpClient();
//        // 连接超时，毫秒
//        httpClient.getHttpConnectionManager().getParams()
//                .setConnectionTimeout(3000);
//        // 读取数据超时，毫秒
//        httpClient.getHttpConnectionManager().getParams().setSoTimeout(2000);
//        PostMethod postMethod = new Utf8PostMethod(url);
//
//        postMethod.setRequestHeader("Content-Type", "text/xml; charset=utf-8");
//        postMethod.setRequestHeader("SOAPAction", soapAction);
//
//        postMethod.setRequestBody(body);
//
//        try {
//            int statusCode = httpClient.executeMethod(postMethod);
//            if (statusCode == HttpStatus.SC_OK) {
//                String s = postMethod.getResponseBodyAsString();
//                return s;
//            } else {
//                throw new IOException("HttpClient response status is: "
//                        + statusCode);
//            }
//        } finally {
//            postMethod.releaseConnection();
//            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
//        }
//    }

    /**
     * @param ContentType 字符串格式 比如：text/xml 或 application/json
     * @param url         地址
     * @param soapAction  可以为空""
     * @param body        字符串
     * @throws IOException
     */
    @SuppressWarnings("deprecation")
    public static String sendPostRequest(String ContentType, String url,
                                         String soapAction, String body) throws IOException {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(1500);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(5000);
        PostMethod postMethod = new GBKPostMethod(url);

        postMethod.setRequestHeader("Content-Type", ContentType);
        postMethod.setRequestHeader("SOAPAction", soapAction);

        postMethod.setRequestBody(body);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static String sendPostRequest(String url, NameValuePair[] params)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(4000);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(10000);

        PostMethod postMethod = new GBKPostMethod(url);
        // postMethod.setRequestHeader("Content-Type",
        // "text/xml; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuffer = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuffer.append(str);
                }
                return stringBuffer.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static String sendPostRequestSmb(String url, NameValuePair[] params)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(1500);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(5000);

        PostMethod postMethod = new GBKPostMethod(url);
        // postMethod.setRequestHeader("Content-Type",
        // "text/xml; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static String sendPostRequest(String url, NameValuePair[] params,
                                         int connectionTimeout, int soTimeout)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(connectionTimeout);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setSoTimeout(soTimeout);

        PostMethod postMethod = new GBKPostMethod(url);
        // postMethod.setRequestHeader("Content-Type",
        // "text/xml; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static String sendPostRequest(String url, Map<String, String> params,
                                         int connectionTimeout, int soTimeout)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(connectionTimeout);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setSoTimeout(soTimeout);

        PostMethod postMethod = new GBKPostMethod(url);
//         postMethod.setRequestHeader("Content-Type",
//         "text/xml; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(getNameValuePairArr(params));

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static NameValuePair[] getNameValuePairArr(
            Map<String, String> parasMap) {
        NameValuePair[] nvps = new NameValuePair[parasMap.size()];
        int i = 0;
        for (Map.Entry<String, String> parasEntry : parasMap.entrySet()) {
            String parasName = parasEntry.getKey();
            String parasValue = parasEntry.getValue();
            nvps[i] = new NameValuePair(parasName, parasValue);
            i++;
        }
        return nvps;
    }

    public static String sendPostRequestQlzx(String url, NameValuePair[] params) throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(1500);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(5000);

        PostMethod postMethod = new GBKPostMethod(url);
        // postMethod.setRequestHeader("Content-Type",
        // "text/xml; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestHeader("Content-uid", "huiyue");

        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }
//
//    public static String sendCinicPostRequest(String url, NameValuePair[] params)
//            throws Exception {
//        HttpClient httpClient = new HttpClient();
//        // 连接超时，毫秒
//        httpClient.getHttpConnectionManager().getParams()
//                .setConnectionTimeout(1500);
//        // 读取数据超时，毫秒
//        httpClient.getHttpConnectionManager().getParams().setSoTimeout(2300);
//
//        PostMethod postMethod = new Utf8PostMethod(url);
//        // postMethod.setRequestHeader("Content-Type",
//        // "text/xml; charset=utf-8");
//        postMethod.setRequestHeader("Connection", "close");
//        postMethod.setRequestBody(params);
//
//        try {
//            int statusCode = httpClient.executeMethod(postMethod);
//            if (statusCode == HttpStatus.SC_OK) {
//                BufferedReader reader = new BufferedReader(
//                        new InputStreamReader(
//                                postMethod.getResponseBodyAsStream()));
//                StringBuffer stringBuffer = new StringBuffer();
//                String str = "";
//                while ((str = reader.readLine()) != null) {
//                    stringBuffer.append(str);
//                }
//                return stringBuffer.toString();
//            } else {
//                throw new IOException("HttpClient response status is: "
//                        + statusCode);
//            }
//        } finally {
//            postMethod.releaseConnection();
//            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
//        }
//    }

    public static String sendHttpsPostRequest(String url, NameValuePair[] params)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(1500);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(5000);
        Protocol myhttps = new Protocol("https",
                new MySSLProtocolSocketFactory(), 443);
        Protocol.registerProtocol("https", myhttps);

        PostMethod postMethod = new GBKPostMethod(url);
        postMethod.setRequestHeader("Content-Type",
                "application/x-www-form-urlencoded; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }
    }

    public static String sendHttpsPostRequest(String url, String params)
            throws Exception {
        HttpClient httpClient = new HttpClient();
        // 连接超时，毫秒
        httpClient.getHttpConnectionManager().getParams()
                .setConnectionTimeout(1500);
        // 读取数据超时，毫秒
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(5000);
        Protocol myhttps = new Protocol("https",
                new MySSLProtocolSocketFactory(), 443);
        Protocol.registerProtocol("https", myhttps);

        PostMethod postMethod = new GBKPostMethod(url);
        postMethod.setRequestHeader("Content-Type",
                "application/x-www-form-urlencoded; charset=utf-8");
        postMethod.setRequestHeader("Connection", "close");
        postMethod.setRequestBody(params);

        try {
            int statusCode = httpClient.executeMethod(postMethod);
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(
                        new InputStreamReader(
                                postMethod.getResponseBodyAsStream()));
                StringBuilder stringBuilder = new StringBuilder();
                String str;
                while ((str = reader.readLine()) != null) {
                    stringBuilder.append(str);
                }
                return stringBuilder.toString();
            } else {
                throw new IOException("HttpClient response status is: "
                        + statusCode);
            }
        } finally {
            postMethod.releaseConnection();
            httpClient.getHttpConnectionManager().closeIdleConnections(1000);
        }

    }
}



/**
 * 重写PostMethod以解决UTF-8编码问题
 */
class GBKPostMethod extends PostMethod {
    public GBKPostMethod(String url) {
        super(url);
    }

    @Override
    public String getRequestCharSet() {
        return "GBK";
    }
}

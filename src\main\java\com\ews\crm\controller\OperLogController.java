
package com.ews.crm.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;



@RestController
@RequestMapping("/admin/operLog")
public class OperLogController {
	@Autowired
	private OperLogService operLogService;


	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private TradeAccountService tradeAccountService;
	
	@Autowired
	private FundInfoService fundInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserService userService;

   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	OperLog query  = new OperLog();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("operName"))) {
               	query.setOperName(request.getParameter("operName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("busId"))) {
               	query.setBusId(Long.parseLong(request.getParameter("busId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operType"))) {
               	query.setOperType(Integer.parseInt(request.getParameter("operType").trim()));
        	}
        	
        	if (!StringUtils.isEmpty(request.getParameter("busType"))) {
               	query.setBusType(Integer.parseInt(request.getParameter("busType").trim()));
        	}
        	Page<OperLog> pages = operLogService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<OperLog> operLogs = pages.getContent();
        	for(int i=0;i<operLogs.size();i++) {
        	     OperLog entity  = operLogs.get(i);
        	     
        	     if(entity.getBusType().intValue()==1) {//代理用户
        	    	 User user=(User)this.userService.findUserById(entity.getBusId());
        	    	 if(user!=null) {
        	    	 entity.setBackup1("userName:"+user.getUsername()+"  name:"+user.getNickName());
        	    	 }
        	     }else if(entity.getBusType().intValue()==2) {//CRM用户
        	    	 UserInfo ui=(UserInfo)this.userInfoService.findById(entity.getBusId());
        	    	 if(ui!=null) {
        	    	 entity.setBackup1("CRM:"+ui.getUserName()+"  "+ui.getFullname());
        	    	 }
        	     }else if(entity.getBusType().intValue()==3) {//入金
        	    	 FundInfo fi=(FundInfo)this.fundInfoService.findById(entity.getBusId());
        	    	 if(fi!=null) {
                     entity.setBackup1("OrderNo:"+fi.getOrderId()+" LoginID:"+fi.getTradeId()+" DepositAmount："+fi.getAmount());  
        	    	 }
        	     }else if(entity.getBusType().intValue()==4) {//出金
        	    	 FundInfo fi=(FundInfo)this.fundInfoService.findById(entity.getBusId());
        	    	 if(fi!=null) {
                     entity.setBackup1("OrderNo:"+fi.getOrderId()+" LoginID:"+fi.getTradeId()+" WithdrawAmount："+fi.getAmount()); 
        	    	 }
        	     }else if(entity.getBusType().intValue()==5) {//同名账号
        	    	 TradeAccount ta=(TradeAccount)this.tradeAccountService.findById(entity.getBusId());
        	    	 if(ta!=null) {
        	    	 entity.setBackup1("LoginID:"+ta.getTradeId()+" "+ta.getUserAccount()); 
        	    	 }
        	     }else if(entity.getBusType().intValue()==6) {//同名转账
        	    	 TransferInfo tfi=(TransferInfo)this.transferInfoService.findById(entity.getBusId());
        	    	 if(tfi!=null) {
        	    	 entity.setBackup1("TransOut:"+tfi.getTransferOut()+" TransIn:"+tfi.getTransferIn()+" OrderNo:"+tfi.getOutOrderId()+"/"+tfi.getInOrderId()+" 转账金额:"+tfi.getTransferAmount()); 
        	    	 }
        	     }else if(entity.getBusType().intValue()==7) {//交易用户
        	    	 TradeAccount ta=(TradeAccount)this.tradeAccountService.findById(entity.getBusId());
        	    	 if(ta!=null) {
        	    	 entity.setBackup1("LoginID:"+ta.getTradeId()+" "+ta.getUserAccount()); 
        	    	 }
        	     }
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", operLogs);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			OperLog operLog = new OperLog();
        	operLog.setOperName(data.getString("operName"));
        	operLog.setBusId(data.getLong("busId"));
        	operLog.setBusType(data.getInteger("busType"));
        	operLog.setOperType(data.getInteger("operType"));
			Result re = operLogService.saveOrUpdate(operLog);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(operLog);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			OperLog operLog = this.operLogService.findById(id);
				if (operLog != null) {
					operLog.setOperName(data.getString("operName"));
					operLog.setBusId(data.getLong("busId"));
					operLog.setBusType(data.getInteger("busType"));
					operLog.setOperType(data.getInteger("operType"));
					Result re = operLogService.saveOrUpdate(operLog);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = operLogService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = operLogService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}


}


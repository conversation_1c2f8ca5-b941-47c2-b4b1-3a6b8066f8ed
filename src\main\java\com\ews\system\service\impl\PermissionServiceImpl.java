package com.ews.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ews.system.entity.Permission;
import com.ews.system.entity.RolePermission;
import com.ews.system.repository.PermissionRepository;
import com.ews.system.service.PermissionService;
import com.ews.system.service.RolePermissionService;


@Service
public class PermissionServiceImpl implements PermissionService 
{
	@Autowired
	private PermissionRepository permissionRepository;
	
	@Autowired
	private RolePermissionService rolePermissionService;
	
	@Autowired
	private PermissionService permissionService ;

	@Override
	public List<Map> getAllPermissions(Long roleId) {
		
		List<Permission> ps = permissionRepository.findAll();
		List<Map> pss = new ArrayList();
		
		Map pm = new HashMap();
		List<Permission> psResult = new ArrayList();
		for(int i =0;i<ps.size();i++) {
			Permission p  = ps.get(i);
			
			if(roleId!=null) {
				List list =rolePermissionService.findByRoleIdAndPermissionId(roleId, p.getPermissionId());
				if(list!=null && list.size()>0) {
					p.setIsChecked(1);
				}else {
					p.setIsChecked(0);
				}
			}else {
				p.setIsChecked(0);
			}
			
			if(pm.containsKey("permissionGroup")) {
				if(pm.get("permissionGroup").equals(p.getPermissionGroup())) {//組相同
					psResult.add(p);
					if(i==ps.size()-1) {
						pm.put("permissions", psResult);
						pss.add(pm);
					}
				}else {
					//清除舊數據
					pm.put("permissions", psResult);
					pss.add(pm);
					pm = new HashMap();
					psResult = new ArrayList();
					//同時添加新數據
					pm.put("permissionGroup", p.getPermissionGroup());
					psResult.add(p);
					if(i==ps.size()-1) {
						pm.put("permissions", psResult);
						pss.add(pm);
					}
				}
			}else {
				pm.put("permissionGroup", p.getPermissionGroup());
				psResult.add(p);
			}
		}
		
		
		return pss;
	}
	
	
	@Override
	public Permission findByPermissinId(Long permissionId) {
		// TODO Auto-generated method stub
		Optional<Permission>  op = this.permissionRepository.findById(permissionId);
		if(op!=null && op.get()!=null) {
			return op.get();
		}
		return null;
	}

	
	@Override
	public List<Permission> getPermissions(Long roleId) {
		List<RolePermission> rps =  rolePermissionService.findByRoleId(roleId);
		List<Long> ids = new ArrayList();
		for(int i =0;i<rps.size();i++) {
			ids.add(rps.get(i).getPermissionId());
		}
		return this.permissionRepository.findAllById(ids);
	}


	@Override
	public List<Permission> findAll() {
		return this.permissionRepository.findAll();
	}
	
	
	
}

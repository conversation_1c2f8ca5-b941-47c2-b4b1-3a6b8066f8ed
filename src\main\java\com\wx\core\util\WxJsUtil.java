package com.wx.core.util;





import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wx.Config;
import com.wx.core.pojo.Token;




/**
 *
 * Created by mazhuang on 2017/3/14.
 */
public class WxJsUtil {
    public static String app_id = Config.APPID1;
    public static String app_secret = Config.APPSECRET1;

    public static Token token = null;
    public static String time = null;
    public static String jsapi_ticket = null;
     // 上传文件存储目录
    private static String UPLOAD_DIRECTORY = "weixin_media";
    /**
     *  获取微信jssdk调用所需参数
     * @param url    当前网页的URL，不包含#及其后面部分
     * @return
     */
    public static String getParam(String url){
    	System.out.println(token);
    	
    	
        if(token == null){
            System.out.println();
            token = CommonUtil.getToken(app_id, app_secret);
            jsapi_ticket = CommonUtil.getJsApiTicket(token.getAccessToken());
            time = getTime();
        }else{
            
                token = null;
                token = CommonUtil.getToken(app_id, app_secret);
                jsapi_ticket = CommonUtil.getJsApiTicket(token.getAccessToken());
                time = getTime();
            
        }
        if(jsapi_ticket == null) {
            jsapi_ticket = CommonUtil.getJsApiTicket(token.getAccessToken());
        }


        Map<String, String> params = sign(jsapi_ticket, url);
        params.put("appid", app_id);
        

        JSONObject jsonObject = new JSONObject();//(JSONObject) JSON.parse(params.toString());
        
       //System.out.println("2224$$$$:"+params.toString());
        jsonObject.put("url", params.get("url").toString());
      //  jsonObject.put("jsapi_ticket", params.get("jsapi_ticket").toString());
        jsonObject.put("jsapi_ticket", params.get("ticket").toString());
        
        jsonObject.put("nonceStr", params.get("nonceStr").toString());
        jsonObject.put("timestamp", params.get("timestamp").toString());
        jsonObject.put("signature", params.get("signature").toString());
        jsonObject.put("appid", Config.APPID1);
       
        String jsonStr = jsonObject.toString();
        System.out.println(jsonStr);
        return jsonStr;
    }

   



   

    public static Map<String, String> sign(String jsapi_ticket, String url) {
        Map<String, String> ret = new HashMap<String, String>();
        String nonce_str = create_nonce_str();
        String timestamp = create_timestamp();
        String str;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        str = "jsapi_ticket=" + jsapi_ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url;

        try
        {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(str.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
        }
        catch (UnsupportedEncodingException e)
        {
            e.printStackTrace();
        }

        ret.put("url", url);
        //ret.put("jsapi_ticket", jsapi_ticket);
        ret.put("ticket", jsapi_ticket);
        ret.put("nonceStr", nonce_str);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);

        return ret;
    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash)
        {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    private static String create_nonce_str() {
        return UUID.randomUUID().toString();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }

    //获取当前系统时间 用来判断access_token是否过期
    public static String getTime(){
        Date dt=new Date();
        SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(dt);
    }
}
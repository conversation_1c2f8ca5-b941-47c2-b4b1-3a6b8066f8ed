package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.PaymentParams;
import com.ews.crm.repository.PaymentParamsRepository;
import com.ews.crm.service.PaymentParamsService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class PaymentParamsServiceImpl implements PaymentParamsService 
{
	@Autowired
	private PaymentParamsRepository paymentParamsRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<PaymentParams> findAll(Integer page, Integer size,String sortName,String sortOrder, PaymentParams paymentParams) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<PaymentParams> pages = paymentParamsRepository.findAll(new Specification<PaymentParams>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<PaymentParams> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(paymentParams.getParamName())) { 
                predicates.add(criteriaBuilder.like(root.get("paramName").as(String.class),"%"+paymentParams.getParamName()+"%"));
             }
             if(!StringUtils.isEmpty(paymentParams.getParamType())) { 
                predicates.add(criteriaBuilder.equal(root.get("paramType").as(Integer.class), paymentParams.getParamType()));
             }
             if(!StringUtils.isEmpty(paymentParams.getIsReturn())) { 
                predicates.add(criteriaBuilder.equal(root.get("isReturn").as(Integer.class), paymentParams.getIsReturn()));
             }
             if(!StringUtils.isEmpty(paymentParams.getIsReback())) { 
                predicates.add(criteriaBuilder.equal(root.get("isReback").as(Integer.class), paymentParams.getIsReback()));
             }
             
             if(!StringUtils.isEmpty(paymentParams.getBackup1())) { 
                 predicates.add(criteriaBuilder.equal(root.get("backup1").as(String.class), paymentParams.getBackup1()));
              }
             if(!StringUtils.isEmpty(paymentParams.getPaymentId())) { 
                predicates.add(criteriaBuilder.equal(root.get("paymentId").as(Long.class), paymentParams.getPaymentId()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public PaymentParams findById(Long id) {
      if(id == null) {
      	return null;
      }
      Optional<PaymentParams> op = paymentParamsRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.paymentParamsRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         PaymentParams old = paymentParamsRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 paymentParamsRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 paymentParamsRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.paymentParamsRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(PaymentParams paymentParams) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (paymentParams.getId()== null) {
            	paymentParams.setGmtCreate(new Date());
            	paymentParams.setGmtModified(new Date());
            	paymentParams.setIsDeleted(0);
            	if(paymentParams.getIsAvailable() == null) {
            		paymentParams.setIsAvailable(1);
            	}
            	paymentParams.setUserCreate(loginUser.getUserId());
	    	} else {
            	paymentParams.setGmtModified(new Date());
        	}
            paymentParamsRepository.save(paymentParams);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



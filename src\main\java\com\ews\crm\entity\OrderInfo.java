package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "order_info")
public class OrderInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *订单号
    **/
	@Column(name = "order_no")
	protected  String orderNo;

    /**
    *开仓时间
    **/
	@Column(name = "open_time")
	protected  Integer openTime;

    /**
    *开仓价格
    **/
	@Column(name = "open_price")
	protected  Double openPrice;

    /**
    *类型：0 buy  1 sell 6 出入金  7 信用
    **/
	@Column(name = "type")
	protected  Integer type;

    /**
    *手数 显示的话需要除以100
    **/
	@Column(name = "volume")
	protected  Double volume;

    /**
    *交易品种
    **/
	@Column(name = "symbol")
	protected  String symbol;

    /**
    *平仓时间
    **/
	@Column(name = "close_time")
	protected  Integer closeTime;

    /**
    *平仓价格
    **/
	@Column(name = "close_price")
	protected  Double closePrice;

    /**
    *库存费
    **/
	@Column(name = "storage")
	protected  Double storage;

    /**
    *获利
    **/
	@Column(name = "profit")
	protected  Double profit;

    /**
    *状态 1：持仓   2：历史
    **/
	@Column(name = "status")
	protected  Integer status;

    /**
    *是否返佣
    **/
	@Column(name = "is_rakeback")
	protected  Integer isRakeback;

    /**
    *返佣金额
    **/
	@Column(name = "rakeback_amount")
	protected  Double rakebackAmount;

    /**
    *交易账号ID
    **/
	@Column(name = "trade_id")
	protected  Long tradeId;

    /**
    *交易账号
    **/
	@Column(name = "login_id")
	protected  String loginId;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  Integer backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  Integer backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  Long backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  Long backup4;

    /**
    *backup5
    **/
	@Column(name = "backup5")
	protected  Double backup5;

    /**
    *backup6
    **/
	@Column(name = "backup6")
	protected  Double backup6;

    /**
    *backup7
    **/
	@Column(name = "backup7")
	protected  String backup7;

    /**
    *backup8
    **/
	@Column(name = "backup8")
	protected  String backup8;

	@Transient
	protected  Integer sortNum;
	
	
	@Transient
	protected  List tradeList;
	
	@Transient
	protected  List typeList;
	
	
	@Transient
	protected Integer queryBeginTime;
	
	
	@Transient
	protected Integer queryEndTime;
	
    @Transient
	protected  List tradeLoginIdList;



    public List getTypeList() {
		return typeList;
	}
	public void setTypeList(List typeList) {
		this.typeList = typeList;
	}
	public List getTradeList() {
		return tradeList;
	}
	public void setTradeList(List tradeList) {
		this.tradeList = tradeList;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getOrderNo()
    {
        return orderNo;
    }
    public void setOrderNo(String  orderNo)
    {
        this.orderNo = orderNo;
    }
    public Integer  getOpenTime()
    {
        return openTime;
    }
    public void setOpenTime(Integer  openTime)
    {
        this.openTime = openTime;
    }
    public Double  getOpenPrice()
    {
        return openPrice;
    }
    public void setOpenPrice(Double  openPrice)
    {
        this.openPrice = openPrice;
    }
    public Integer  getType()
    {
        return type;
    }
    public void setType(Integer  type)
    {
        this.type = type;
    }
    public Double  getVolume()
    {
        return volume;
    }
    public void setVolume(Double  volume)
    {
        this.volume = volume;
    }
    public String  getSymbol()
    {
        return symbol;
    }
    public void setSymbol(String  symbol)
    {
        this.symbol = symbol;
    }
    public Integer  getCloseTime()
    {
        return closeTime;
    }
    public void setCloseTime(Integer  closeTime)
    {
        this.closeTime = closeTime;
    }
    public Double  getClosePrice()
    {
        return closePrice;
    }
    public void setClosePrice(Double  closePrice)
    {
        this.closePrice = closePrice;
    }
    public Double  getStorage()
    {
        return storage;
    }
    public void setStorage(Double  storage)
    {
        this.storage = storage;
    }
    public Double  getProfit()
    {
        return profit;
    }
    public void setProfit(Double  profit)
    {
        this.profit = profit;
    }
    public Integer  getStatus()
    {
        return status;
    }
    public void setStatus(Integer  status)
    {
        this.status = status;
    }
    public Integer  getIsRakeback()
    {
        return isRakeback;
    }
    public void setIsRakeback(Integer  isRakeback)
    {
        this.isRakeback = isRakeback;
    }
    public Double  getRakebackAmount()
    {
        return rakebackAmount;
    }
    public void setRakebackAmount(Double  rakebackAmount)
    {
        this.rakebackAmount = rakebackAmount;
    }
    public Long  getTradeId()
    {
        return tradeId;
    }
    public void setTradeId(Long  tradeId)
    {
        this.tradeId = tradeId;
    }
    public String  getLoginId()
    {
        return loginId;
    }
    public void setLoginId(String  loginId)
    {
        this.loginId = loginId;
    }
    public Integer  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(Integer  backup1)
    {
        this.backup1 = backup1;
    }
    public Integer  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(Integer  backup2)
    {
        this.backup2 = backup2;
    }
    public Long  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(Long  backup3)
    {
        this.backup3 = backup3;
    }
    public Long  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(Long  backup4)
    {
        this.backup4 = backup4;
    }
    public Double  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(Double  backup5)
    {
        this.backup5 = backup5;
    }
    public Double  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(Double  backup6)
    {
        this.backup6 = backup6;
    }
    public String  getBackup7()
    {
        return backup7;
    }
    public void setBackup7(String  backup7)
    {
        this.backup7 = backup7;
    }
    public String  getBackup8()
    {
        return backup8;
    }
    public void setBackup8(String  backup8)
    {
        this.backup8 = backup8;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public Integer getQueryBeginTime() {
		return queryBeginTime;
	}
	public void setQueryBeginTime(Integer queryBeginTime) {
		this.queryBeginTime = queryBeginTime;
	}
	public Integer getQueryEndTime() {
		return queryEndTime;
	}
	public void setQueryEndTime(Integer queryEndTime) {
		this.queryEndTime = queryEndTime;
	}
    
    public List getTradeLoginIdList() {
		return tradeLoginIdList;
	}
	public void setTradeLoginIdList(List tradeLoginIdList) {
		this.tradeLoginIdList = tradeLoginIdList;
	}
    

}

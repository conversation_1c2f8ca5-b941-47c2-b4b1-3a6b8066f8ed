package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "payment_params")
public class PaymentParams implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *参数名
    **/
	@Column(name = "param_name")
	protected  String paramName;

    /**
    *参数类型
    **/
	@Column(name = "param_type")
	protected  Integer paramType;

    /**
    *提交参数
    **/
	@Column(name = "is_submit")
	protected  Integer isSubmit;

    /**
    *返回参数
    **/
	@Column(name = "is_return")
	protected  Integer isReturn;

    /**
    *回调参数
    **/
	@Column(name = "is_reback")
	protected  Integer isReback;

    /**
    *参数值
    **/
	@Column(name = "param_value")
	protected  String paramValue;

    /**
    *排序
    **/
	@Column(name = "sort")
	protected  Integer sort;

    /**
    *backup1   --是否参与加密
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *支付ID
    **/
	@Column(name = "payment_id")
	protected  Long paymentId;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getParamName()
    {
        return paramName;
    }
    public void setParamName(String  paramName)
    {
        this.paramName = paramName;
    }
    public Integer  getParamType()
    {
        return paramType;
    }
    public void setParamType(Integer  paramType)
    {
        this.paramType = paramType;
    }
    public Integer  getIsSubmit()
    {
        return isSubmit;
    }
    public void setIsSubmit(Integer  isSubmit)
    {
        this.isSubmit = isSubmit;
    }
    public Integer  getIsReturn()
    {
        return isReturn;
    }
    public void setIsReturn(Integer  isReturn)
    {
        this.isReturn = isReturn;
    }
    public Integer  getIsReback()
    {
        return isReback;
    }
    public void setIsReback(Integer  isReback)
    {
        this.isReback = isReback;
    }
    public String  getParamValue()
    {
        return paramValue;
    }
    public void setParamValue(String  paramValue)
    {
        this.paramValue = paramValue;
    }
    public Integer  getSort()
    {
        return sort;
    }
    public void setSort(Integer  sort)
    {
        this.sort = sort;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Long  getPaymentId()
    {
        return paymentId;
    }
    public void setPaymentId(Long  paymentId)
    {
        this.paymentId = paymentId;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

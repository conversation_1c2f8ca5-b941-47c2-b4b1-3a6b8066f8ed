
package com.ews.crm.controller;

import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.common.SendCloudAPIV2;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.AccountType;
import com.ews.crm.entity.EmailInfo;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserBank;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.entity.UserTag;
import com.ews.crm.service.AccountTypeService;
import com.ews.crm.service.CompanyInfoService;
import com.ews.crm.service.EmailInfoService;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserBankService;
import com.ews.crm.service.UserInfoService;
import com.ews.crm.service.UserTagService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@RestController
@RequestMapping("/admin/userInfo")
public class UserInfoController {
	@Autowired
	private UserInfoService userInfoService;


	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private AccountTypeService accountTypeService;
	
	@Autowired
	private TradeAccountService tradeAccountService;
	
	@Autowired
	private EmailInfoService emailInfoService;
	
	@Autowired
	private CompanyInfoService companyInfoService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private LoginService loginService;

	@Autowired
	private OperLogService operLogService;
	
	
	@Autowired
	private ConstantConfig constantConfig;
	
	@Autowired
	private UserBankService userBankService;
	
	@Autowired
	private UserTagService userTagService;

	@Autowired
	private FundInfoService fundInfoService;

   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	UserInfo query  = new UserInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
	    	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
        	}
	    	
        	if (!StringUtils.isEmpty(request.getParameter("fullname"))) {
               	query.setFullname(request.getParameter("fullname").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("email"))) {
               	query.setEmail(request.getParameter("email").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tel"))) {
               	query.setTel(request.getParameter("tel").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("invitationCode"))) {
               	query.setInvitationCode(request.getParameter("invitationCode").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userType"))) {
               	query.setUserType(Long.parseLong(request.getParameter("userType").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("parentId"))) {
               	query.setParentId(Long.parseLong(request.getParameter("parentId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("pinyin"))) {
               	query.setPinyin(request.getParameter("pinyin").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("isAgent"))) {
               	query.setIsAgent(Integer.parseInt(request.getParameter("isAgent").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("backup5"))) {
               	query.setBackup5("\""+request.getParameter("backup5").trim()+"\"");
        	}
        	
        	
        	if (!StringUtils.isEmpty(request.getParameter("parent_ID"))) {
        		 User loginUser = this.userService.findByUserName(request.getParameter("parent_ID"));
        		 query.setParentId(loginUser.getUserId()) ;
        	}
        	
        	
        	
        	
        //	query.setIsAvailable(1);
        	
        	 List userList=new ArrayList();
        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
             if(loginUser!=null) {
             	
            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
            	 
            	 if(uu!=null&&uu.getUserId()!=null) {
            		 
            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
            			
            			 //1.查出自己及名下所有的代理
            			 User user_query=new User();
            			 user_query.setSortStr(uu.getSortStr());
            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
            			
            			 userList.add(uu.getUserId());
            			 for(int n=0;n<ul.getContent().size();n++) {
            				 User user_1=ul.getContent().get(n);
            				 userList.add(user_1.getUserId());
            			 }
            			 query.setUserInfoList(userList);
            		 }
            	 }
             }else {
            	 
            	 userList.add(-9999999999L);
            	 query.setUserInfoList(userList);
             }
             
             
        	Page<UserInfo> pages = userInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<UserInfo> userInfos = pages.getContent();
        	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); 
        	for(int i=0;i<userInfos.size();i++) {
        		UserInfo entity  = userInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		  
        		 if(entity.getParentId()!=null) {
        			 User usr=(User)this.userService.findUserById(entity.getParentId());
        			 if(usr!=null) {
        			 entity.setBackup2(usr.getUsername()+"("+usr.getNickName()+")");
        			 }
        		 }
        		 
        		 
        		 //用户资金渠道
        		 UserBank ub_query=new UserBank();
    			 ub_query.setUserId(entity.getId());
    			 Page<UserBank> ub_page=this.userBankService.findAll(0,1,"id","desc", ub_query);
    			 
    			 if(ub_page.getContent().size()>0) {
    				 UserBank ub=(UserBank)ub_page.getContent().get(0);
    				 entity.setBackup4(ub.getBankName()+" "+ub.getBankAccount()+" "+ub.getAccountUsername());
    			 }
    			 
    			 
    			 
    			 if(entity.getBackup5()!=null) {
    				 JSONArray ja=JSONArray.parseArray(entity.getBackup5().toString());  
    				 for(int k=0;k<ja.size();k++) {
    					 UserTag ut=this.userTagService.findById(new Long(ja.getLongValue(k)));
    					 entity.setKycInfo(entity.getKycInfo()+" "+ut.getTagName());
    				 }
    			 }
    			 
    			 
    			 if(entity.getBirthday()!=null) {
    				 
    				 entity.setRemark(sdf.format(entity.getBirthday()));
    			 }
    			 
    			 
    			 
    			
        		 
        	}
        	
        	datas.put("items", userInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list2")
    public ResponseData list2(HttpServletRequest request) {
    	try {
        	UserInfo query  = new UserInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "asc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
	    	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("fullname"))) {
               	query.setFullname(request.getParameter("fullname").trim());
        	}
        	
        	if (!StringUtils.isEmpty(request.getParameter("tel"))) {
               	query.setTel(request.getParameter("tel").trim());
        	}
        	query.setIsAvailable(3);
        	Page<UserInfo> pages = userInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<UserInfo> userInfos = pages.getContent();
        	for(int i=0;i<userInfos.size();i++) {
        		UserInfo entity  = userInfos.get(i);
        		if(entity.getUserType()!=null) {
        		 AccountType aty=(AccountType)this.accountTypeService.findById(new Long(entity.getUserType()));
         		entity.setBackup3(aty.getTypeName());
        		}
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", userInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			
			UserInfo query_userInfo=new UserInfo();
			query_userInfo.setUserName(data.getString("userName"));
			Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);

			if (userInfo_page.getContent().size()>0) {
				return ResponseDataUtil.buildError("CRM用户已经存在");
			} else {
				
				UserInfo userInfo = new UserInfo();
	        	userInfo.setSurname(data.getString("surname"));
	        	userInfo.setName(data.getString("name"));
	        	userInfo.setFullname(data.getString("surname")+data.getString("name"));
	        	userInfo.setUserName(data.getString("userName"));
	        	userInfo.setPassword(data.getString("password"));
	        	userInfo.setTel(data.getString("tel"));
	        	userInfo.setCountry(data.getString("country"));
	        	userInfo.setProvince(data.getString("province"));
	        	userInfo.setCity(data.getString("city"));
	        	userInfo.setGender(data.getInteger("gender"));
	        	userInfo.setAdress(data.getString("adress"));
	        	userInfo.setIdentityNum(data.getString("identityNum"));
	        	userInfo.setImageFront(data.getString("imageFront"));
	        	userInfo.setImageBack(data.getString("imageBack"));
	        	userInfo.setInvitationCode(data.getString("invitationCode"));
				userInfo.setBackup1("zh-cn");
				userInfo.setIsAgent(1);
	        	
	        	if(userInfo.getInvitationCode()!=null&&!userInfo.getInvitationCode().equals("")) {
		    		User user_query=new User();
		    		user_query.setSaleSerial(userInfo.getInvitationCode());
		    		Page<User> user_page=this.userService.findAll(0,1,"userId","asc", user_query);
		    		if(user_page.getContent().size()>0) {
		    			userInfo.setParentId(user_page.getContent().get(0).getUserId());
		    		}
		    		
		    	}
	        	
	        	
	        	if(data.get("rekebackRule")!=null) {
	        	Object[] o1 = data.getJSONArray("rekebackRule").toArray();
				userInfo.setBackup5(data.getJSONArray("rekebackRule").toString());
	        	}
	        	
	        	
				Result re = userInfoService.saveOrUpdate(userInfo);
				
				//操作日志 -begin
				User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
				OperLog olog=new OperLog();
				olog.setBusId(userInfo.getId());
				olog.setBusType(2);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
				olog.setOperId(loginUser_log.getUserId());
				olog.setOperName(loginUser_log.getUsername());
				olog.setOperType(1);  //1 新增   2 修改  3 删除  4 审批
				this.operLogService.saveOrUpdate(olog);
				//操作日志 -end
				if(re.getCode().equals(Result.CODEFAIL)){
					return ResponseDataUtil.buildError("save fail");
				}
				return ResponseDataUtil.buildSuccess(userInfo);
			}
			
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			UserInfo userInfo = this.userInfoService.findById(id);
				if (userInfo != null) {
					       	
		        	userInfo.setTel(data.getString("tel"));
		        	userInfo.setProvince(data.getString("province"));
		        	userInfo.setCity(data.getString("city"));
		        	userInfo.setGender(data.getInteger("gender"));
		        	userInfo.setCountry(data.getString("country"));
		        	userInfo.setAdress(data.getString("adress"));
		        	userInfo.setIdentityNum(data.getString("identityNum"));
		        	userInfo.setImageFront(data.getString("imageFront"));
		        	userInfo.setImageBack(data.getString("imageBack"));
		        	
		        	if(data.get("rekebackRule")!=null) {
			        	Object[] o1 = data.getJSONArray("rekebackRule").toArray();
						userInfo.setBackup5(data.getJSONArray("rekebackRule").toString());
			        	}
		        	userInfo.setName(data.getString("name"));
		        	userInfo.setSurname(data.getString("surname"));
		        	userInfo.setFullname(data.getString("name")+data.getString("surname"));
		        	
		        	if(data.getString("birthday")!=null&&!data.getString("birthday").equals("")) {
		        		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		       		    Date date = sdf.parse(data.getString("birthday"));
		        		userInfo.setBirthday(date);
		        	}
		        	userInfo.setIsAvailable(data.getInteger("isAvailable"));
		        	
					Result re = userInfoService.saveOrUpdate(userInfo);
					
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(userInfo.getId());
					olog.setBusType(2);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(2);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				
				UserInfo ui=this.userInfoService.findById(id);
				
				

				    //根据userInfo的id 去 查询tradeaccount的记录 ，并将tradeAccount的userId设置为null,userAccount设置为空字符串
					TradeAccount tradeAccount_query=new TradeAccount();
					tradeAccount_query.setUserId(id);
					Page<TradeAccount> tradeAccount_page=this.tradeAccountService.findAll(0,1000,"id","asc", tradeAccount_query);
					for(int i=0;i<tradeAccount_page.getContent().size();i++) {
						TradeAccount tradeAccount=tradeAccount_page.getContent().get(i);
						tradeAccount.setUserId(null);
						tradeAccount.setUserAccount("");
						this.tradeAccountService.saveOrUpdate(tradeAccount);
					   }
	 
					//根据userInfo 的id 去查询 FundInfo ，将相关记录删除
					FundInfo fundInfo_query=new FundInfo();
					fundInfo_query.setUserId(id);
					Page<FundInfo> fundList_page=this.fundInfoService.findAll(0,1000,"id","asc", fundInfo_query);
					 for(int i=0;i<fundList_page.getContent().size();i++) {
					 FundInfo fundInfo=fundList_page.getContent().get(i);
					   this.fundInfoService.removeEntityById(fundInfo.getId());
					}
	 
				Result result = this.userInfoService.removeEntityById(id);
				
				
				
				if(result.getCode().equals(Result.CODESUCCESS)){
					
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(2);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(3);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					
					return ResponseDataUtil.buildSuccess(); 
					
					
					
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}

 /**
	 * 驳回
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/bohui")
	public ResponseData bohui(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				
				UserInfo ui=this.userInfoService.findById(id);
				//EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
				//SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"开户驳回","您的开户申请被驳回,原因："+request.getParameter("bhyy"),"","",emailInfo.getBackup3(),emailInfo.getFromEmail(),emailInfo.getEmailName(),ui.getUserName(),"","");
				
				ui.setIsAvailable(4);
				Result result = userInfoService.saveOrUpdate(ui);
				
				 EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
	                try {
	                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Rejected","","","",emailInfo.getBackup6(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,ui.getUserName(),ui.getFullname(),"");
	                }catch(Exception e) {
	                	System.out.println(e);
	              }
				
				if(result.getCode().equals(Result.CODESUCCESS)){
					
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(2);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(3);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					
					return ResponseDataUtil.buildSuccess(); 
					
					
					
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}



    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = userInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	 /**
		 * 审核通过
		 * @param data
		 * @param request
		 * @return
		 */
		@RequestMapping(value = "/audit")
		public ResponseData audit(HttpServletRequest request, @RequestBody JSONObject data) {
			try {
		 		if (data.containsKey("id")) {
		 			Long id = data.getLong("id");
		 			UserInfo userInfo = this.userInfoService.findById(id);
		 			 TradeAccount taccount=new TradeAccount();
					if (userInfo != null&&userInfo.getIsAvailable().intValue()!=1) {
						userInfo.setAuditTime(new Date());
						userInfo.setIsAvailable(1);
						Result re = userInfoService.saveOrUpdate(userInfo);
						
						
						   EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
			                try {
			                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your Profile Has Been Successfully","","","",emailInfo.getBackup5(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,userInfo.getUserName(),userInfo.getFullname(),"");
			                }catch(Exception e) {
			                	System.out.println(e);
			                }
						
						//操作日志 -begin
						User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
						OperLog olog=new OperLog();
						olog.setBusId(userInfo.getId());
						olog.setBusType(2);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
						olog.setOperId(loginUser_log.getUserId());
						olog.setOperName(loginUser_log.getUsername());
						olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
						this.operLogService.saveOrUpdate(olog);
						//操作日志 -end
						return ResponseDataUtil.buildSuccess();
						
					}else{
						return ResponseDataUtil.buildError("update fail");
					}
		 		}else{
					return ResponseDataUtil.buildError("param id error");
		 		}
			} catch (Exception e) {
		 		System.out.println(e);
		 		return ResponseDataUtil.buildError(e.getMessage());
		 	}
		}
		
		@RequestMapping(value = "/resetPassword", method = RequestMethod.POST)
		public ResponseData resetPassword(Long id, String resetPassword) {
			if (id != null && !StringUtils.isEmpty(resetPassword)) {
				try {
					
					if (resetPassword.length() < 6) {
						return ResponseDataUtil.buildError("密码长度不能小于6位");
					}else {
						UserInfo userInfo=this.userInfoService.findById(id);
						userInfo.setPassword(resetPassword);
						this.userInfoService.saveOrUpdate(userInfo);
						return ResponseDataUtil.buildSuccess();
					}
					
					
				} catch (Exception e) {
					e.printStackTrace();
					return ResponseDataUtil.buildError(e.getMessage());
				}
			} else {
				return ResponseDataUtil.buildError("参数错误");
			}
		}
		
		 public String genRandomNum(int cnt,int type){
			  int  maxNum = 36;
			  char[] str = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K',
					    'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
					    'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };	
			  int i;
			  int count = 0;
			  
			  if(type==2)
			  {
			   maxNum = 10;
			   char[] str1 = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };	
			   str=str1;
			  }
			  StringBuffer pwd = new StringBuffer("");
			  Random r = new Random();
			  while(count < cnt){
			   i = Math.abs(r.nextInt(maxNum));   
			   if (i >= 0 && i < str.length) {
			    pwd.append(str[i]);
			    count ++;
			   }
			  }
			  return pwd.toString();
			}
		 
		 
		 @PostMapping("/exportUserInfoExcel")
		    public ResponseData exportUserInfoExcel(HttpServletRequest request) {
		    		JSONObject datas = new JSONObject();
		    	 try {
		    		 String filename=new Date().getTime()+".xls";
		    	     String path=constantConfig.getFileStoreUrl();
				String[] headers = {  "姓名", "邮箱","手机号","性别","注册时间","所属代理","地址","身份证号"};
		        // 声明一个工作薄
		        HSSFWorkbook workbook = new HSSFWorkbook();
		        // 生成一个表格
		        HSSFSheet sheet = workbook.createSheet();
		        // 设置表格默认列宽度为15个字节
		        //sheet.setDefaultColumnWidth((short)3);
		        sheet.setColumnWidth(0,(short)15*256);
		        sheet.setColumnWidth(1, (short)15*256);
		        sheet.setColumnWidth(2,(short)15*256);
		        sheet.setColumnWidth(3, (short)15*256);
		        sheet.setColumnWidth(4, (short)15*556);
		        sheet.setColumnWidth(5, (short)15*256);
		        sheet.setColumnWidth(6, (short)15*656);
		        sheet.setColumnWidth(7, (short)15*256);
		        sheet.setDefaultRowHeight((short)400);
		       
		        HSSFRow row = sheet.createRow(0);
		        for (short i = 0; i < headers.length; i++) {
		            HSSFCell cell = row.createCell(i);
		            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
		            HSSFCellStyle style = workbook.createCellStyle();
		             //设置背景颜色
		             style.setFillForegroundColor((short)10);
		            cell.setCellStyle(style);
		             cell.setCellValue(text);
		        }
				    int index = 0;
					UserInfo query  = new UserInfo();
				    String sortBy = "desc";
			    	String sort = "id";
			    	if (request.getParameter("sort")!= null) {
			    		sort = request.getParameter("sort").trim();
			    		if (sort.startsWith("+")) {
			    			sortBy = "asc";
			    		}
			  			sort = sort.replace("+", "").replace("-", "");
			    	}
			    	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
		               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
		               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
		        	}
			    	
		        	if (!StringUtils.isEmpty(request.getParameter("fullname"))) {
		               	query.setFullname(request.getParameter("fullname").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
		               	query.setUserName(request.getParameter("userName").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("email"))) {
		               	query.setEmail(request.getParameter("email").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("tel"))) {
		               	query.setTel(request.getParameter("tel").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("invitationCode"))) {
		               	query.setInvitationCode(request.getParameter("invitationCode").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("userType"))) {
		               	query.setUserType(Long.parseLong(request.getParameter("userType").trim())) ;
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("parentId"))) {
		               	query.setParentId(Long.parseLong(request.getParameter("parentId").trim())) ;
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("pinyin"))) {
		               	query.setPinyin(request.getParameter("pinyin").trim());
		        	}
		        	if (!StringUtils.isEmpty(request.getParameter("isAgent"))) {
		               	query.setIsAgent(Integer.parseInt(request.getParameter("isAgent").trim()));
		        	}
		        	
		        	if (!StringUtils.isEmpty(request.getParameter("parent_ID"))) {
		        		 User loginUser = this.userService.findByUserName(request.getParameter("parent_ID"));
		        		 query.setParentId(loginUser.getUserId()) ;
		        	}
		        	query.setIsAvailable(1);
		        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
		             if(loginUser!=null) {
		             	
		            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
		            	 
		            	 if(uu!=null&&uu.getUserId()!=null) {
		            		 
		            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
		            			
		            			 //1.查出自己及名下所有的代理
		            			 User user_query=new User();
		            			 user_query.setSortStr(uu.getSortStr());
		            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
		            			 List userList=new ArrayList();
		            			 userList.add(uu.getUserId());
		            			 for(int n=0;n<ul.getContent().size();n++) {
		            				 User user_1=ul.getContent().get(n);
		            				 userList.add(user_1.getUserId());
		            			 }
		            			 query.setUserInfoList(userList);
		            		 }
		            	 }
		             }
		             
		             
		        	Page<UserInfo> pages = userInfoService.findAll(0, 100000, sort, sortBy, query);
		        	List<UserInfo> userInfos = pages.getContent();
		        	for(int i=0;i<userInfos.size();i++) {
		        		UserInfo entity  = userInfos.get(i);
		        		 if(entity.getParentId()!=null) {
		        			 User usr=(User)this.userService.findUserById(entity.getParentId());
		        			 if(usr!=null) {
		        				 entity.setBackup2(usr.getUsername()+"("+usr.getNickName()+")");
		        			 }
		        		 }
		        		 
		        	}
				 for(int k=0;k<userInfos.size();k++) {
					 index++;
					 try {
					   row = sheet.createRow(index);
					   UserInfo entity  = userInfos.get(k);
					   row.createCell(0).setCellValue(new HSSFRichTextString(entity.getFullname()));
					   row.createCell(1).setCellValue(new HSSFRichTextString(entity.getUserName()));
		               row.createCell(2).setCellValue(new HSSFRichTextString(entity.getTel()));
		               if(entity.getGender()!=null&&entity.getGender().intValue()==1) {
		            	   row.createCell(3).setCellValue(new HSSFRichTextString("男"));
		               }else {
		            	   row.createCell(3).setCellValue(new HSSFRichTextString("女"));
		               }
		               row.createCell(4).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
		               row.createCell(5).setCellValue(new HSSFRichTextString(entity.getBackup2()));
		               row.createCell(6).setCellValue(new HSSFRichTextString(entity.getProvince()+entity.getCity()+entity.getAdress()));
		               row.createCell(7).setCellValue(new HSSFRichTextString(entity.getIdentityNum()));
					 } catch (Exception e) {
		                 // TODO Auto-generated catch block
		                 e.printStackTrace();
		             } 
				 }
				 FileOutputStream output=new FileOutputStream(path+filename);  
				 workbook.write(output);//写入磁盘  
		         workbook.close();
		         output.close();
		         datas.put("fileUrl",filename);
		         return ResponseDataUtil.buildSuccess(datas);
		    	 }catch (Exception e) {
		          	e.printStackTrace();
		          	return ResponseDataUtil.buildError(e.getMessage());
		      	}
			}


}


import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/accountType/list',
    method: 'post',
    params: query
  })
}

export function fetchAccountTypeList(query) {
  return request({
    url: '/accountType/list',
    method: 'post',
    params: query
  })
}

export function fetchAccountType(id) {
  return request({
    url: '/accountType/detail',
    method: 'get',
    params: { id }
  })
}

export function listAll(query) {
  return request({
    url: '/accountType/listAll',
    method: 'post',
    params: query
  })
}

export function createAccountType(data) {
  return request({
    url: '/accountType/add',
    method: 'post',
    data
  })
}

export function updateAccountType(data) {
  return request({
    url: '/accountType/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/accountType/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeAccountType(id) {
  return request({
    url: '/accountType/remove',
    method: 'get',
    params: { id }
  })
}

package com.ews.system.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import com.ews.common.Result;
import com.ews.system.entity.Role;
import com.ews.system.model.IRolePermission;


@Component
public interface RoleService
{
	/**
	 * 分页查询方法
	 * @param page
	 * @param size
	 * @param sortName
	 * @param sortOrder
	 * @param role
	 * @return
	 */
	Page<Role> findAll(Integer page, Integer size, String sortName,String sortOrder,final Role role);


	Role findById(Long roleId);
    

    /**
     * 保存和更新方法
     * @param role
     * @return
     */
    Role saveOrUpdate(Role role);


    boolean deleteAllByRoleIdIn(List<Long> roleIdList);

    List<IRolePermission> findRolePermissionByRoleId(Long roleId);

    void grantAuthorization(Long roleId, Long[] permissionList);

    void clearAuthorization(Long roleId);
    
    /**
     * 通过roleid 删除role
     * @param roleId
     * @return
     */
    Result removeRoleByRoleId(Long roleId);
    
    /**
     * 更新角色可用状态
     * @param roleId
     * @param isAvailable
     * @return
     */
    boolean updateIsAvailableById(Long roleId,Integer isAvailable);
    
    /**
     * 查询所有可用的role
     * @return
     */
    List<Role> findAll(Role queryRole);
    
    /**
     * 查询指定用户的所有权限
     * @param userId
     * @return
     */
    List<Role> findAllByUserId(Long userId);
    
    
    /**
     * 查询roleSign是否重复
     * @param roleId
     * @param roleSign
     * @return
     */
    boolean checkExistedByIdAndRoleSign(Long roleId,String roleSign);
    
    /**
     * 查询roleName是否重复
     * @param roleId
     * @param roleName
     * @return
     */
    boolean checkExistedByIdAndRoleName(Long roleId,String roleName);
    
    /**
     * 查询roleName和roleSign是否重复
     * @param roleId
     * @param roleName
     * @param roleSign
     * @return
     */
    Map<String,String> checkExistedByIdAndRoleNameAndRoleSign(Long roleId,String roleName,String roleSign);
    
    /**
     * 保存或更新并更新权限信息
     * @param role
     * @return
     */
    Result saveOrUpdateRoleAndPermission(Role role,String[] permissionIdsStrArr);
}

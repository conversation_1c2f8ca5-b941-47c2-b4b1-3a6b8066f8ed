
package com.ews.crm.controller;

import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.AccountType;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@RestController
@RequestMapping("/admin/reckbackInfo")
public class ReckbackInfoController {
	@Autowired
	private ReckbackInfoService reckbackInfoService;

	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;
	
	@Autowired
	private ConstantConfig constantConfig;

   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	ReckbackInfo query  = new ReckbackInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
	    	
	    	  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			 query.setUserId(uu.getUserId()) ;
	            		 }
	            	 }else {
		            	 query.setUserId(-99999999L) ;
		             }
	             }else {
	            	 query.setUserId(-99999999L) ;
	             }
	             
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmUserId"))) {
               	query.setCrmUserId(Long.parseLong(request.getParameter("crmUserId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmOrderId"))) {
               	query.setCrmOrderId(request.getParameter("crmOrderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmTradeId"))) {
               	query.setCrmTradeId(request.getParameter("crmTradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
               	query.setGroupName(request.getParameter("groupName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("symbol"))) {
               	query.setSymbol(request.getParameter("symbol").trim());
        	}
        	
        	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
        	}
        	
        	if (!StringUtils.isEmpty(request.getParameter("reckbackUserId"))) {
               	query.setReckbackUserId(Long.parseLong(request.getParameter("reckbackUserId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("reckbackOrderId"))) {
               	query.setReckbackOrderId(request.getParameter("reckbackOrderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("reckbackTradeId"))) {
               	query.setReckbackTradeId(request.getParameter("reckbackTradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("reckbackStatus"))) {
               	query.setReckbackStatus(Integer.parseInt(request.getParameter("reckbackStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("reckbackType"))) {
               	query.setReckbackType(Integer.parseInt(request.getParameter("reckbackType").trim()));
        	}
        	Page<ReckbackInfo> pages = reckbackInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	
        	
        	
        	
        	
        	
        	    JSONObject datas = new JSONObject();
        	
        	    if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))||!StringUtils.isEmpty(request.getParameter("gmtCreateSearchEnd"))||!StringUtils.isEmpty(request.getParameter("reckbackTradeId"))||!StringUtils.isEmpty(request.getParameter("reckbackOrderId"))||!StringUtils.isEmpty(request.getParameter("symbol"))||!StringUtils.isEmpty(request.getParameter("groupName"))||!StringUtils.isEmpty(request.getParameter("crmTradeId"))||!StringUtils.isEmpty(request.getParameter("crmOrderId"))||!StringUtils.isEmpty(request.getParameter("crmUserId"))) {
            	Page<ReckbackInfo> pages2 = reckbackInfoService.findAll(0, 1000000, sort, sortBy, query);
            	
            	double fyzj=0d;
				double fyss=0d;
	            	for(int m=0;m<pages2.getContent().size();m++) {
	            		ReckbackInfo riii=(ReckbackInfo)pages2.getContent().get(m);
	            		BigDecimal bg = new BigDecimal(riii.getReckbackAmount());
	            		double f1 = bg.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
	            		fyzj=fyzj+f1;

						BigDecimal bg2=new BigDecimal(riii.getTradeQty());
						double f2=bg2.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
						fyss=fyss+f2;
	            	}
	            	DecimalFormat df3=new DecimalFormat("000.0000");
					DecimalFormat df4=new DecimalFormat("000.00");
	            	datas.put("fyzj", df3.format(fyzj));
					datas.put("fyss", df4.format(fyss));
            	}else {
            		
            	}
        	List<ReckbackInfo> reckbackInfos = pages.getContent();
        	for(int i=0;i<reckbackInfos.size();i++) {
        		ReckbackInfo entity  = reckbackInfos.get(i);
        		BigDecimal bg = new BigDecimal(entity.getReckbackAmount());
        		double f1 = bg.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        		entity.setReckbackAmount(f1);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", reckbackInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			ReckbackInfo reckbackInfo = new ReckbackInfo();
        	reckbackInfo.setUserId(data.getLong("userId"));
        	reckbackInfo.setCrmUserId(data.getLong("crmUserId"));
        	reckbackInfo.setCrmOrderId(data.getString("crmOrderId"));
        	reckbackInfo.setCrmTradeId(data.getString("crmTradeId"));
        	reckbackInfo.setTradeQty(data.getDouble("tradeQty"));
        	reckbackInfo.setGroupName(data.getString("groupName"));
        	reckbackInfo.setSymbol(data.getString("symbol"));
        	reckbackInfo.setReckbackUserId(data.getLong("reckbackUserId"));
        	reckbackInfo.setReckbackOrderId(data.getString("reckbackOrderId"));
        	reckbackInfo.setReckbackTradeId(data.getString("reckbackTradeId"));
        	reckbackInfo.setReckbackStatus(data.getInteger("reckbackStatus"));
        	reckbackInfo.setReckbackType(data.getInteger("reckbackType"));
        	reckbackInfo.setReckbackAmount(data.getDouble("reckbackAmount"));
			Result re = reckbackInfoService.saveOrUpdate(reckbackInfo);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(reckbackInfo);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			ReckbackInfo reckbackInfo = this.reckbackInfoService.findById(id);
				if (reckbackInfo != null) {
					reckbackInfo.setUserId(data.getLong("userId"));
					reckbackInfo.setCrmUserId(data.getLong("crmUserId"));
					reckbackInfo.setCrmOrderId(data.getString("crmOrderId"));
					reckbackInfo.setCrmTradeId(data.getString("crmTradeId"));
					reckbackInfo.setTradeQty(data.getDouble("tradeQty"));
					reckbackInfo.setGroupName(data.getString("groupName"));
					reckbackInfo.setSymbol(data.getString("symbol"));
					reckbackInfo.setReckbackUserId(data.getLong("reckbackUserId"));
					reckbackInfo.setReckbackOrderId(data.getString("reckbackOrderId"));
					reckbackInfo.setReckbackTradeId(data.getString("reckbackTradeId"));
					reckbackInfo.setReckbackStatus(data.getInteger("reckbackStatus"));
					reckbackInfo.setReckbackType(data.getInteger("reckbackType"));
					reckbackInfo.setReckbackAmount(data.getDouble("reckbackAmount"));
					Result re = reckbackInfoService.saveOrUpdate(reckbackInfo);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = reckbackInfoService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = reckbackInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	
	
	 @PostMapping("/exportReckbackInfoExcel")
	    public ResponseData exportReckbackInfoExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = {  "交易订单号", "交易账号","交易手数","交易品种","返佣订单号","佣金账户","返佣状态","返佣类型","返佣金额","记录时间","实际返佣时间"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*256);
	        sheet.setColumnWidth(3, (short)15*256);
	        sheet.setColumnWidth(4, (short)15*256);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setColumnWidth(7, (short)15*256);
	        sheet.setColumnWidth(8, (short)15*256);
	        sheet.setColumnWidth(9, (short)15*456);
	        sheet.setColumnWidth(10, (short)15*456);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
			  
			    ReckbackInfo query  = new ReckbackInfo();
	        	Integer page = 0;
	        	Integer limit = 10;
	        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
	            	page = Integer.parseInt(request.getParameter("page").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
	            	limit = Integer.parseInt(request.getParameter("limit").trim());
	        	}
		    	String sortBy = "desc";
		    	String sort = "id";
		    	if (request.getParameter("sort")!= null) {
		    		sort = request.getParameter("sort").trim();
		    		if (sort.startsWith("+")) {
		    			sortBy = "asc";
		    		}
		  			sort = sort.replace("+", "").replace("-", "");
		    	}
		    	
		    	  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
		             if(loginUser!=null) {
		             	
		            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
		            	 
		            	 if(uu!=null&&uu.getUserId()!=null) {
		            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
		            			 query.setUserId(uu.getUserId()) ;
		            		 }
		            	 }
		             }
		             
	        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
	               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("crmUserId"))) {
	               	query.setCrmUserId(Long.parseLong(request.getParameter("crmUserId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("crmOrderId"))) {
	               	query.setCrmOrderId(request.getParameter("crmOrderId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("crmTradeId"))) {
	               	query.setCrmTradeId(request.getParameter("crmTradeId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
	               	query.setGroupName(request.getParameter("groupName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("symbol"))) {
	               	query.setSymbol(request.getParameter("symbol").trim());
	        	}
	        	
	        	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
	               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
	               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
	        	}
	        	
	        	if (!StringUtils.isEmpty(request.getParameter("reckbackUserId"))) {
	               	query.setReckbackUserId(Long.parseLong(request.getParameter("reckbackUserId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("reckbackOrderId"))) {
	               	query.setReckbackOrderId(request.getParameter("reckbackOrderId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("reckbackTradeId"))) {
	               	query.setReckbackTradeId(request.getParameter("reckbackTradeId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("reckbackStatus"))) {
	               	query.setReckbackStatus(Integer.parseInt(request.getParameter("reckbackStatus").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("reckbackType"))) {
	               	query.setReckbackType(Integer.parseInt(request.getParameter("reckbackType").trim()));
	        	}
	        	Page<ReckbackInfo> pages = reckbackInfoService.findAll(0, 100000, sort, sortBy, query);
			 for(int k=0;k<pages.getContent().size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   ReckbackInfo entity  = pages.getContent().get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(entity.getCrmOrderId()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(entity.getCrmTradeId()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(entity.getTradeQty().toString()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(entity.getSymbol()));
	               row.createCell(4).setCellValue(new HSSFRichTextString(entity.getReckbackOrderId()));
	               row.createCell(5).setCellValue(new HSSFRichTextString(entity.getReckbackTradeId()));
	               String fyzt="未返佣";
	               if(entity.getReckbackStatus().intValue()==1) {
	            	   fyzt="已返佣";
	               }
	               row.createCell(6).setCellValue(new HSSFRichTextString(fyzt));
	               String fylx="一级返佣";
	               if(entity.getReckbackType().intValue() ==1){
	            	   fylx= "一级返佣";
	                  }else if(entity.getReckbackType().intValue() ==2){
	                	  fylx= "二级返佣";
	                  }else if(entity.getReckbackType().intValue() ==3){
	                	  fylx= "三级返佣";
	                  }else if(entity.getReckbackType().intValue() ==6){
	                	  fylx= "销售返佣";
	                  }else if(entity.getReckbackType().intValue() ==7){
	                	  fylx= "经理返佣";
	                  }else if(entity.getReckbackType().intValue() ==8){
	                	  fylx= "总监返佣";
	                  }
	               row.createCell(7).setCellValue(new HSSFRichTextString(fylx));
	               
	                BigDecimal bg = new BigDecimal(entity.getReckbackAmount());
	        		double f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
	        		entity.setReckbackAmount(f1);
	        		
	               row.createCell(8).setCellValue(new HSSFRichTextString(entity.getReckbackAmount().toString()));
	               row.createCell(9).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
	               row.createCell(10).setCellValue(new HSSFRichTextString(entity.getGmtModified().toString()));
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}


}


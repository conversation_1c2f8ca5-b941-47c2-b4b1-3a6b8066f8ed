package com.ews.common;

import org.springframework.stereotype.Component;

@Component
public class LoginResult {
	private boolean isLogin = false;
	private String result;
	/**
	 * 错误类型 
	 * 0、无错误
	 * 1、用户不存在
	 * 2、密码错误
	 * 3、系统异常
	 * 4、已禁用
	 */
	private Integer errorType;
	
	public boolean isLogin() {
		return isLogin;
	}

	public void setLogin(boolean login) {
		isLogin = login;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public Integer getErrorType() {
		return errorType;
	}

	public void setErrorType(Integer errorType) {
		this.errorType = errorType;
	}
	
}

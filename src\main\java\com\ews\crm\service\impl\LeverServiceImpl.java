package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.Lever;
import com.ews.crm.repository.LeverRepository;
import com.ews.crm.service.LeverService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class LeverServiceImpl implements LeverService 
{
	@Autowired
	private LeverRepository leverRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<Lever> findAll(Integer page, Integer size,String sortName,String sortOrder, Lever lever) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<Lever> pages = leverRepository.findAll(new Specification<Lever>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<Lever> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public Lever findById(Long id) {
      Optional<Lever> op = leverRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.leverRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         Lever old = leverRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 leverRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 leverRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.leverRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(Lever lever) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (lever.getId()== null) {
            	lever.setGmtCreate(new Date());
            	lever.setGmtModified(new Date());
            	lever.setIsDeleted(0);
            	if(lever.getIsAvailable() == null) {
            		lever.setIsAvailable(1);
            	}
            	lever.setUserCreate(loginUser.getUserId());
	    	} else {
            	lever.setGmtModified(new Date());
        	}
            leverRepository.save(lever);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



package com.ews.common;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "constant")
public class FilePathConstant {

	private String fileStoreUrl;

	private String fileStoreUrlPrefix;

	private String codeFileUrl;

	public String getFileStoreUrl() {
		return fileStoreUrl;
	}

	public void setFileStoreUrl(String fileStoreUrl) {
		this.fileStoreUrl = fileStoreUrl;
	}

	public String getFileStoreUrlPrefix() {
		return fileStoreUrlPrefix;
	}

	public void setFileStoreUrlPrefix(String fileStoreUrlPrefix) {
		this.fileStoreUrlPrefix = fileStoreUrlPrefix;
	}

	public String getCodeFileUrl() {
		return codeFileUrl;
	}

	public void setCodeFileUrl(String codeFileUrl) {
		this.codeFileUrl = codeFileUrl;
	}

}

<template>
  <div class="app-container">
    <el-button type="primary" icon="el-icon-plus" @click="handleAddRole">
      {{ $t('permission.addRole') }}
    </el-button>

    <el-table :data="rolesList" style="width: 100%;margin-top:30px;" border>
      <el-table-column align="center" :label="$t('role.roleName')" width="220">
        <template slot-scope="scope">
          {{ scope.row.roleName }}
        </template>
      </el-table-column>

      <!--状态  前加: 标识字段类型为数字 -->
      <el-table-column :label="$t('role.status')" class-name="status-col" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isAvailable"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            @change="handleIsAvailableChange(scope.row)"
          />
        </template>
      </el-table-column>
      <!--
			<el-table-column align="center"  width="120" label="状态">
			  <template slot-scope="scope">

			    <el-tag type="success"  v-if="scope.row.isAvailable ==1">已Enable</el-tag>
				  <el-tag type="danger" v-else>已Disable</el-tag>
				</template>
			</el-table-column>
      -->
      <el-table-column align="header-center" min-width="220px " :label="$t('role.roleDescription')">
        <template slot-scope="scope" align="left">
          {{ scope.row.description }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('role.oper')" width="220px ">
        <template slot-scope="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope)">
            {{ $t('permission.editPermission') }}
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope)">
            {{ $t('permission.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="dialogVisible" :title="dialogType==='edit'?'Edit':'New'" :close-on-click-modal="false">
      <el-form ref="dataForm" :model="role" :rules="rules" label-width="80px" label-position="rigth">
        <el-form-item :label="$t('role.roleName')" prop="roleName">
          <el-input v-model="role.roleName" :placeholder="$t('role.roleName')" />
        </el-form-item>
        <el-form-item :label="$t('role.roleDescription')" prop="roleDescription">
          <el-input
            v-model="role.description"
            :autosize="{ minRows: 2, maxRows: 4}"
            type="textarea"
            :placeholder="$t('role.roleDescription')"
          />
        </el-form-item>
        <el-form-item :label="$t('role.permission')">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('role.allSelect') }}</el-checkbox>
          <div style="margin: 15px 0;" />
          <el-checkbox-group v-for="permission in permissions" :key="permission.group" v-model="checkedPermissions" :label="permission.group" @change="handleCheckedCitiesChange">
            {{ permission.group }}
            <el-checkbox v-for="p in permission.data" :key="p.permissionId" :label="p.permissionId">{{ p.description }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div style="text-align:right;">
        <el-button type="danger" @click="dialogVisible=false">
          {{ $t('permission.cancel') }}
        </el-button>
        <el-button type="primary" @click="confirmRole">
          {{ $t('permission.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import path from 'path'
import { deepClone } from '@/utils'
import { getRoutes, getRoles, addRole, deleteRole, updateRole, disableRole, enableRole, getRoutesByRoleId, updateIsAvailable } from '@/api/role'
import i18n from '@/lang'

const defaultRole = {
  roleId: '',
  roleName: '',
  description: '',
  permissions: []
}
var permissions = []
var count = 0
var checkedPermissions = []
export default {
  data() {
    return {
      role: Object.assign({}, defaultRole),
      rolesList: [],
      dialogVisible: false,
      dialogType: 'new',
      checkStrictly: false,
      checkAll: false,
      checkedPermissions: checkedPermissions,
      permissions: permissions,
      isIndeterminate: true,
		 rules: {
		   roleName: [
				 { required: true, message: 'Please enter a role name', trigger: 'change' },
				 { max: 20, message: 'Maximum length of 20 characters', trigger: 'change' }
			 ],
			 roleDescription: [
				 { max: 200, message: 'Maximum length of 200 characters', trigger: 'change' }
			 ]
		 }
    }
  },

  created() {
    this.getRoutes()// 初始化所有权限的数组
    this.getRoles()// 初始化所有角色信息
  },
  methods: {
    handleIsAvailableChange(row) {
				 updateIsAvailable(row.roleId, row.isAvailable).then(response => {
					 if (response.code != 20000) {
							 this.$message({
							  message: response.message,
							  type: 'error'
          })
					 }
      })
    },
    handleCheckAllChange(val) {
      this.checkedPermissions = val ? permissions : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(value) {
      const checkedCount = value.length

      this.checkAll = checkedCount === count
      this.isIndeterminate = checkedCount > 0 && checkedCount < count
    },
    async getRoutes() {
      const res = await getRoutes()
      this.permissions = res.data
      // 数据总数
      for (var i = 0; i < this.permissions.length; i++) {
        var data = this.permissions[i].data
        count += data.length
      }
      // 所有权限的数据集合
      for (var i = 0; i < this.permissions.length; i++) {
        var data = this.permissions[i].data
        for (var j = 0; j < data.length; j++) {
          var per = data[j].permissionId
          permissions.push(per)
        }
      }
    },
    async getRoles() {
      const res = await getRoles()
      this.rolesList = res.data
    },
    async getRoutesByRoleId(roleId) {
		  const res = await getRoutesByRoleId(roleId)
      checkedPermissions = res.data
		  this.checkedPermissions = res.data
      console.log(this.checkedPermissions.length + ':' + count)
      this.checkAll = this.checkedPermissions.length === count
      this.isIndeterminate = this.checkedPermissions.length > 0 && this.checkedPermissions.length < count
    },
    handleAddRole() {
      this.role = Object.assign({}, defaultRole)
      this.dialogType = 'new'
      this.dialogVisible = true
      this.checkedPermissions = []
      this.checkAll = false

      this.isIndeterminate = false
      this.$nextTick(() => {
			  this.$refs['dataForm'].clearValidate()
      })
    },
    handleEdit(scope) {
      this.dialogType = 'edit'
      this.dialogVisible = true
      this.checkStrictly = true
      this.role = deepClone(scope.row)
		  this.getRoutesByRoleId(scope.row.roleId)
      this.checkedPermissions = checkedPermissions
      this.isIndeterminate = true
      this.$nextTick(() => {
			  this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete({ $index, row }) {
      this.$confirm('Are you sure you want to delete this role?', 'INFO', {
        confirmButtonText: 'OK',
        cancelButtonText: 'CHANNEL',
        type: 'warning'
      })
        .then(async() => {
          await deleteRole(row.roleId)
          this.rolesList.splice($index, 1)
          this.$notify({
            title: 'success',
            message: 'delete success',
            type: 'success',
            duration: 2000
          })
        })
        .catch(err => { console.error(err) })
    },
    handleDisable({ $index, row }) {
      this.$confirm('Are you sure you want to disable this role?', 'INFO', {
			  confirmButtonText: 'OK',
			  cancelButtonText: 'CANCEL',
			  type: 'warning'
      })
			  .then(async() => {
			    await disableRole(row.roleId)
			    for (let index = 0; index < this.rolesList.length; index++) {
			    	if (this.rolesList[index].roleId === row.roleId) {
			    		this.rolesList[index].isAvailable = 0
			    		break
			    	}
			    }
			   this.$notify({
			     title: 'success',
			     message: 'oper success', type: 'success',
			     duration: 2000
			   })
			  })
			  .catch(err => { console.error(err) })
    },
    handleEnable({ $index, row }) {
		  this.$confirm('Are you sure you want to enable this role?', 'INFO', {
			  confirmButtonText: 'OK',
			  cancelButtonText: 'CANCEL',
			  type: 'warning'
      })
			 .then(async() => {
			  await enableRole(row.roleId)
          for (let index = 0; index < this.rolesList.length; index++) {
            if (this.rolesList[index].roleId === row.roleId) {
              this.rolesList[index].isAvailable = 1
              break
            }
          }
			 this.$notify({
			   title: 'success',
			   message: 'oper success', type: 'success',
			   duration: 2000
			 })
        })
			 .catch(err => { console.error(err) })
    },
    // 提交角色修改
    async confirmRole() {
      const isEdit = this.dialogType === 'edit'
      this.role.permissions = this.checkedPermissions

		 var v = this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (isEdit) {
            updateRole(this.role.roleId, this.role).then(response => {
              if (response.code == 20000) {
                for (let index = 0; index < this.rolesList.length; index++) {
                  if (this.rolesList[index].roleId === this.role.roleId) {
                    this.rolesList.splice(index, 1, Object.assign({}, this.role))
                    break
                  }
                }
                // 弹出提示
                this.$notify({
												  title: 'success',
												  message: 'oper success', type: 'success',
												  duration: 2000
                })
                // 关闭弹窗
                this.dialogVisible = false
              } else {
                this.$message.error(response.message)
              }
            })
          } else {
            addRole(this.role).then(result => {
              if (result.code == 20000) {
                this.$notify({
										  title: 'success',
										  message: 'oper success', type: 'success',
										  duration: 2000
                })
                this.rolesList.push(result.data)
                this.dialogVisible = false// 隐藏弹窗
              } else {
                this.$message.error(result.message)
              }
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .roles-table {
    margin-top: 30px;
  }
}
</style>

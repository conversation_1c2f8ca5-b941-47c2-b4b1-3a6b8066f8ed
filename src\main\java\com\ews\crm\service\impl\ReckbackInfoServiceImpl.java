package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.repository.ReckbackInfoRepository;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class ReckbackInfoServiceImpl implements ReckbackInfoService 
{
	@Autowired
	private ReckbackInfoRepository reckbackInfoRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<ReckbackInfo> findAll(Integer page, Integer size,String sortName,String sortOrder, ReckbackInfo reckbackInfo) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<ReckbackInfo> pages = reckbackInfoRepository.findAll(new Specification<ReckbackInfo>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<ReckbackInfo> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(reckbackInfo.getUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("userId").as(Long.class), reckbackInfo.getUserId()));
             }
             
             if(!StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchBegin()) || !StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchEnd())) {  
                 try {
                	 if(!StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchBegin()) && StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchEnd())) {//只有开始时间
                	 	Date begin= DateUtil.parseDate(reckbackInfo.getGmtCreateSearchBegin(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("gmtCreate").as(Date.class), begin));//小于开始时间
                	 }else if(StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchBegin()) && !StringUtils.isEmpty(reckbackInfo.getGmtCreateSearchEnd())) {//只有截至时间
                	 	Date end = DateUtil.parseDate(reckbackInfo.getGmtCreateSearchEnd(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("gmtCreate").as(Date.class), end));//大于截至时间
                	 }else {
                	 	Date begin= DateUtil.parseDate(reckbackInfo.getGmtCreateSearchBegin(), "yyyy-MM-dd HH:mm:ss");
                	 	Date end = DateUtil.parseDate(reckbackInfo.getGmtCreateSearchEnd(), "yyyy-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.between(root.get("gmtCreate"), begin, end));
                	 }
                 } catch (ParseException e) {
                	 e.printStackTrace();
                 }
                 
               }
             
             
             if(!StringUtils.isEmpty(reckbackInfo.getCrmUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("crmUserId").as(Long.class), reckbackInfo.getCrmUserId()));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getCrmOrderId())) { 
                predicates.add(criteriaBuilder.equal(root.get("crmOrderId").as(String.class),reckbackInfo.getCrmOrderId()));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getCrmTradeId())) { 
                predicates.add(criteriaBuilder.like(root.get("crmTradeId").as(String.class),"%"+reckbackInfo.getCrmTradeId()+"%"));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getGroupName())) { 
                predicates.add(criteriaBuilder.like(root.get("groupName").as(String.class),"%"+reckbackInfo.getGroupName()+"%"));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getSymbol())) { 
                predicates.add(criteriaBuilder.like(root.get("symbol").as(String.class),"%"+reckbackInfo.getSymbol()+"%"));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getReckbackUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("reckbackUserId").as(Long.class), reckbackInfo.getReckbackUserId()));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getReckbackOrderId())) { 
                predicates.add(criteriaBuilder.like(root.get("reckbackOrderId").as(String.class),"%"+reckbackInfo.getReckbackOrderId()+"%"));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getReckbackTradeId())) { 
                predicates.add(criteriaBuilder.equal(root.get("reckbackTradeId").as(String.class),reckbackInfo.getReckbackTradeId()));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getReckbackStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("reckbackStatus").as(Integer.class), reckbackInfo.getReckbackStatus()));
             }
             if(!StringUtils.isEmpty(reckbackInfo.getReckbackType())) { 
                predicates.add(criteriaBuilder.equal(root.get("reckbackType").as(Integer.class), reckbackInfo.getReckbackType()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public ReckbackInfo findById(Long id) {
      Optional<ReckbackInfo> op = reckbackInfoRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.reckbackInfoRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         ReckbackInfo old = reckbackInfoRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 reckbackInfoRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 reckbackInfoRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.reckbackInfoRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(ReckbackInfo reckbackInfo) {
       
        Result vr = new Result();
		try {
        	if (reckbackInfo.getId()== null) {
            	reckbackInfo.setGmtCreate(new Date());
            	reckbackInfo.setGmtModified(new Date());
            	reckbackInfo.setIsDeleted(0);
            	if(reckbackInfo.getIsAvailable() == null) {
            		reckbackInfo.setIsAvailable(1);
            	}
            	
	    	} else {
            	reckbackInfo.setGmtModified(new Date());
        	}
            reckbackInfoRepository.save(reckbackInfo);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



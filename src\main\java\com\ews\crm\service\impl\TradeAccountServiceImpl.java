package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.repository.TradeAccountRepository;
import com.ews.crm.service.TradeAccountService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class TradeAccountServiceImpl implements TradeAccountService 
{
	@Autowired
	private TradeAccountRepository tradeAccountRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<TradeAccount> findAll(Integer page, Integer size,String sortName,String sortOrder, TradeAccount tradeAccount) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<TradeAccount> pages = tradeAccountRepository.findAll(new Specification<TradeAccount>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<TradeAccount> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             
             if(!StringUtils.isEmpty(tradeAccount.getGmtCreateSearchBegin()) || !StringUtils.isEmpty(tradeAccount.getGmtCreateSearchEnd())) {  
                 try {
                	 if(!StringUtils.isEmpty(tradeAccount.getGmtCreateSearchBegin()) && StringUtils.isEmpty(tradeAccount.getGmtCreateSearchEnd())) {//只有开始时间
                	 	Date begin= DateUtil.parseDate(tradeAccount.getGmtCreateSearchBegin(), "YYYY-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("gmtCreate").as(Date.class), begin));//小于开始时间
                	 }else if(StringUtils.isEmpty(tradeAccount.getGmtCreateSearchBegin()) && !StringUtils.isEmpty(tradeAccount.getGmtCreateSearchEnd())) {//只有截至时间
                	 	Date end = DateUtil.parseDate(tradeAccount.getGmtCreateSearchEnd(), "YYYY-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("gmtCreate").as(Date.class), end));//大于截至时间
                	 }else {
                	 	Date begin= DateUtil.parseDate(tradeAccount.getGmtCreateSearchBegin(), "YYYY-MM-dd HH:mm:ss");
                	 	Date end = DateUtil.parseDate(tradeAccount.getGmtCreateSearchEnd(), "YYYY-MM-dd HH:mm:ss");
                	 	predicates.add(criteriaBuilder.between(root.get("gmtCreate"), begin, end));
                	 }
                 } catch (ParseException e) {
                	 e.printStackTrace();
                 }
                 
               }
             
             if(!StringUtils.isEmpty(tradeAccount.getTradeId())) { 
                predicates.add(criteriaBuilder.equal(root.get("tradeId").as(String.class),tradeAccount.getTradeId()));
             }
             if(!StringUtils.isEmpty(tradeAccount.getUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("userId").as(Long.class), tradeAccount.getUserId()));
             }
             if(!StringUtils.isEmpty(tradeAccount.getUserAccount())) { 
                predicates.add(criteriaBuilder.like(root.get("userAccount").as(String.class),"%"+tradeAccount.getUserAccount()+"%"));
             }
             if(!StringUtils.isEmpty(tradeAccount.getType())) { 
                predicates.add(criteriaBuilder.equal(root.get("type").as(Integer.class), tradeAccount.getType()));
             }
             if(!StringUtils.isEmpty(tradeAccount.getGroupName())) { 
                predicates.add(criteriaBuilder.like(root.get("groupName").as(String.class),"%"+tradeAccount.getGroupName()+"%"));
             }
             if(!StringUtils.isEmpty(tradeAccount.getLeverId())) { 
                predicates.add(criteriaBuilder.equal(root.get("leverId").as(Long.class), tradeAccount.getLeverId()));
             }
             if(!StringUtils.isEmpty(tradeAccount.getTradeStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("tradeStatus").as(Integer.class), tradeAccount.getTradeStatus()));
             }
             if(!StringUtils.isEmpty(tradeAccount.getIsAvailable())) { 
                 predicates.add(criteriaBuilder.equal(root.get("isAvailable").as(Integer.class), tradeAccount.getIsAvailable()));
              }
             
             if(tradeAccount.getUserInfoList()!=null&&tradeAccount.getUserInfoList().size()>0)
             {
            	    Path<Object> path = root.get("userId");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					for(int i=0;i<tradeAccount.getUserInfoList().size();i++) {
						  in.value(new Long(tradeAccount.getUserInfoList().get(i).toString()));
					}
					predicates.add(criteriaBuilder.and(in));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public TradeAccount findById(Long id) {
      Optional<TradeAccount> op = tradeAccountRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.tradeAccountRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         TradeAccount old = tradeAccountRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 tradeAccountRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 tradeAccountRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.tradeAccountRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(TradeAccount tradeAccount) {
      
        Result vr = new Result();
		try {
        	if (tradeAccount.getId()== null) {
            	tradeAccount.setGmtCreate(new Date());
            	tradeAccount.setGmtModified(new Date());
            	tradeAccount.setIsDeleted(0);
            	if(tradeAccount.getIsAvailable() == null) {
            		tradeAccount.setIsAvailable(1);
            	}
            	//tradeAccount.setUserCreate(loginUser.getUserId());
	    	} else {
            	tradeAccount.setGmtModified(new Date());
        	}
            tradeAccountRepository.save(tradeAccount);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



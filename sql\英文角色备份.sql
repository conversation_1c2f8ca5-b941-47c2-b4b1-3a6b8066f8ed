/*
SQLyog v10.2 
MySQL - 5.5.62-log : Database - newcrm
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`newcrm` /*!40100 DEFAULT CHARACTER SET utf8 */;

/*Table structure for table `sys_permission` */

DROP TABLE IF EXISTS `sys_permission`;

CREATE TABLE `sys_permission` (
  `permission_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限id',
  `permission_name` varchar(32) DEFAULT NULL COMMENT '权限名',
  `permission_sign` varchar(128) DEFAULT NULL COMMENT '权限标识,程序中判断使用,如"user:create"',
  `description` varchar(256) DEFAULT NULL COMMENT '权限描述,UI界面显示使用',
  `permission_group` varchar(128) DEFAULT NULL COMMENT '权限分组，用来分组显示。',
  PRIMARY KEY (`permission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='权限表';

/*Data for the table `sys_permission` */

insert  into `sys_permission`(`permission_id`,`permission_name`,`permission_sign`,`description`,`permission_group`) values (1,'Role Management','role:list','Role Management','系统设置'),(2,'User Management','user:list','User Management','系统设置'),(3,'Website Settings','wangzhanxinxi','Website Settings','系统设置'),(6,'System Notificaiton','gonggaoguanli','System Notificaiton','系统设置'),(7,'Exchange Rate Setting','huilvshezhi','Exchange Rate Setting','交易设置'),(8,'Leverage Settings','gangganshezhi','Leverage Settings','交易设置'),(9,'Product Setup','jiaoyipinzhong','Product Setup','交易设置'),(10,'User Group','yonghujiaoyhizu','User Group','交易设置'),(11,'Account Type','zhanghaoleixing','Account Type','交易设置'),(12,'Commission Rule','fanyongguizezu','Commission Rule','交易设置'),(14,'Deposit Channel','rujinzhanghhu','Deposit Channel','出入金设置'),(15,'Withdrawal Risk Management','chujinpeizhi','Withdrawal Risk Management','出入金设置'),(16,'Withdrawal Channel','chujinzhanghu','Withdrawal Channel','出入金设置'),(17,'User Approval','yonghushenpi','User Approval','审批管理'),(18,'Deposit Approval','rujinshenpi','Deposit Approval','审批管理'),(19,'Withdrawal Approval','chujinshennpi','Withdrawal Approval','审批管理'),(20,'Trading Account Approval','tongmingzhanghao','Trading Account Approval','审批管理'),(21,'Internal Transfer Approval','tongmingzhuanzhang','Internal Transfer Approval','审批管理'),(22,'Agent User','dailiyonghu','Agent User','代理管理'),(23,'CRM User','crmyonghu','CRM User','代理管理'),(24,'Trading User','jiaoyiyonghu','Trading User','代理管理'),(25,'Deposit Log','rujinjilu','Deposit Log','财务管理'),(26,'Withdrawal Log','chujinjilu','Withdrawal Log','财务管理'),(27,'Internal Transfer Log','zhuanzhangjilu','Internal Transfer Log','财务管理'),(28,'Commission Log','yongjinjilu','Commission Log','财务管理'),(29,'Agent Sales','dailiyeji','Agent Sales','代理管理'),(30,'Operation Log','caozuorizhi','Operation Log','系统设置'),(31,'Payment Approval','zhifushenpi','Payment Approval','审批管理'),(32,'Payment Failure','zhifushibai','Payment Failure','审批管理'),(33,'View Pending Deposit Request','rujindaiqueren','View Pending Deposit Request','财务管理'),(34,'View Pending Withdrawal Request','chujindaiqueren','View Pending Withdrawal Request','财务管理'),(35,'View CRM User','crmyonghuchakan','View CRM User','代理管理'),(36,'View Trading Account','jiaoyiyonghuchakan','View Trading Account','代理管理'),(37,'User Tag','yonghubiaoqian','User Tag','系统设置');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

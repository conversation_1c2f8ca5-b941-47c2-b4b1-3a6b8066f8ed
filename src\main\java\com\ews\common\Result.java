package com.ews.common;

/**
 * 验证结果实体对象 用户权限校验等功能
 * 
 * <AUTHOR>
 *
 */
public class Result {

	/**
	 * 操作成功
	 */
	public static final String CODESUCCESS = "20000";

	/**
	 * 操作失败
	 */
	public static final String CODEFAIL = "fail";

	/**
	 * 400 语义/参数错误
	 */
	public static final Integer ERRTYPE400 = 400;
	/**
	 * 401 未登录
	 */
	public static final Integer ERRTYPE401 = 401;
	/**
	 * 403 无权限
	 */
	public static final Integer ERRTYPE403 = 403;
	/**
	 * 404 无资源
	 */
	public static final Integer ERRTYPE404 = 404;
	/**
	 * 500 程序错误
	 */
	public static final Integer ERRTYPE500 = 500;//

	/**
	 * 
	 * success fail
	 * 
	 * 
	 */
	private String code;
	/**
	 * 结果信息
	 */
	private String message;

	/**
	 * 错误类型，当 code 为 fail时，按业务需要去内置或者自定义错误类型
	 */
	private int errType;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public int getErrType() {
		return errType;
	}

	public void setErrType(int errType) {
		this.errType = errType;
	}

}

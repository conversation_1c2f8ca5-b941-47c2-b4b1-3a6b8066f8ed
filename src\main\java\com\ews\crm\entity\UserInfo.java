package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "user_info")
public class UserInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

	
	  /**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchBegin;

	    /**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchEnd;
    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *姓氏
    **/
	@Column(name = "surname")
	protected  String surname;

    /**
    *名字
    **/
	@Column(name = "name")
	protected  String name;

    /**
    *姓名
    **/
	@Column(name = "fullname")
	protected  String fullname;

    /**
    *账号
    **/
	@Column(name = "user_name")
	protected  String userName;

    /**
    *邮箱
    **/
	@Column(name = "email")
	protected  String email;

    /**
    *手机号
    **/
	@Column(name = "tel")
	protected  String tel;

    /**
    *密码
    **/
	@Column(name = "password")
	protected  String password;

    /**
    *国家地区
    **/
	@Column(name = "country")
	protected  String country;

    /**
    *省
    **/
	@Column(name = "province")
	protected  String province;

    /**
    *市
    **/
	@Column(name = "city")
	protected  String city;

    /**
    *性别
    **/
	@Column(name = "gender")
	protected  Integer gender;

    /**
    *详细地址
    **/
	@Column(name = "adress")
	protected  String adress;

    /**
    *昵称
    **/
	@Column(name = "nickname")
	protected  String nickname;

    /**
    *出生日期
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd") 
	protected  Date birthday;

    /**
    *身份证号
    **/
	@Column(name = "identity_num")
	protected  String identityNum;

    /**
    *身份证正面
    **/
	@Column(name = "image_front")
	protected  String imageFront;

    /**
    *身份证反面
    **/
	@Column(name = "image_back")
	protected  String imageBack;

    /**
    *邀请码
    **/
	@Column(name = "invitation_code")
	protected  String invitationCode;

    /**
    *主账户
    **/
	@Column(name = "amount_main")
	protected  Double amountMain;

    /**
    *副账户
    **/
	@Column(name = "amount_sub")
	protected  Double amountSub;

    /**
    *用户类型
    **/
	@Column(name = "user_type")
	protected  Long userType;

    /**
    *父用户
    **/
	@Column(name = "parent_id")
	protected  Long parentId;

    /**
    *姓名拼音
    **/
	@Column(name = "pinyin")
	protected  String pinyin;

    /**
    *备注
    **/
	@Column(name = "remark")
	protected  String remark;

    /**
    *返佣类型
    **/
	@Column(name = "rake_back_type")
	protected  Integer rakeBackType;

    /**
    *返佣比例
    **/
	@Column(name = "rake_back_amount")
	protected  Double rakeBackAmount;

    /**
    *销售代理
    **/
	@Column(name = "is_agent")
	protected  Integer isAgent;

    /**
    *审批人
    **/
	@Column(name = "audit_id")
	protected  Long auditId;

    /**
    *审核时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date auditTime;

    /**
    *backup1   --首选语言
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2  所属代理
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**  
    *backup3    --临时存放 交易用户的类型    &图片3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4   -临时存放 用户的资金渠道信息  applicantID
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5   --Tag
    **/
	@Column(name = "backup5")
	protected  String backup5;

    /**
    *backup6  --临时显示Tag Show   保存kyc的返回信息
    **/
	@Column(name = "backup6")
	protected  String backup6;
	
	
	
	
	

	@Transient
	protected  Integer sortNum;
	
	

	@Transient
	protected  List userInfoList;
	
	
	@Transient
	protected  String kycInfo;
    public List getUserInfoList() {
		return userInfoList;
	}
	public void setUserInfoList(List userInfoList) {
		this.userInfoList = userInfoList;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getSurname()
    {
        return surname;
    }
    public void setSurname(String  surname)
    {
        this.surname = surname;
    }
    public String  getName()
    {
        return name;
    }
    public void setName(String  name)
    {
        this.name = name;
    }
    public String  getFullname()
    {
        return fullname;
    }
    public void setFullname(String  fullname)
    {
        this.fullname = fullname;
    }
    public String  getUserName()
    {
        return userName;
    }
    public void setUserName(String  userName)
    {
        this.userName = userName;
    }
    public String  getEmail()
    {
        return email;
    }
    public void setEmail(String  email)
    {
        this.email = email;
    }
    public String  getTel()
    {
        return tel;
    }
    public void setTel(String  tel)
    {
        this.tel = tel;
    }
    public String  getPassword()
    {
        return password;
    }
    public void setPassword(String  password)
    {
        this.password = password;
    }
    public String  getCountry()
    {
        return country;
    }
    public void setCountry(String  country)
    {
        this.country = country;
    }
    public String  getProvince()
    {
        return province;
    }
    public void setProvince(String  province)
    {
        this.province = province;
    }
    public String  getCity()
    {
        return city;
    }
    public void setCity(String  city)
    {
        this.city = city;
    }
    public Integer  getGender()
    {
        return gender;
    }
    public void setGender(Integer  gender)
    {
        this.gender = gender;
    }
    public String  getAdress()
    {
        return adress;
    }
    public void setAdress(String  adress)
    {
        this.adress = adress;
    }
    public String  getNickname()
    {
        return nickname;
    }
    public void setNickname(String  nickname)
    {
        this.nickname = nickname;
    }
    public Date  getBirthday()
    {
        return birthday;
    }
    public void setBirthday(Date  birthday)
    {
        this.birthday = birthday;
    }
    public String  getIdentityNum()
    {
        return identityNum;
    }
    public void setIdentityNum(String  identityNum)
    {
        this.identityNum = identityNum;
    }
    public String  getImageFront()
    {
        return imageFront;
    }
    public void setImageFront(String  imageFront)
    {
        this.imageFront = imageFront;
    }
    public String  getImageBack()
    {
        return imageBack;
    }
    public void setImageBack(String  imageBack)
    {
        this.imageBack = imageBack;
    }
    public String  getInvitationCode()
    {
        return invitationCode;
    }
    public void setInvitationCode(String  invitationCode)
    {
        this.invitationCode = invitationCode;
    }
    public Double  getAmountMain()
    {
        return amountMain;
    }
    public void setAmountMain(Double  amountMain)
    {
        this.amountMain = amountMain;
    }
    public Double  getAmountSub()
    {
        return amountSub;
    }
    public void setAmountSub(Double  amountSub)
    {
        this.amountSub = amountSub;
    }
    public Long  getUserType()
    {
        return userType;
    }
    public void setUserType(Long  userType)
    {
        this.userType = userType;
    }
    public Long  getParentId()
    {
        return parentId;
    }
    public void setParentId(Long  parentId)
    {
        this.parentId = parentId;
    }
    public String  getPinyin()
    {
        return pinyin;
    }
    public void setPinyin(String  pinyin)
    {
        this.pinyin = pinyin;
    }
    public String  getRemark()
    {
        return remark;
    }
    public void setRemark(String  remark)
    {
        this.remark = remark;
    }
    public Integer  getRakeBackType()
    {
        return rakeBackType;
    }
    public void setRakeBackType(Integer  rakeBackType)
    {
        this.rakeBackType = rakeBackType;
    }
    public Double  getRakeBackAmount()
    {
        return rakeBackAmount;
    }
    public void setRakeBackAmount(Double  rakeBackAmount)
    {
        this.rakeBackAmount = rakeBackAmount;
    }
    public Integer  getIsAgent()
    {
        return isAgent;
    }
    public void setIsAgent(Integer  isAgent)
    {
        this.isAgent = isAgent;
    }
    public Long  getAuditId()
    {
        return auditId;
    }
    public void setAuditId(Long  auditId)
    {
        this.auditId = auditId;
    }
    public Date  getAuditTime()
    {
        return auditTime;
    }
    public void setAuditTime(Date  auditTime)
    {
        this.auditTime = auditTime;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public String  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(String  backup5)
    {
        this.backup5 = backup5;
    }
    public String  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(String  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public String getGmtCreateSearchBegin() {
		return gmtCreateSearchBegin;
	}
	public void setGmtCreateSearchBegin(String gmtCreateSearchBegin) {
		this.gmtCreateSearchBegin = gmtCreateSearchBegin;
	}
	public String getGmtCreateSearchEnd() {
		return gmtCreateSearchEnd;
	}
	public void setGmtCreateSearchEnd(String gmtCreateSearchEnd) {
		this.gmtCreateSearchEnd = gmtCreateSearchEnd;
	}
	public String getKycInfo() {
		return kycInfo;
	}
	public void setKycInfo(String kycInfo) {
		this.kycInfo = kycInfo;
	}
    
    

}

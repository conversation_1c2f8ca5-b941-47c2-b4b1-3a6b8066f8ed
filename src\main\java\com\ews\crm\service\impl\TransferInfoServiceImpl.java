package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.repository.TransferInfoRepository;
import com.ews.crm.service.TransferInfoService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class TransferInfoServiceImpl implements TransferInfoService 
{
	@Autowired
	private TransferInfoRepository transferInfoRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<TransferInfo> findAll(Integer page, Integer size,String sortName,String sortOrder, TransferInfo transferInfo) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<TransferInfo> pages = transferInfoRepository.findAll(new Specification<TransferInfo>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<TransferInfo> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(transferInfo.getUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("userId").as(Long.class), transferInfo.getUserId()));
             }
             if(!StringUtils.isEmpty(transferInfo.getTransferOut())) { 
                predicates.add(criteriaBuilder.equal(root.get("transferOut").as(Integer.class), transferInfo.getTransferOut()));
             }
             if(!StringUtils.isEmpty(transferInfo.getTransferIn())) { 
                predicates.add(criteriaBuilder.equal(root.get("transferIn").as(Integer.class), transferInfo.getTransferIn()));
             }
             if(!StringUtils.isEmpty(transferInfo.getOutStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("outStatus").as(Integer.class), transferInfo.getOutStatus()));
             }
             if(!StringUtils.isEmpty(transferInfo.getInStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("inStatus").as(Integer.class), transferInfo.getInStatus()));
             }
             if(!StringUtils.isEmpty(transferInfo.getTransferStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("transferStatus").as(Integer.class), transferInfo.getTransferStatus()));
             }
             if(!StringUtils.isEmpty(transferInfo.getAuditStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("auditStatus").as(Integer.class), transferInfo.getAuditStatus()));
             }
             if(!StringUtils.isEmpty(transferInfo.getOutOrderId())) { 
                predicates.add(criteriaBuilder.like(root.get("outOrderId").as(String.class),"%"+transferInfo.getOutOrderId()+"%"));
             }
             if(!StringUtils.isEmpty(transferInfo.getInOrderId())) { 
                predicates.add(criteriaBuilder.like(root.get("inOrderId").as(String.class),"%"+transferInfo.getInOrderId()+"%"));
             }
             
             if(transferInfo.getUserInfoList()!=null&&transferInfo.getUserInfoList().size()>0)
             {
            	    Path<Object> path = root.get("userId");
					CriteriaBuilder.In<Object> in = criteriaBuilder.in(path);
					for(int i=0;i<transferInfo.getUserInfoList().size();i++) {
						  in.value(new Long(transferInfo.getUserInfoList().get(i).toString()));
					}
					predicates.add(criteriaBuilder.and(in));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public TransferInfo findById(Long id) {
      Optional<TransferInfo> op = transferInfoRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.transferInfoRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         TransferInfo old = transferInfoRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 transferInfoRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 transferInfoRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.transferInfoRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(TransferInfo transferInfo) {
       
        Result vr = new Result();
		try {
        	if (transferInfo.getId()== null) {
            	transferInfo.setGmtCreate(new Date());
            	transferInfo.setGmtModified(new Date());
            	transferInfo.setIsDeleted(0);
            	if(transferInfo.getIsAvailable() == null) {
            		transferInfo.setIsAvailable(1);
            	}
            	
	    	} else {
            	transferInfo.setGmtModified(new Date());
        	}
            transferInfoRepository.save(transferInfo);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



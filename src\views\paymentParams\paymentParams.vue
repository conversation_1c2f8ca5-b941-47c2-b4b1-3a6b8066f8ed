<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.paramName" placeholder="参数名" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.paramType" style="width: 220px" placeholder="参数类型" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in paramTypes" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参数名" prop="paramName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.paramName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="排序" prop="sort" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sort }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="参数名" prop="paramName">
          <el-input v-model="temp.paramName" />
        </el-form-item>
        <el-form-item label="参数类型" prop="paramType">
          <el-select v-model="temp.paramType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="paramType in paramTypes"
              :key="paramType.value"
              :label="paramType.label"
              :value="paramType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交参数" prop="isSubmit">
          <el-select v-model="temp.isSubmit" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isSubmit in isSubmits"
              :key="isSubmit.value"
              :label="isSubmit.label"
              :value="isSubmit.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="返回参数" prop="isReturn">
          <el-select v-model="temp.isReturn" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isReturn in isReturns"
              :key="isReturn.value"
              :label="isReturn.label"
              :value="isReturn.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回调参数" prop="isReback">
          <el-select v-model="temp.isReback" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isReback in isRebacks"
              :key="isReback.value"
              :label="isReback.label"
              :value="isReback.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参与加密" prop="backup1">
          <el-select v-model="temp.backup1" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="cyjm in cyjms"
              :key="cyjm.value"
              :label="cyjm.label"
              :value="cyjm.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参数值" prop="paramValue">
          <el-input v-model="temp.paramValue" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="temp.sort" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="参数名" prop="paramName">
          <el-input v-model="temp.paramName" />
        </el-form-item>
        <el-form-item label="参数类型" prop="paramType">
          <el-select v-model="temp.paramType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="paramType in paramTypes"
              :key="paramType.value"
              :label="paramType.label"
              :value="paramType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交参数" prop="isSubmit">
          <el-select v-model="temp.isSubmit" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isSubmit in isSubmits"
              :key="isSubmit.value"
              :label="isSubmit.label"
              :value="isSubmit.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="返回参数" prop="isReturn">
          <el-select v-model="temp.isReturn" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isReturn in isReturns"
              :key="isReturn.value"
              :label="isReturn.label"
              :value="isReturn.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回调参数" prop="isReback">
          <el-select v-model="temp.isReback" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isReback in isRebacks"
              :key="isReback.value"
              :label="isReback.label"
              :value="isReback.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参与加密" prop="backup1">
          <el-select v-model="temp.backup1" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="cyjm in cyjms"
              :key="cyjm.value"
              :label="cyjm.label"
              :value="cyjm.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="参数值" prop="paramValue">
          <el-input v-model="temp.paramValue" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="temp.sort" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchPaymentParams, createPaymentParams, updatePaymentParams, updateIsAvailable, removePaymentParams } from '@/api/paymentParams'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'PaymentParamsTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        paramName: undefined,
        paramType: undefined,
        isReturn: undefined,
        isReback: undefined,
        paymentId: undefined
      },
      temp: {
        id: undefined,
        paymentId: undefined,
        paramName: '',
        paramType: '',
        isSubmit: '',
        isReturn: '',
        isReback: '',
        paramValue: '',
        sort: '',
        backup1: '',
        paymentId: ''
      },
      temp_id: undefined,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 paramTypes: [
        {
          value: 1,
          label: '数量'
        },
        {
          value: 2,
          label: '订单ID'
        },
        {
          value: 3,
          label: '用户名'
        },
        {
          value: 4,
          label: '状态'
        },
        {
          value: 5,
          label: '数据'
        },
        {
          value: 6,
          label: '常量'
        },
        {
          value: 7,
          label: '第三方流水号'
        }
      ],
				 isSubmits: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ],
				 isReturns: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ],
				 isRebacks: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ], cyjms: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ],
      rules: {
        paramName: [
          { required: true, message: '参数名不能为空', trigger: 'change' },,
        ],
        paramType: [
          { required: true, message: '参数类型不能为空', trigger: 'change' },,
        ],
        isSubmit: [
        ],
        isReturn: [
          { required: true, message: '返回参数不能为空', trigger: 'change' },,
        ],
        isReback: [
          { required: true, message: '回调参数不能为空', trigger: 'change' },,
        ],
        paramValue: [
        ],
        sort: [
          { required: true, message: '排序不能为空', trigger: 'change' },,
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        paymentId: [
          { required: true, message: '支付ID不能为空', trigger: 'change' },,
        ]
      }
    }
  },
  created() {
    const id = this.$route.params && this.$route.params.id
        				if (id != null && id != undefined && id != '') {
        		       this.listQuery.paymentId = id
      this.temp_id = id
        				}
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        paymentId: undefined,
        paramName: '',
        paramType: '',
        isSubmit: '',
        isReturn: '',
        isReback: '',
        paramValue: '',
        sort: '',
        backup1: '',
        paymentId: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.temp.paymentId = this.temp_id
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createPaymentParams(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updatePaymentParams(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removePaymentParams(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

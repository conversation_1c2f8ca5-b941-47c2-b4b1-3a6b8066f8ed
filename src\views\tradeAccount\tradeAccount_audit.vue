<template>
  <div class="app-container">

    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label1')" prop="gmtCreate" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label2')" prop="fullname" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.fullname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label3')" prop="userName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label4')" prop="tel" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.tel }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label5')" prop="city" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userInfo.country }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label6')" prop="groupName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.groupName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label7')" prop="leverShow" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>1:{{ scope.row.leverShow }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('tradeAccountAudit.label8')" prop="backup3" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup3 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleAudit(scope.row)">{{ $t('tradeAccountAudit.label9') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('tradeAccountAudit.label10') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList2, fetchTradeAccount, createTradeAccount, updateTradeAccount, updateIsAvailable, removeTradeAccount, auditTradeAccount } from '@/api/tradeAccount'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'TradeAccountTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        tradeId: undefined,
        userId: undefined,
        userAccount: undefined,
        type: undefined,
        groupName: undefined,
        leverId: undefined,
        tradeStatus: undefined
      },
      temp: {
        id: undefined,
        tradeId: '',
        userId: '',
        userAccount: '',
        type: '',
        groupName: '',
        leverId: '',
        leverShow: '',
        tradeStatus: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        tradeId: [,
        ],
        userId: [
        ],
        userAccount: [,
        ],
        type: [
        ],
        groupName: [
        ],
        leverId: [
        ],
        leverShow: [
        ],
        tradeStatus: [
        ],
        balance1: [
        ],
        balance2: [
        ],
        balance3: [
        ],
        balance4: [
        ],
        balance5: [
        ],
        balance6: [
        ],
        balance7: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList2(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        tradeId: '',
        userId: '',
        userAccount: '',
        type: '',
        groupName: '',
        leverId: '',
        leverShow: '',
        tradeStatus: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createTradeAccount(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateTradeAccount(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to reject this application?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeTradeAccount(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'Rejected successfully',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    },
				 handleAudit(row) {
      this.$confirm('Are you sure you want to review this application?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await auditTradeAccount(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'Audit successful',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

import request from '@/utils/request'
	export function fetchList(query) {
		return request({
			url: '/orderInfo/list',
			method: 'post',
			params: query
		})
	}
export function fetchMyList(query) {
	return request({
		url: '/orderInfo/mylist',
		method: 'post',
		params: query
	})
}
export function fetchTeamList(query) {
	return request({
		url: '/orderInfo/teamlist',
		method: 'post',
		params: query
	})
}
export function fetchOrderInfo(id) {
	return request({
		url: '/orderInfo/detail',
		method: 'get',
		params: { id }
	})
}


export function createOrderInfo(data) {
	return request({
		url: '/orderInfo/add',
		method: 'post',
		data
	})
}

export function updateOrderInfo(data) {
	return request({
		url: '/orderInfo/update',
		method: 'post',
		data
	})
}
export function updateIsAvailable(id,isAvailable) {
	return request({
		url: '/orderInfo/updateIsAvailable',
		method: 'get',
		params: { id ,isAvailable },
	})
}
export function removeOrderInfo(id) {
	return request({
		url: '/orderInfo/remove',
		method: 'get',
		params: { id },
	})
}





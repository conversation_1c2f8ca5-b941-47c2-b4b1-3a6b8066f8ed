<template>
  <el-row>
    <el-col v-if="(this.dqryType=='999'||this.dqryType=='0')?true:false" :span="24">
      <div style="border: 1px solid #ececec;width:95%;height:auto;margin-top:35px;margin-left: 30px;border-radius:10px;">
        <div style="margin-top:10px;margin-bottom: 20px;border-bottom: 1px solid #ececec;">
          <div style="margin-left:20px;margin-bottom:10px;height:30px;border:1px;font-size:16px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;">
            {{ $t('dashboard.title1') }}
          </div>
        </div>
        <div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
              <center>
                <div class="grid-content bg-purple" style="width:100%;max-width:200px;margin-bottom:20px;">
                  <div style="font-size:14px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;"> {{ $t('dashboard.title2') }}</div>
                  <div> <el-link style="font-size:30px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:red;line-height:46px;" @click="toDetail1()">{{ dshyh }}</el-link></div>
                </div>
              </center></el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
              <center>
                <div class="grid-content bg-purple" style="width:100%;max-width:200px;margin-bottom:20px;">
                  <div style="font-size:14px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;"> {{ $t('dashboard.title3') }}</div>
                  <div><el-link style="font-size:30px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:red;line-height:46px;" @click="toDetail2()">{{ rjdsp }}</el-link></div>
                </div>
              </center></el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
              <center>
                <div class="grid-content bg-purple" style="width:100%;max-width:200px;margin-bottom:20px;">
                  <div style="font-size:14px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;"> {{ $t('dashboard.title4') }}</div>
                  <div><el-link style="font-size:30px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:red;line-height:46px;" @click="toDetail3()">{{ cjdsp }}</el-link></div>
                </div>
              </center></el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
              <center>
                <div class="grid-content bg-purple" style="width:100%;max-width:200px;margin-bottom:20px;">
                  <div style="font-size:14px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;"> {{ $t('dashboard.title5') }}</div>
                  <div><el-link style="font-size:30px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:red;line-height:46px;" @click="toDetail4()">{{ tmzhsp }}</el-link></div>
                </div>
              </center></el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="4" :xl="4">
              <center>
                <div class="grid-content bg-purple" style="width:100%;max-width:200px;margin-bottom:20px;">
                  <div style="font-size:14px; font-family:PingFangSC-Regular,PingFang SC;color:rgba(0,0,0,0.5);line-height:28px;font-weight:400;">{{ $t('dashboard.title6') }}</div>
                  <div><el-link style="font-size:30px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:red;line-height:46px;" @click="toDetail5()">{{ tmzzsp }}</el-link></div>
                </div>
              </center></el-col>
          </el-row>
        </div>
      </div>
    </el-col>
    <el-col v-if="(this.dqryType!='999'&&this.dqryType!='0')?true:false" :span="24">
      <div style="margin-top:55px;">
        <div>
          <center> <!--img :src="backgroudImg_url" width="100%" style="max-width:45%;"--></center>
        </div>
      </div>
    </el-col>
  </el-row>

</template>
<script>
import { mapGetters } from 'vuex'
import PanThumb from '@/components/PanThumb'
import GithubCorner from '@/components/GithubCorner'
import Setting from '@/settings'
import { getInfo } from '@/api/navbar'

import { indexInfo } from '@/api/user'

export default {
  name: 'DashboardEditor',
  components: { PanThumb, GithubCorner },
  data() {
    return {
      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3',
      dqryType: undefined,
      dshyh: 0,
      rjdsp: 0,
      cjdsp: 0,
      tmzhsp: 0,
      tmzzsp: 0
    }
  }, created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })
    indexInfo().then(response => {
        	this.dshyh = response.data.dshyh
         	this.rjdsp = response.data.rjdsp
      this.cjdsp = response.data.cjdsp
      this.tmzhsp = response.data.tmzhsp
      this.tmzzsp = response.data.tmzzsp
    })
    this.backgroudImg_url = Setting.backgroudImg_url
  },

  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'roles'
    ])
  }, methods: {

    toDetail1() {
      this.$router.push({
        path: '/auditManager/auditUser'
      })
    },
    toDetail2() {
      this.$router.push({
        path: '/auditManager/depositAudit'
      })
    },
    toDetail3() {
      this.$router.push({
        path: '/auditManager/withdrawalAudit'
      })
    },
    toDetail4() {
      this.$router.push({
        path: '/auditManager/auditAccount'
      })
    },
    toDetail5() {
      this.$router.push({
        path: '/auditManager/auditTransfer'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .emptyGif {
    display: block;
    width: 45%;
    margin: 0 auto;
  }

  .dashboard-editor-container {
    background-color: #e3e3e3;
    min-height: 80vh;
    padding: 50px 60px 0px;
    .info-container {
      position: relative;
      margin-left: 190px;
      height: 150px;
      line-height: 200px;
      .display_name {
        font-size: 48px;
        line-height: 48px;
        color: #212121;
        position: absolute;
        top: 25px;
      }
    }
  }
</style>

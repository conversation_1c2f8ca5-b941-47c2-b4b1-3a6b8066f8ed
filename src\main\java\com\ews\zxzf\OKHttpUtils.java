package com.ews.zxzf;


import okhttp3.*;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ****************************************************
 * author sb
 * created on: 17/12/25 下午1:26
 * e-mail: <EMAIL>
 * name: wsj
 * desc:  wsj is sb
 * ****************************************************
 */
public class OKHttpUtils {
    private static volatile OKHttpUtils mOKHttpUtils;
    private OkHttpClient mOkHttpClient;

    // private Handler mHandler;


    private OKHttpUtils() {
        //TODO do sth

        //   mHandler = new Handler(Looper.getMainLooper());
        int cacheSize = 10 * 1024 * 1024; //10M
        int cache = 10 << 20; //10M
        mOkHttpClient = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS) //连接超时时间
                .readTimeout(120, TimeUnit.SECONDS) //读取超时时间
                .writeTimeout(120, TimeUnit.SECONDS) //写入超时时间
//                .cache(new Cache(context.getCacheDir(), cache))//开启缓存
                .build();
    }

    /**
     * 单例化
     *
     * @return
     */
    public static OKHttpUtils newInstance() {
        if (mOKHttpUtils == null) {
            synchronized (OKHttpUtils.class) {
                if (mOKHttpUtils == null) {
                    mOKHttpUtils = new OKHttpUtils();
                }
            }
        }
        return mOKHttpUtils;
    }

    //=======================================================================
    // GET异步方式
    //  因为结果集也就是,自带的两个回调方法是在子线程中执行的,所以每次我们调用都得发送到主线程
    // 1. 把结果集想办法发送到主线程中   ---> Hanlder
    // 2. 如何把结果集返回给调用者   ---> 接口回调方式
    //=======================================================================
    public void getAsyncData(String url, final OnResultListener listener) {
        Request request = new Request.Builder()
                .url(url)
                .build();
        Call call = mOkHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                try {
                    listener.onFailure(call, e);
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                /*mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onFailure(call, e);
                        }
                    }
                });*/

            }

            @Override
            public void onResponse(final Call call, Response response) throws IOException {
                String result = response.body().string();
                listener.onSuccess(call, result);
                /*mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onSuccess(call, result);
                        }
                    }
                });*/
            }
        });
    }

    //=======================================================================
    // GET同步方式
    //  因为结果集也就是,自带的两个回调方法是在子线程中执行的,所以每次我们调用都得发送到主线程
    // 1. 把结果集想办法发送到主线程中   ---> Hanlder
    // 2. 如何把结果集返回给调用者   ---> 接口回调方式
    //=======================================================================
    public String getSyncData(String url) {
        Request request = new Request.Builder()
                .url(url)
                .build();
        final Call call = mOkHttpClient.newCall(request);
        Response response = null;
        try {
            response = call.execute();
            String result = response.body().string();
            if (result != null && !"".equals(result)) {
                return result;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    //=======================================================================
    // Post请求方式
    //=======================================================================
    public String postSyncData(Map<String, String> map, String url) {
        FormBody.Builder builder = new FormBody.Builder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            builder.add(entry.getKey(), entry.getValue());
        }
        RequestBody requestBody = builder.build();

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        Call call = mOkHttpClient.newCall(request);
        try {
            Response response = call.execute();
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
            return  e.getMessage();
        }
    }
    /**
	 * POST请求
	 */
	public String doPost(String url, Map<String, String> param) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建参数列表
			if (null != param) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, "UTF-8");
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (null != response) {
					response.close();
				}
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}


    //=======================================================================
    // Post请求方式
    //=======================================================================
    public void postAsnycData(Map<String, String> map, String url, final OnResultListener listener) {
        FormBody.Builder builder = new FormBody.Builder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            builder.add(entry.getKey(), entry.getValue());
        }
        RequestBody requestBody = builder.build();

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = mOkHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                //   String string = call.execute().body().string();
                //   System.out.println(string);
                System.out.println(e.getMessage());
                //   listener.onFailure(call, e);
                /*mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onFailure(call, e);
                        }
                    }
                });*/
            }

            @Override
            public void onResponse(final Call call, Response response) throws IOException {
                String result = response.body().string();
                listener.onSuccess(call, result);
                /*mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onSuccess(call, result);
                        }
                    }
                });*/
            }
        });
    }

    //=======================================================================
    // Post请求方式 json 传参方式
    //=======================================================================
    public void postAsnycJsonData(String json, String url, final OnResultListener listener) {
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),json);

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = mOkHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                try {
                    listener.onFailure(call,e);
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                //android 切换线程
                /*mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onFailure(call, e);
                        }
                    }
                });*/
            }

            @Override
            public void onResponse(final Call call, Response response) throws IOException {
                String result = response.body().string();
                listener.onSuccess(call,result);
                //android 切换线程
               /* mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.onSuccess(call, result);
                        }
                    }
                });*/
            }
        });
    }

    //=======================================================================
    // Post同步请求方式 json 传参方式
    //=======================================================================
    public String postSyncJsonData(String json, String url){
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"),json);

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = mOkHttpClient.newCall(request);
        try {
            Response response = call.execute();
            String result = response.body().string();
            if (result != null && !"".equals(result)) {
                return result;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    //=======================================================================
    // 下载
    //=======================================================================
    public void downloadData(final String url, final String filepath) {
        Request request = new Request.Builder()
                .url(url)
                .build();
        Call call = mOkHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) {
                InputStream is = null;
                byte[] buffer = new byte[1024];
                int length = 0;
                FileOutputStream fos = null;

                try {
                    is = response.body().byteStream();
                    File file = new File(filepath, getName(url));
                    fos = new FileOutputStream(file);
                    while ((length = is.read(buffer)) != -1) {
                        fos.write(buffer, 0, length);
                    }
                    fos.flush();
                } catch (IOException e) {

                } finally {

                    try {
                        if (is != null) {
                            is.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    try {
                        if (fos != null) {
                            fos.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        });

    }


    /**
     * 通过url获取名字
     *
     * @param path
     * @return
     */
    public String getName(String path) {
        int indexOf = path.lastIndexOf("/");
        if (indexOf < 0) {
            return path;
        } else {
            return path.substring(indexOf + 1);
        }
    }

    //返回给调用者
    public interface OnResultListener {
        void onFailure(Call call, IOException e) throws IOException;

        void onSuccess(Call call, String response) throws IOException;
    }


    /**
     * 取消对应tag值的请求
     *
     * @param tag
     */
    public void cancelTag(Object tag) {
        for (Call call : mOkHttpClient.dispatcher().queuedCalls()) {
            if (tag.equals(call.request().tag())) {
                call.cancel();
            }
        }

        for (Call call : mOkHttpClient.dispatcher().runningCalls()) {
            if (tag.equals(call.request().tag())) {
                call.cancel();
            }
        }
    }


}
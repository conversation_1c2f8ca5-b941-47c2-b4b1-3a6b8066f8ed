<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.crmOrderId" :placeholder="$t('reckbackInfo.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.crmTradeId" :placeholder="$t('reckbackInfo.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <!--el-input v-model="listQuery.groupName" placeholder="用户交易组" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" /-->
      <el-input v-model="listQuery.symbol" :placeholder="$t('reckbackInfo.label4')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.reckbackOrderId" :placeholder="$t('reckbackInfo.label5')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.reckbackTradeId" :placeholder="$t('reckbackInfo.label6')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-date-picker v-model="valueGmtCreate" class="filter-item" type="daterange" align="right" unlink-panels range-separator="~" :picker-options="pickerOptions" style="width: 320px" />

      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button v-waves class="filter-item" type="primary" @click="handleExport">导出报表</el-button>

      <el-tag v-if="(this.fyzj!=undefined)?true:false">返佣金额{{ this.fyzj }}</el-tag>&nbsp;&nbsp;
      <el-tag v-if="(this.fyss!=undefined)?true:false">返佣手数{{ this.fyss }}</el-tag>
      
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label1')" prop="crmOrderId" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmOrderId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label2')" prop="crmTradeId" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmTradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label3')" prop="tradeQty" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeQty }}</span>
        </template>
      </el-table-column>
      <!--el-table-column label="用户交易组" prop="groupName"  min-width="100px"  align="center">
				<template slot-scope="scope">
					<span>{{ scope.row.groupName }}</span>
				</template>
			 </el-table-column-->
      <el-table-column :label="$t('reckbackInfo.label4')" prop="symbol" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.symbol }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label5')" prop="reckbackOrderId" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reckbackOrderId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label6')" prop="reckbackTradeId" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reckbackTradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label7')" prop="reckbackStatus" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reckbackStatus==1?'Success':'' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label8')" prop="reckbackType" min-width="100px" align="center" :formatter="fmtType" />
      <el-table-column :label="$t('reckbackInfo.label9')" prop="reckbackAmount" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.reckbackAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label10')" prop="reckbackAmount" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('reckbackInfo.label11')" prop="reckbackAmount" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtModified | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchReckbackInfo, createReckbackInfo, updateReckbackInfo, updateIsAvailable, removeReckbackInfo, exportReckbackInfoExcel } from '@/api/reckbackInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'
export default {
  name: 'ReckbackInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        crmUserId: undefined,
        crmOrderId: undefined,
        crmTradeId: undefined,
        groupName: undefined,
        symbol: undefined,
        reckbackUserId: undefined,
        reckbackOrderId: undefined,
        reckbackTradeId: undefined,
        reckbackStatus: undefined,
        reckbackType: undefined,
        gmtCreateSearchBegin: undefined,
        gmtCreateSearchEnd: undefined
      }, pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }

        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
					 }]				},
      temp: {
        id: undefined,
        userId: '',
        crmUserId: '',
        crmOrderId: '',
        crmTradeId: '',
        tradeQty: '',
        groupName: '',
        symbol: '',
        reckbackUserId: '',
        reckbackOrderId: '',
        reckbackTradeId: '',
        reckbackStatus: '',
        reckbackType: '',
        reckbackAmount: ''

      },
		  fyzj: undefined,
		  fyss: undefined,
      dqryType: undefined,
      valueGmtCreate: undefined,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        crmUserId: [
        ],
        crmOrderId: [
        ],
        crmTradeId: [
        ],
        tradeQty: [
        ],
        groupName: [
        ],
        symbol: [
        ],
        reckbackUserId: [
        ],
        reckbackOrderId: [
        ],
        reckbackTradeId: [
        ],
        reckbackStatus: [
        ],
        reckbackType: [
        ],
        reckbackAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        if (response.data.fyzj != null) {
          this.fyzj = response.data.fyzj
        }
        if (response.data.fyss != null) {
          this.fyss = response.data.fyss
        }
        this.listLoading = false
      })
    },
    handleFilter() {
      if (this.valueGmtCreate) {
          	this.listQuery.gmtCreateSearchBegin = parseTime(new Date(this.valueGmtCreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
          	this.listQuery.gmtCreateSearchEnd = parseTime(new Date(this.valueGmtCreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
          	this.listQuery.gmtCreateSearchBegin = undefined
          	this.listQuery.gmtCreateSearchEnd = undefined
      }

      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        crmUserId: '',
        crmOrderId: '',
        crmTradeId: '',
        tradeQty: '',
        groupName: '',
        symbol: '',
        reckbackUserId: '',
        reckbackOrderId: '',
        reckbackTradeId: '',
        reckbackStatus: '',
        reckbackType: '',
        reckbackAmount: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    }, handleExport() {
					 exportReckbackInfoExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createReckbackInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateReckbackInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 }, fmtType(row, column) {
      if (row.reckbackType == 1) {
           	return 'Rebate 1'
      } else if (row.reckbackType == 2) {
           	return 'Rebate 2'
      } else if (row.reckbackType == 3) {
           	return 'Rebate 3'
      } else if (row.reckbackType == 6) {
           	return 'Rebate sale'
      } else if (row.reckbackType == 7) {
           	return 'Rebate manager'
      } else if (row.reckbackType == 8) {
           	return 'Rebate commissioner'
      } else if (row.reckbackType == 104) {
           	return 'Rebate 4'
      } else if (row.reckbackType == 105) {
           	return 'Rebate 5'
      } else if (row.reckbackType == 106) {
           	return 'Rebate 6'
      } else if (row.reckbackType == 107) {
           	return 'Rebate 7'
      } else if (row.reckbackType == 108) {
           	return 'Rebate 8'
      } else if (row.reckbackType == 109) {
           	return 'Rebate 9'
      } else if (row.reckbackType == 110) {
           	return 'Rebate 10'
      } else if (row.reckbackType == 111) {
           	return 'Rebate 11'
      } else if (row.reckbackType == 112) {
           	return 'Rebate 12'
      } else if (row.reckbackType == 113) {
           	return 'Rebate 13'
      } else if (row.reckbackType == 114) {
           	return 'Rebate 14'
      } else if (row.reckbackType == 115) {
           	return 'Rebate 15'
      } else if (row.reckbackType == 116) {
           	return 'Rebate 16'
      } else if (row.reckbackType == 117) {
           	return 'Rebate 17'
      } else if (row.reckbackType == 118) {
           	return 'Rebate 18'
      } else if (row.reckbackType == 119) {
           	return 'Rebate 19'
      } else if (row.reckbackType == 120) {
           	return 'Rebate 20'
      } else if (row.reckbackType == 121) {
           	return 'Rebate 21'
      } else if (row.reckbackType == 122) {
           	return 'Rebate 22'
      } else if (row.reckbackType == 123) {
           	return 'Rebate 23'
      } else if (row.reckbackType == 124) {
           	return 'Rebate 24'
      } else if (row.reckbackType == 125) {
           	return 'Rebate 25'
      } else if (row.reckbackType == 126) {
           	return 'Rebate 26'
      } else if (row.reckbackType == 127) {
           	return 'Rebate 27'
      } else if (row.reckbackType == 128) {
           	return 'Rebate 28'
      } else if (row.reckbackType == 129) {
           	return 'Rebate 29'
      } else if (row.reckbackType == 130) {
           	return 'Rebate 30'
      }
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeReckbackInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

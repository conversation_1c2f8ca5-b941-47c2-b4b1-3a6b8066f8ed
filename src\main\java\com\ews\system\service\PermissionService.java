package com.ews.system.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ews.system.entity.Permission;


@Component
public interface PermissionService
{
	/**
	 * 查询所有权限
	 * @return
	 */
	public List<Map> getAllPermissions(Long roleId);
	
	/**
	 * 通过用户查询权限
	 * @param roleId
	 * @return
	 */
	public List<Permission> getPermissions(Long roleId);
	
	
	/**
	 * 查询单一permission
	 * @param permissionId
	 * @return
	 */
	public Permission findByPermissinId(Long permissionId);
	
	
	/**
	 * 查询所有的权限
	 * @return
	 */
	public List<Permission> findAll();
	
}

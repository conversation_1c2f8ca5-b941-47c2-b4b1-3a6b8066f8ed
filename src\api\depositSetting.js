import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/depositSetting/list',
    method: 'post',
    params: query
  })
}

export function fetchDepositSetting(id) {
  return request({
    url: '/depositSetting/detail',
    method: 'get',
    params: { id }
  })
}

export function createDepositSetting(data) {
  return request({
    url: '/depositSetting/add',
    method: 'post',
    data
  })
}

export function updateDepositSetting(data) {
  return request({
    url: '/depositSetting/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/depositSetting/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeDepositSetting(id) {
  return request({
    url: '/depositSetting/remove',
    method: 'get',
    params: { id }
  })
}


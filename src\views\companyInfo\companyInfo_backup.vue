<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.companyName" placeholder="公司名称" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" prop="companyName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="系统名称" prop="systemName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.systemName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="temp.companyName" />
        </el-form-item>
        <el-form-item label="公司logo" prop="companyLogo">
          <el-input v-model="temp.companyLogo" />
        </el-form-item>
        <el-form-item label="登陆页logo" prop="loginLogo">
          <el-input v-model="temp.loginLogo" />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="temp.systemName" />
        </el-form-item>
        <el-form-item label="官网地址" prop="webUrl">
          <el-input v-model="temp.webUrl" />
        </el-form-item>
        <el-form-item label="社区地址" prop="communityUrl">
          <el-input v-model="temp.communityUrl" />
        </el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="temp.email" />
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="temp.telephone" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="temp.companyName" />
        </el-form-item>
        <el-form-item label="公司logo" prop="companyLogo">
          <el-input v-model="temp.companyLogo" />
        </el-form-item>
        <el-form-item label="登陆页logo" prop="loginLogo">
          <el-input v-model="temp.loginLogo" />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="temp.systemName" />
        </el-form-item>
        <el-form-item label="官网地址" prop="webUrl">
          <el-input v-model="temp.webUrl" />
        </el-form-item>
        <el-form-item label="社区地址" prop="communityUrl">
          <el-input v-model="temp.communityUrl" />
        </el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="temp.email" />
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="temp.telephone" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchCompanyInfo, createCompanyInfo, updateCompanyInfo, updateIsAvailable, removeCompanyInfo } from '@/api/companyInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'CompanyInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        companyName: undefined
      },
      temp: {
        id: undefined,
        companyName: '',
        companyLogo: '',
        loginLogo: '',
        systemName: '',
        webUrl: '',
        communityUrl: '',
        email: '',
        telephone: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        companyName: [
          { required: true, message: '公司名称不能为空', trigger: 'change' },,
        ],
        companyLogo: [
          { required: true, message: '公司logo不能为空', trigger: 'change' },,
        ],
        loginLogo: [
          { required: true, message: '登陆页logo不能为空', trigger: 'change' },,
        ],
        systemName: [
          { required: true, message: '系统名称不能为空', trigger: 'change' },,
        ],
        webUrl: [
        ],
        communityUrl: [
          { required: true, message: '社区地址不能为空', trigger: 'change' },,
        ],
        email: [
        ],
        telephone: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        companyName: '',
        companyLogo: '',
        loginLogo: '',
        systemName: '',
        webUrl: '',
        communityUrl: '',
        email: '',
        telephone: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createCompanyInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateCompanyInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeCompanyInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

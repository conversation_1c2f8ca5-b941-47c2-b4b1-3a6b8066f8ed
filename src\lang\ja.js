export default {
  route: {
    dashboard: 'タスクの概要',
    welcome: 'ワークステーション',
    documentation: 'ドキュメント',
    guide: 'ガイド',
    permission: 'システム設定',
    rolePermission: '役割管理',
    userPermission: '管理者/エージェント',
    companyInfo: '会社情報',
    exchangeRate: '為替レート設定',
    lever: 'レバレッジ設定',
    webContent: 'システム通知',
    emailInfo: 'Eメール設定',
    accountType: 'アカウントタイプ',
    serverSetting: 'サーバー設定',
    tradeProd: '商品設定',
    operLog: '操作ログ',
    userTag: 'ユーザータグ',
    tradeSetting: '取引設定',
    userGroup: 'ユーザー・グループ',
    rekebackRule: '手数料規定',
    addRekebackRule: 'コミッションルールの追加',
    editRekebackRule: 'コミッションルールの編集',
    copyRekebackRule: 'コミッションルールの複製',
    deposit: '入金チャンネル',
    withdrawal: '出金チャネル',
    withdrawalcontrol: '危機管理',
    auditManager: '承認管理',
    auditUser: 'ユーザー承認',
    auditAccount: '取引口座の承認',
    depositAudit: '入金の承認',
    withdrawalAudit: '出金承認',
    withdrawalAudit2: '支払承認',
    withdrawalAudit3: '出金失敗',
    auditTransfer: '内部送金承認',
    userManager: 'ユーザー管理',
    userList: 'エージェントアカウント',
    agentAchievement: '運用資産',
    crmUserList: 'CRMユーザー',
    tradeUserList: '取引口座',
    financeManager: '財務ログとアラート',
    depositList: '入金ログ',
    withdrawalList: '出金ログ',
    transferList: '内部転送ログ',
    rekebackList: 'コミッションログ',
    crmUserListquery: 'CRM ユーザーの表示',
    tradeUserListquery: '取引口座を表示する',
    label1: 'CRM ユーザーの表示',
    label2: '取引口座を見る',
    label3: '入金待ち',
    label4: '出金保留中',
    label5: '入金承認',
    label6: '支払いの失敗'

  },
  navbar: {
    logOut: 'ログアウト',
	 userInfo: 'ユーザー情報',
	 changePass: 'パスワードの変更',
    theme: 'テーマ',
    size: 'サイズ設定',
    userName: 'ユーザー名',
    password: 'パスワード',
    initialPassword: '初期パスワード',
    newPassword: '新しいパスワード',
    againPassword: '新しいパスワードの再入力',
    nickName: '名前',
    storeName: '代理店名',
		  depaName: '部署名',
    roleName: '役割',
    mobile: '携帯電話番号',
		 edit: '編集',
    invitationLink: '招待リンク',
    qrcode: '招待QRコード'
  },
  dashboard: {
    title1: '承認待ち',
    title2: 'ユーザーの承認待ち',
    title3: 'デポジット保留中',
    title4: '出金保留中',
    title5: '取引口座未決済',
    title6: '内部転送保留中'

  },
  login: {
    title: 'バックオフィス',
    logIn: 'ログイン',
    username: 'ユーザー名',
    password: 'パスワード',
    verifycode: '検証コード',
    refreshcode: '検証コードを更新する',
    prompt1: 'ユーザー名を入力してください',
    prompt2: 'パスワードを入力してください'

  },
  role: {
    roleName: 'ロール',
    status: '状態',
    roleDescription: '役割の説明',
    oper: 'アクション',
    permission: '許可',
    allSelect: 'すべて選択'
  },
  documentation: {
    documentation: 'ドキュメンテーション',
    github: 'Github リンク'
  },
  permission: {
    addRole: '追加',
    editPermission: '編集',
    'Delete': '消去',
    enable: '有効にする',
    disable: '無効',
    confirm: '確認する',
    cancel: 'キャンセル'
  },
  guide: {
  },
  components: {
    documentation: 'ドキュメンテーション'
  },
  table: {
    dynamicTips1: 'タイトル固定、タイトル順にソート',
    dynamicTips2: 'タイトル固定なし、クリック順',
    dragTips1: 'デフォルトの並び順',
    dragTips2: '配置後の順序',
    title: 'タイトル',
    importance: '重要度',
    type: 'タイプ',
    remark: '備考',
    search: '検索',
    add: '追加',
    'export': 'エクスポート',
    reviewer: '承認者',
    id: '番号',
    date: '時間',
    author: '著者',
    readings: 'ビュー',
    status: 'ステータス',
    actions: 'アクション',
    edit: '編集',
    publish: '発行',
    draft: '下書き',
    'delete': '削除',
    cancel: 'キャンセル',
    confirm: '確認',
    save: '保存'
  },
  userTable: {
    userName: 'ユーザー名',
    password: 'パスワード',
    initialPassword: '初期パスワード',
    newPassword: '新しいパスワード',
    resetPassword: 'パスワードのリセット',
    nickName: '名前',
    storeName: '代理店名',
    depaName: '部署名',
    roleName: '役割',
    permission: '許可',
    saleType: 'エージェントタイプ',
    saleMgn: 'セールスマネージャー',
    saleSerial: 'エージェントID',
    mobile: '連絡先電話番号',
    search: '検索',
    add: '追加',
    enable: '有効',
    disable: '無効',
    doEnable: '有効',
    doDisable: '無効',
    'export': 'エクスポート',
    id: '番号',
    createDate: '作成日',
    author: '作成者',
    status: 'ステータス',
    actions: 'アクション',
    edit: '編集',
    'delete': '削除',
    cancel: 'キャンセル',
    confirm: '確認',
    superiorAgent: 'スーパーバイザー',
    zhi: 'に',
    beginDate: '開始日',
    endDate: '終了日',
    number: '番号',
    userType: '役割',
    superAgent: '監督者',
    button1: 'サブエージェントの作成',
    button2: 'CRMユーザーの割り当て',
    crmUserEmail: 'CRMユーザーのメールアドレス',
    placeholder1: 'CRMユーザーのEメールアドレスを入力してください',
    label1: 'コミッションアカウント',
    placeholder2: 'コミッションアカウント番号を入力してください',
    label2: 'コミッションタイプ',
    placeholder3: '選択してください',
    placeholder4: '監督者名を入力してください',
    label3: 'スーパーバイザー名'

  },
  userType: {
    administrator: '管理者',
    agentIB: 'エージェント/IB',
    sales: '営業',
    salesManager: '営業マネージャー',
    salesDirector: '営業ディレクター',
    superAdministrator: 'スーパー管理者'
  },
  companyInfo: {
    label1: 'CRM名',
    label2: 'ウェルカムメッセージ',
    label3: '会社名',
    label4: '会社のロゴ',
    label5: 'ログインページバナー',
    label6: '公式ウェブサイトリンク',
    label7: 'コミュニティリンク',
    label8: 'メールアドレス',
    label9: '連絡先電話番号',
    label10: '詳細情報',
    button1: '更新'

  },
  webContent: {
    placeholder1: 'コンテンツタイトル',
    label1: 'コンテンツタイトル',
    label2: 'コンテンツの種類',
    label3: '公開日',
    label4: 'コンテンツ'
  },
  userTag: {
    placeholder1: 'タグ',
    label1: 'タグの説明'
  },
  operLog: {
    placeholder1: 'によって行われ',
    placeholder2: 'アクションのタイプ',
    placeholder3: 'アクション',
    placeholder4: 'ログの詳細',
    placeholder5: '時間',
    busType1: 'エージェント/IB/管理者',
    busType2: 'CRMユーザー',
    busType3: '入金',
    busType4: '出金',
    busType5: '同名口座',
    busType6: '同名振込',
    busType7: 'ユーザーアカウント',
    operType1: '追加',
    operType2: '編集',
    operType3: '削除',
    operType4: '承認'
  },
  lever: {
    label1: 'レバレッジ比率',
    label2: '備考'

  },
  tradeProd: {
    label1: '商品名',
    label2: '説明',
    button1: '製品を同期する'

  },
  userGroup: {
    label1: 'MT5グループ名',
    label2: '通貨タイプ',
    label3: '説明',
    button1: '同期グループ'

  },
  accountType: {
    label1: 'アカウントタイプ',
    label2: 'MT5グループ',
    label3: 'CRMに表示',
    label4: 'クレジットタイプ',
    label5: 'クレジット額',
    label6: 'MT5番号から',
    label7: 'MT5番号まで',
    label8: 'レバレッジを選択',
    label9: '自動転送承認',
    label10: '自動アカウント承認',
    show: '表示',
    hidden: '非表示',
    scale: 'スケール',
    regular: '定期的',
    selectAll: 'すべて選択',
    dialogTitleAdd: 'アカウントタイプの追加',
    dialogTitleEdit: 'アカウントタイプの編集',
    typeNameEmpty: 'タイプ名を入力してください',
    groupNameEmpty: 'MTグループ名を入力してください',
    isShowEmpty: 'フォアグラウンド表示を入力してください',
    openTypeEmpty: 'クレジットタイプを入力してください',
    creditValueEmpty: 'クレジット値を入力してください',
    autoAudit: '自動監査',
    manualReview: '手動レビュー',
    maxAccounts: '最大アカウント数',
    operSuccess: '操作が成功しました',
    deleteConfirm: 'このデータを削除してもよろしいですか？',
    deleteSuccess: '削除が成功しました'
  },
  rekebackRule: {
    label1: 'ルール名',
    label2: '作成日',
    label3: 'ディレクターコミッション',
    label4: 'マネージャーコミッション',
    label5: 'セールスリベート',
    label6: '1 階層上のコミッション',
    label7: '2 階層上のコミッション',
    label8: '3 階層上のコミッション',
    label9: 'コミッション総額',
    label10: 'コピー',
    label11: '外部手数料ティア',
    label12: '4 ティア以上のコミッション',
    label13: '5 ティア以上のコミッション',
    label14: '6 ティア以上のコミッション',
    label15: '7 ティア以上のコミッション',
    label16: '8 ティア以上のコミッション',
    label17: '9 ティア以上のコミッション',
    label18: '10 ティア以上のコミッション',
    label19: '11 ティア以上のコミッション',
    label20: '12 ティア以上のコミッション',
    label21: '13 ティア以上のコミッション',
    label22: '14 ティア以上のコミッション',
    label23: '15 ティア以上のコミッション',
    label24: '16 ティア以上のコミッション',
    label25: '17 ティア以上のコミッション',
    label26: '18 ティア以上のコミッション',
    label27: '19 ティア以上のコミッション',
    label28: '20 ティア以上のコミッション',
    label29: '21 ティア以上のコミッション',
    label30: '22 ティア以上のコミッション',
    label31: '23 ティア以上のコミッション',
    label32: '24 ティア以上のコミッション',
    label33: '25 ティア以上のコミッション',
    label34: '26 ティア以上のコミッション',
    label35: '27 ティア以上のコミッション',
    label36: '28 ティア以上のコミッション',
    label37: '29 ティア以上のコミッション',
    label38: '30 ティア以上のコミッション',
    label39: '関連商品',
    label40: '関連グループ',
    label41: '備考',
    level3: '3層',
    level4: '4層',
    level5: '5層',
    level6: '6層',
    level7: '7層',
    level8: '8層',
    level9: '9層',
    level10: '10層',
    level11: '11層',
    level12: '12層',
    level13: '13層',
    level14: '14層',
    level15: '15層',
    level16: '16層',
    level17: '17層',
    level18: '18層',
    level19: '19層',
    level20: '20層',
    level21: '21層',
    level22: '22層',
    level23: '23層',
    level24: '24層',
    level25: '25層',
    level26: '26層',
    level27: '27層',
    level28: '28層',
    level29: '29層',
    level30: '30層'
  },
  depositBank: {
    label1: '入金方法',
    label2: 'チャンネルタイプ',
    label3: '支払いリンク',
    label4: '通貨',
    label5: '銀行名',
    label6: '銀行住所',
    label7: '口座番号',
    label8: '受取人',
    label9: 'デジタルウォレットのアドレス',
    label10: 'マークアップ (%)',
    label11: '最低入金額',
    label12: '最大入金額',
    label13: 'KYCステータス',
    label14: '許可された国',
    label15: '口座から隠す',
    label16: 'タグから隠す',
    label17: 'ワイトリストに登録された IP'
  },
  withdrawalSetting: {
    label1: '許可された国',
    label2: '口座から隠す',
    label3: 'タグから隠す',
    label4: '最大出金額',
    label5: '最低トレーダー自己資金',
    label6: '無料現金マージンの最小%'

  },
  withdrawalBank: {
    label1: '出金方法',
    label2: '出金リンク',
    label3: '優先順位',
    label4: '通貨',
    label5: 'マークダウン(%)',
    label6: '最低出金額',
    label7: '最大出金額',
    label8: '許可される国',
    label9: 'アカウントの種類から非表示にする',
    label10: 'タグから隠す',
    label11: 'ワイトリストに登録された IP'

  },
  userInfoAudit: {
    label1: 'ユーザー名',
    label2: '名前',
    label3: '携帯番号',
    label4: '国籍',
    label5: '自宅の住所',
    label6: 'ID番号',
    label7: '写真',
    label8: '生年月日',
    label9: '居住国',
    label10: '市',
    label11: '携帯番号',
    label12: '住所',
    label13: '申請は拒否されました',
    label14: '却下の理由',
    label15: '承認',
    label16: '却下',
    label17: '登録時期',
    label18: 'レビュー',
    label19: 'キャンセル'

  },
  tradeAccountAudit: {
    label1: '申請時間',
    label2: '名前',
    label3: 'メールアドレス',
    label4: '携帯番号',
    label5: '国',
    label6: '取引グループ',
    label7: 'てこの作用',
    label8: '口座タイプ',
    label9: '承認',
    label10: '拒否'

  }, depositAudit: {
    label1: 'CRMユーザー',
    label2: '取引口座',
    label3: '入金チャンネルの詳細',
    label4: '入金額',
    label5: 'デジタルウォレットアドレス',
    label6: 'アタッチメント',
    label7: '備考',
    label8: '入金額',
    label9: '備考確認',
    label10: '承認',
    label11: '却下',
    label12: '申請時間'
  }, withdrawalAudit: {
    label1: 'CRMユーザー',
    label2: '取引口座',
    label3: '方法',
    label4: '出金額',
    label5: '通貨',
    label6: 'ユーザーが受け取った金額',
    label7: '支払口座',
    label8: '承認備考',
    label9: '現在の残高',
    label10: '承認',
    label11: '却下',
    label12: '申請時間',
    label13: '備考',
    label14: '支払口座'
  }, transferInfoAudit: {
    label1: '申請時間',
    label2: '転送元',
    label3: '転送先',
    label4: '名前',
    label5: 'メールアドレス',
    label6: '時間',
    label7: '金額',
    label8: '承認',
    label9: '拒否'
  }, user2: {
    label1: '上司アカウントID',
    label2: 'コミッション口座',
    label3: '直属の上司',
    label4: '招待コード'
  },
  user3: {
    label1: 'エージェント数',
    label2: 'CRMユーザー数',
    label3: '取引口座数',
    label4: '取引口座の現金残高',
    label5: '総入金額',
    label6: '出金総額'

  }, userInfo: {
    label1: 'メールアドレス',
    label2: '名前',
    label3: '携帯番号',
    label4: '国籍',
    label5: '住所',
    label6: 'ID番号',
    label7: '写真',
    label8: '生年月日',
    label9: '居住国',
    label10: '市',
    label11: '携帯番号',
    label12: '住所',
    label13: 'アカウントマネージャーのID',
    label14: 'アカウントタグ',
    label15: '表示',
    label16: '編集',
    label17: '作成日',
    validation: {
      surnameRequired: '姓は必須です',
      nameRequired: '名前は必須です',
      userNameRequired: 'メールアドレスは必須です',
      passwordRequired: 'パスワードは必須です',
      fileSizeLimit: '画像サイズは2MBを超えることはできません！',
      countryRequired: '居住国は必須です',
      provinceRequired: '国籍は必須です'
    }
  }, tradeAccount: {
    label1: '取引口座',
    label2: '口座の種類',
    label3: '作成日',
    label4: 'リンクされた CRM ユーザー',
    label5: 'メールアドレス',
    label6: 'レバレッジ',
    label7: '残高',
    label8: 'クレジット',
    label9: '合計証拠金',
    label10: '取引高(過去30日間)',
    label11: 'フリーマジン',
    label12: '使用証拠金',
    label13: '最初のパスワード',
    label14: 'リンクされた CRM ユーザー',
    label15: 'てこの作用',
    label16: 'トレーディンググループ',
    label17: '取引口座の追加',
    label18: '編集'
  }, depositList: {
    label1: '提出日',
    label2: '処理日',
    label3: 'メールアドレス',
    label4: 'MT5 アカウント',
    label5: '入金額',
    label6: '承認状況',
    label7: 'ステータス',
    label8: '備考',
    label9: 'チケット番号'
  }, withdrawalList: {
    label1: '出金日',
    label2: '処理日',
    label3: 'メールアドレス',
    label4: 'MT5 アカウント',
    label5: '出金額',
    label6: 'ステータス',
    label7: 'リクエストステータス',
    label8: '述べる',
    label9: 'チケット番号',
    label10: 'リスク管理',
    label11: '財務承認',
    label12: '支払状況',
    label13: 'MT5控除ステータス'

  }, transferInfo: {
    label1: '提出日',
    label2: 'From',
    label3: '宛先',
    label4: '名前',
    label5: 'メールアドレス',
    label6: '振込時間',
    label7: '金額',
    label8: '処理時間',
    label9: '備考'
  }, reckbackInfo: {
    label1: '取引番号',
    label2: 'MT5 アカウント',
    label3: 'ロットサイズ',
    label4: '製品の種類',
    label5: '販売数',
    label6: 'コミッションアカウント',
    label7: 'ステータス',
    label8: 'リベートタイプ',
    label9: '金額',
    label10: '提出日',
    label11: '支払日'

  },
  example: {
  },
  errorLog: {
  },
  excel: {
    'export': '輸出',
    selectedExport: '選択したものをエクスポート'
  },
  zip: {
    'export': '輸出'
  },
  pdf: {
  },
  theme: {
    change: 'テーマ'
  },
  tagsView: {
    refresh: 'リフレッシュ',
    close: '閉じる',
    closeOthers: 'その他を閉じる',
    closeAll: 'すべて閉じる'
  },
  settings: {
    title: 'システム設定',
    theme: 'テーマカラー',
    tagsView: 'タグビューを開く',
    fixedHeader: '固定ヘッダー',
    sidebarLogo: 'サイドバーロゴ'
  },
  qt: {
    qt1: '再試行',
    qt2: 'キャンセル'
  },
  サプリメント: {
    label1: 'CRM ユーザーの表示',
    label2: 'MT5アカウントを見る',
    label3: '承認待ちの入金',
    label4: '出金は承認待ちです',
    label5: '支払いのレビュー',
    label6: '支払いに失敗しました',
    label7: '最大口座数'

  }
}

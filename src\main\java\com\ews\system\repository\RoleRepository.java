package com.ews.system.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import com.ews.system.entity.Role;
import com.ews.system.model.IRolePermission;

public interface RoleRepository extends  JpaRepository<Role, Long>,JpaSpecificationExecutor<Role> {
	Page<Role> findAllByRoleSignContains(String roleSign, Pageable pageable);
    Role findRoleByRoleSign(String roleSign);
    
    Role findRoleByRoleId(Long roleId);

    //排除现有角色的情况下，判断新的角色是否存在
    @Query(value="select * from sys_role where role_id <> ?1 and role_sign = ?2",nativeQuery = true)
    Role findRoleExistsByIdAndRoleSign(Long roleId, String roleSign);
    
    //排除现有角色的情况下，判断新的角色是否存在
    @Query(value="select * from sys_role where role_id <> ?1 and role_name = ?2",nativeQuery = true)
    Role findRoleExistsByIdAndRoleName(Long roleId, String roleName);

    //根据roleid列表删除所有的角色
    @Transactional
    @Modifying
    @Query(value = "delete from sys_role where role_id in (?1)",nativeQuery = true)
    void deleteAllByRoleIdList(List<Long> roleIdList);

    //根据roleid列出所有权限，包括没有的权限，如果没有权限，则roleid和role两个字段会是null值，可以根据此做业务判断
    @Query(value="select a.permission_id as permissionId,a.permission_sign as permission,a.permission_name as permissionName,c.role_id as roleId,c.role_name as role  from sys_permission a \n" +
            "left join sys_role_permission b on a.permission_id=b.permission_id  and b.role_id=?1\n" +
            "left join sys_role c on c.role_id=b.role_id",
            
    countQuery = "select count(*)  from sys_permission a \n" +
            "left join sys_role_permission b on a.permission_id=b.permission_id  and b.role_id=?1\n" +
            "left join sys_role c on c.role_id=b.role_id",
    nativeQuery = true)
    List<IRolePermission> findRolePermissionByRoleId(Long roleId);

    //根据roleid删除角色权限关联表里所有角色权限
    @Transactional
    @Modifying
    @Query(value = "delete from sys_role_permission where role_id=?1",nativeQuery = true)
    void deleteRolePermission(Long roleId);

    //插入角色和权限
    @Transactional
    @Modifying
    @Query(value="insert into sys_role_permission(role_id,permission_id) VALUES(?1,?2) ",nativeQuery = true)
    void insertRolePermission(Long roleId, Long permissionId);
    
    //更新可用状态
    @Transactional
    @Modifying
    @Query(value = "update sys_role set is_available=?2 where role_id=?1",nativeQuery = true)
    int updateIsAvailableById(Long roleId,Integer isAvailable);
    
    
	
}

package com.ews.system.service;

import java.util.List;

import org.springframework.stereotype.Component;

import com.ews.system.entity.RolePermission;


@Component
public interface RolePermissionService
{
	/**
	 * 通过角色查询和权限的关联信息
	 * @param roleId
	 * @return
	 */
	List<RolePermission> findByRoleId(Long roleId);
	
	/**
	 * 通过角色和权限查询关联信息
	 * @param roleId
	 * @param permissionId
	 * @return
	 */
	List<RolePermission> findByRoleIdAndPermissionId(Long roleId,Long permissionId);
	
	
	/**
	 * 通过角色id 删除角色和权限关联管理
	 * @param roleId
	 */
	void deleteByRoleId(Long roleId);
}

package com.ews.common;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class SaticScheduleTask3 {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private UserService userService;
	@Autowired
	private ReckbackInfoService reckbackInfoService;
	//是否开启自动轮询功能 true 开启   false  关闭
	/*目前只用于生成返佣和返佣操作，出入金 */
	private boolean bl1=false;   //余额，
	private boolean bl8=false;   //预付款
	private boolean bl2=true;    //查询已生成的历史订单生成返佣
	private boolean bl3=true;   //返佣操作
	private boolean bl4=false;   //查询持仓
	private boolean bl5=false;   //查询历史订单
	private boolean bl6=true;   //出金
	private boolean bl7=true;   //入金
	
	//备用  
	private boolean bl9=false;   //补充开通交易账号
	private boolean bl13=false;   //补发佣金
	
	
	
	private boolean mainSwitch=true;    //总开关
	
   // @Scheduled(fixedRate=60000)
    @Scheduled(fixedRate=3600000L)//每小时查询一次
    private void configureTasks() {
    	if(bl1&&mainSwitch) {
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
		MyWebsocketClient4Balance client= new MyWebsocketClient4Balance(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
		client.setTradeAccountService(this.tradeAccountService);
		    client.connect();
			Thread.sleep(300);
			if (client.getReadyState().equals(READYSTATE.OPEN)) {
				System.out.println("getBalance   "+new Date()+" "+client.getReadyState());
				this.getBalance(client,ss.getBackup1());
			 }
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
    
   // @Scheduled(fixedRate=600000,initialDelay=20000)
    @Scheduled(fixedRate=3600000L,initialDelay=20000)//每小时查询一次
    private void configureTasks1() {
    	if(bl8&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,100000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
		MyWebsocketClient4Balance2 client2= new MyWebsocketClient4Balance2(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
		        client2.setTradeAccountService(this.tradeAccountService);
		    	client2.connect();
		    	Thread.sleep(300);
				if (client2.getReadyState().equals(READYSTATE.OPEN)) {
					 System.out.println("getBalance2   "+new Date()+" "+client2.getReadyState());
					 java.util.List list2=new ArrayList();
						for(int i=0;i<pages2.getContent().size();i++) {
				    		TradeAccount entity  = pages2.getContent().get(i);
				    		HashMap mm1=new HashMap();
				    		mm1.put("login", new Integer(entity.getTradeId()).intValue());
				    		list2.add(mm1);
				    		if((i+1)%80==0) {
				    			this.getBalance2(list2,client2);
				    			Thread.sleep(50L);
				    			list2=new ArrayList();
				    		}
				     }
						this.getBalance2(list2,client2);
		    	}
				
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
    
    //查询余额、信用、净值
    public void getBalance(MyWebsocketClient4Balance client,String roleUser) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "getuserlistinfo");
			map.put("reqid", new Date().getTime());
			map.put("login", new Integer(roleUser).intValue());
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
	}
    //可用预付款、已用预付款
     public void getBalance2(java.util.List l,MyWebsocketClient4Balance2 client) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "marginleveluserinfo");
			map.put("reqid", new Date().getTime());
			map.put("loginItem", l);
			map.put("count", l.size());
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
	}
     
     //查询持仓   每4分钟查询一次，延迟2分钟
    // @Scheduled(fixedRate=240000,initialDelay=129000)
     @Scheduled(fixedRate=3600000L,initialDelay=329000)//每小时查询一次
    private void configureTasks2() {
    	if(bl4&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,5000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
				MyWebsocketClient4Hold client= new MyWebsocketClient4Hold(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
				client.setTradeAccountService(this.tradeAccountService);
				client.setOrderInfoService(this.orderInfoService);
				  client.connect();
				  Thread.sleep(300);
					if (client.getReadyState().equals(READYSTATE.OPEN)) {
						System.out.println("queryHold   "+new Date()+" "+client.getReadyState());
						for(int i=0;i<pages2.getContent().size();i++) {
				    		TradeAccount entity  = pages2.getContent().get(i);
				    		this.queryHold(entity,client);
				    	}
					 }
		    	
			}catch(Exception e) {
				System.out.println(e);
						
			}
    	}
    }
    
    public void queryHold(TradeAccount tradeAccount,MyWebsocketClient4Hold client) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "ordersuserinfo");
			map.put("reqid", new Date().getTime());
			map.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
				
	}
   
    //查询历史   每10分钟查询一次，延迟4分钟
    // @Scheduled(fixedRate=600000,initialDelay=245000)
    @Scheduled(fixedRate=3600000L,initialDelay=645000)  //每小时查询一次
    private void configureTasks3() {
    	if(bl5&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,5000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    	try {
    	MyWebsocketClient4History client= new MyWebsocketClient4History(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
  		client.setTradeAccountService(this.tradeAccountService);
  		client.setOrderInfoService(this.orderInfoService);
  		client.connect();
  		Thread.sleep(300);
		if (client.getReadyState().equals(READYSTATE.OPEN)) {
			System.out.println("queryHistory   "+new Date()+" "+client.getReadyState());
			for(int i=0;i<pages2.getContent().size();i++) {
	    		TradeAccount entity  = pages2.getContent().get(i);
	    		this.queryHistory(entity,client);
	    	}
		 }
  		}catch(Exception e) {
  			System.out.println(e);
  		}
    	}
    }
    
    public void queryHistory(TradeAccount tradeAccount,MyWebsocketClient4History client) {
  		ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
  		try {
  		
  		    HashMap map=new HashMap();
  		    map.put("reqtype", "historyorderinfo");
  			map.put("reqid", new Date().getTime());
  			map.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
  			OrderInfo order_query=new OrderInfo();
			order_query.setLoginId(String.valueOf(tradeAccount.getTradeId()));
			order_query.setStatus(2);
			List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	ll.add(6);
	    	order_query.setTypeList(ll);
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,1, "closeTime","desc", order_query);
			if(oi_page.getContent().size()>0) {//有记录
				map.put("fromtime", new Long((oi_page.getContent().get(0).getCloseTime())+1L));
				  //System.out.println("1:"+oi_page.getContent().get(0).getLoginId().toString()+" "+map.get("fromtime")+"   "+((new Date().getTime()/1000)+86400L)+"   ");
			}else {//没有交易信息
				map.put("fromtime", new Long((tradeAccount.getGmtCreate().getTime()/1000)-3700L));
				  //System.out.println("2:"+tradeAccount.getTradeId()+" "+map.get("fromtime")+"   "+((new Date().getTime()/1000)+86400L)+"   ");
			}
  			map.put("endtime", (new Date().getTime()/1000)+86400L);  //结束时间增加一个小时计算
  		    JSONObject jsonObj=new JSONObject(map);
  		   client.send(jsonObj.toString());
  		  
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    
   //查询历史订单进行返佣   每1分钟查询一次，延迟5分钟
    @Scheduled(fixedRate=60000,initialDelay=300000)
    private void configureTasks6() {
    	if(bl2&&mainSwitch) {
      
    		try {
      			OrderInfo order_query=new OrderInfo();
      			order_query.setStatus(2);
      			order_query.setIsRakeback(0);
      			List typeList=new ArrayList();
      			typeList.add(1);
      			typeList.add(0);
      			order_query.setTypeList(typeList);
      			Page<OrderInfo> order_page=this.orderInfoService.findAll(0,600, "id", "asc", order_query);
      			for(int i=0;i<order_page.getContent().size();i++) {
      				OrderInfo orderInfo=(OrderInfo)order_page.getContent().get(i);
      				if(orderInfo.getTradeId()!=null&&!orderInfo.getTradeId().toString().equals("")) {
      					TradeAccount ta=this.tradeAccountService.findById(orderInfo.getTradeId());
      					if(ta.getUserId()!=null) {
      		  				UserInfo userInfo=this.userInfoService.findById(ta.getUserId());
      		  				if(userInfo.getParentId()!=null) {
      		  				Long trade_id=orderInfo.getTradeId();//交易账号的CRM系统ID
      		  				String symbol=orderInfo.getSymbol();//交易品种
      		  				String loginID=orderInfo.getLoginId();//MT交易账号
      		  				Long crm_user_id=ta.getUserId();  //CRM用户ID
      		  				String group_name=ta.getGroupName();//用户组 
      		  				Long parent_id=userInfo.getParentId();// 代理用户ID
      		  				Double qty=orderInfo.getVolume();//手数
      		  				String order_id=orderInfo.getOrderNo();
      		  				//System.out.println(trade_id+" "+symbol+" "+loginID+" "+crm_user_id+" "+group_name+" "+parent_id);
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				//迭代进行记录待返佣记录
      		  				if(parent_id!=null) {
      		  				this.toDoRekeBack(order_id,trade_id, symbol, loginID, crm_user_id, group_name, parent_id, qty, null, null, null, null, null, null,1);
      		  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				}
      		  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				}
      	  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      	  				}
      				}else {
      					orderInfo.setIsRakeback(1);
    		  				this.orderInfoService.saveOrUpdate(orderInfo);
      				}
      			}
      		}catch(Exception e) {
      			System.out.println(e);
      		}
    	}
    	
    }
    public void toDoRekeBack(String order_id,Long trade_id,String symbol,String loginID,Long crm_user_id,String group_name,Long parent_id,Double qty,Double a1,Double a2,Double a3,Double b1,Double b2,Double b3,int level) {
  		try {
  			
  			//System.out.println("lsjl:"+order_id+" "+trade_id+" "+symbol+" "+loginID+" "+crm_user_id+" "+group_name+" "+parent_id+" "+qty+" "+level);
  			//System.out.println(parent_id);
  			Double aa1=a1;//总监
  			Double aa2=a2;//经理
  			Double aa3=a3;//销售
  			Double bb1=b1;//一级
  			Double bb2=b2;//二级
  			Double bb3=b3;//三级
  			
  			if(parent_id!=null) {
  			Long pparent_id=null;
  			User u=(User)this.userService.findUserById(parent_id);
				if(u.getReId()!=null&&u.getReId().intValue()!=1&&u.getReId().intValue()!=0) {
					pparent_id=u.getReId();
			}
  			
  			List rekebackList=this.dataSqlService.queryRekeBackAmount(parent_id, group_name, symbol);
  			
  			if(rekebackList.size()>0) {
  				
  				Object obj[]=(Object[])rekebackList.get(0);
  	  			
  				if(aa1==null) {
	  	  			if(obj[0]!=null) {
	  	  				aa1=new Double(obj[0].toString());
	  	  			}else {
	  	  			aa1=0D;
	  	  			}
  				}
  				if(aa2==null) {
  	  			if(obj[1]!=null) {
	  				aa2=new Double(obj[1].toString());
	  			}else {
	  	  			aa2=0D;
	  	  			}
  				}
  				if(aa3==null) {
		  	  	if(obj[2]!=null) {
						aa3=new Double(obj[2].toString());
					}else {
		  	  			aa3=0D;
	  	  			}
  				}
  				if(bb1==null) {
			  	  if(obj[3]!=null) {
			  		bb1=new Double(obj[3].toString());
						}else {
			  	  			bb1=0D;
		  	  			}
  				}
  				if(bb2==null) {
			  	if(obj[4]!=null) {
			  		bb2=new Double(obj[4].toString());
					}else {
		  	  			bb2=0D;
	  	  			}
  				}
  				if(bb3==null) {
			  	if(obj[5]!=null) {
			  		bb3=new Double(obj[5].toString());
					}else {
		  	  			bb3=0D;
	  	  			}
  				}
			  	    int reckback_type=0;
			  	    Double reckback_amount=0D;
			  	
			  	    if(u.getRoleType().intValue()==0) {//后台管理员
	  				}else if(u.getRoleType().intValue()==1) {//代理
	  					if(level==1) {//一级代理
	  						reckback_type=1;
	  						reckback_amount=bb1*qty;
	  	  	  			}else if(level==2) {//二级代理
	  	  	  			reckback_type=2;
	  	  	  		        reckback_amount=bb2*qty;
	  	  	  			}else if(level==3) {//三级代理
	  	  	  			reckback_type=3;
	  	  	  		        reckback_amount=bb3*qty;
	  	  	  			}
	  				}else {
	  					if(u.getRoleType().intValue()==2) {//销售
	  						reckback_type=6;
	  						reckback_amount=aa3*qty;
	  					}else if(u.getRoleType().intValue()==3) {//经理
	  						reckback_type=7;
	  						reckback_amount=aa2*qty;
	  					}else if(u.getRoleType().intValue()==4) {//总监
	  						reckback_type=8;
	  						reckback_amount=aa1*qty;
	  					}
	  					
	  				}
			  	    
			  	    //存返佣记录
			  	     ReckbackInfo rbif=new ReckbackInfo();
			  	     rbif.setUserId(parent_id);
			  	     rbif.setCrmUserId(crm_user_id);
			  	     rbif.setCrmOrderId(order_id);
			  	     rbif.setCrmTradeId(loginID);
			  	     rbif.setTradeQty(qty);
			  	     rbif.setGroupName(group_name);
			  	     rbif.setSymbol(symbol);
			  	     rbif.setReckbackTradeId(u.getStoreId()!=null?u.getStoreId().toString():"");
			  	     rbif.setReckbackStatus(0);
			  	     rbif.setReckbackType(reckback_type);
			  	     rbif.setReckbackAmount(reckback_amount);
			  	     rbif.setBackup1(u.getNickName());
			  	     if(rbif.getReckbackAmount()>0&&!rbif.getReckbackTradeId().equals("")) {
			  	    	this.reckbackInfoService.saveOrUpdate(rbif);
			  	     }else {
			  	     }
  			}else {
  			}
  		       this.toDoRekeBack(order_id,trade_id, symbol, loginID, crm_user_id, group_name, pparent_id, qty, aa1, aa2, aa3, bb1, bb2, bb3,level+1);
  			}
  			
  			
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    //发放佣金   每60秒查询一次，延迟15分钟
    @Scheduled(fixedRate=60000,initialDelay=460000)
    private void configureTasks7() {
    	if(bl3&&mainSwitch) {
           
        ReckbackInfo rbif_query=new ReckbackInfo();
        rbif_query.setReckbackStatus(0);
        Page<ReckbackInfo> ti_page=this.reckbackInfoService.findAll(0,55, "id", "asc", rbif_query);
        
        if(ti_page.getContent().size()>0) {
		        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		  		try {
		  			MyWebsocketClient4RekeBack client= new MyWebsocketClient4RekeBack(new URI("ws://"+ss.getApiAddress()+":"+"8060"), new Draft_6455(), null, 0);
		  			 client.setReckbackInfoService(this.reckbackInfoService);
		  			 client.connect();
		  			Thread.sleep(300);
		 			if (client.getReadyState().equals(READYSTATE.OPEN)) {
		 				System.out.println("sendRekeback   "+new Date()+" "+client.getReadyState());
		 				for(int i=0;i<ti_page.getContent().size();i++) {
				    		ReckbackInfo entity  = ti_page.getContent().get(i);
				    		entity.setReckbackStatus(99);
				    		this.reckbackInfoService.saveOrUpdate(entity);
				    		sendRekeBack(entity,client);
				    		Thread.sleep(100);
				    	}	
		 			 }
			    	
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
        }
    	}
    	
    }
    
    public void sendRekeBack(ReckbackInfo entity,MyWebsocketClient4RekeBack client) {
  		try {
  		     HashMap map=new HashMap();
  		    map.put("reqtype", "deposituserinfo");
  			map.put("reqid", new Date().getTime()+"___"+entity.getId());
  			map.put("login", new Integer(entity.getReckbackTradeId()));
			map.put("operationtype", 1); 
			map.put("amount", entity.getReckbackAmount());
			//map.put("endtime", 1867656947L);
			map.put("comment", "Co."+entity.getCrmTradeId()+"-"+entity.getCrmOrderId());
  		    JSONObject jsonObj=new JSONObject(map);
  		   client.send(jsonObj.toString());
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    
    
    //查询转账出金   每1分钟查询一次，延迟5分钟
    @Scheduled(fixedRate=60000,initialDelay=370000)
    private void configureTasks4() {
    	if(bl6&&mainSwitch) {
    	TransferInfo ti_query=new TransferInfo();
    	
    	ti_query.setAuditStatus(1);
    	ti_query.setTransferStatus(0);
    	ti_query.setOutStatus(0);
    	
    	Page<TransferInfo> ti_page=this.transferInfoService.findAll(0,60, "id", "asc", ti_query);
    	
    	if(ti_page.getContent().size()>0) {
		    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		  		try {
		  			MyWebsocketClient4TransferOut client= new MyWebsocketClient4TransferOut(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
		  		client.setTransferInfoService(this.transferInfoService);
		  		client.connect();
		  		Thread.sleep(300);
	 			if (client.getReadyState().equals(READYSTATE.OPEN)) {
	 				System.out.println("transferOut   "+new Date()+" "+client.getReadyState());
	 				for(int i=0;i<ti_page.getContent().size();i++) {
			    		TransferInfo entity  = ti_page.getContent().get(i);
			    		//client.setTransferInfo(entity);
			    		entity.setTransferStatus(99);
			    		this.transferInfoService.saveOrUpdate(entity);
			    		transferOut(entity,client);
			    	}
	 			 }
		    	
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
		    	}
    	}
    	
    }
    
    public void transferOut(TransferInfo transferInfo,MyWebsocketClient4TransferOut client) {
	  		try {
  		     HashMap map=new HashMap();
  		    map.put("reqtype", "deposituserinfo");
  			map.put("reqid", new Date().getTime()+"___"+transferInfo.getId());
  			map.put("login", new Integer(String.valueOf(transferInfo.getTransferOut())));
			map.put("operationtype", 2);  
			map.put("amount", transferInfo.getTransferAmount());
			//map.put("endtime", 1867656947L);
			map.put("comment", "Transfer Out");
  		    JSONObject jsonObj=new JSONObject(map);
  		   
  		   client.send(jsonObj.toString());
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    //查询转账入金   每1分钟查询一次，延迟7分钟
    @Scheduled(fixedRate=60000,initialDelay=428000)
    private void configureTasks5() {
    	if(bl7&&mainSwitch) {
       TransferInfo ti_query=new TransferInfo();
    	
    	ti_query.setAuditStatus(1);
    	ti_query.setTransferStatus(1);
    	ti_query.setOutStatus(1);
        Page<TransferInfo> ti_page=this.transferInfoService.findAll(0,60, "id", "asc", ti_query);
        
        if(ti_page.getContent().size()>0) {
			        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
			  		try {
			  			MyWebsocketClient4TransferIn client= new MyWebsocketClient4TransferIn(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
			  			client.setTransferInfoService(this.transferInfoService);
			  			 client.connect();
			  			Thread.sleep(300);
			 			if (client.getReadyState().equals(READYSTATE.OPEN)) {
			 				System.out.println("transferIN   "+new Date()+" "+client.getReadyState());
			 				for(int i=0;i<ti_page.getContent().size();i++) {
					    		TransferInfo entity  = ti_page.getContent().get(i);
					    		entity.setTransferStatus(99);
					    		this.transferInfoService.saveOrUpdate(entity);
					    		transferIN(entity,client);
					    	}
			 			 }
				    	
			  		}catch(Exception e) {
			  			System.out.println(e);
			  		}
			        }
    	}
    	
    }
    
    public void transferIN(TransferInfo transferInfo,MyWebsocketClient4TransferIn client) {
  		try {
  		   
  		     HashMap map=new HashMap();
  		    map.put("reqtype", "deposituserinfo");
  			map.put("reqid", new Date().getTime()+"___"+transferInfo.getId());
  			map.put("login", new Integer(String.valueOf(transferInfo.getTransferIn())));
			map.put("operationtype", 1); 
			map.put("amount", transferInfo.getTransferAmount());
			//map.put("endtime", 1867656947L);
			map.put("comment", "Transfer In");
  		    JSONObject jsonObj=new JSONObject(map);
  		   client.send(jsonObj.toString());
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    //补发佣金   每30秒查询一次，延迟16分钟
    @Scheduled(fixedRate=30000,initialDelay=999000)
    private void configureTasks7_2() {
    	if(bl13&&mainSwitch) {
           
        ReckbackInfo rbif_query=new ReckbackInfo();
        rbif_query.setReckbackStatus(2);
        Page<ReckbackInfo> ti_page=this.reckbackInfoService.findAll(0,30, "id", "asc", rbif_query);
        
        if(ti_page.getContent().size()>0) {
        	
        
		        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		  		try {
		  			MyWebsocketClient4RekeBack client= new MyWebsocketClient4RekeBack(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
		  			 client.setReckbackInfoService(this.reckbackInfoService);
		  			 client.connect();
		  			Thread.sleep(300);
		 			if (client.getReadyState().equals(READYSTATE.OPEN)) {
		 				System.out.println("sendRekeback_99   "+new Date()+" "+client.getReadyState());
		 				for(int i=0;i<ti_page.getContent().size();i++) {
				    		ReckbackInfo entity  = ti_page.getContent().get(i);
				    		entity.setReckbackStatus(99);
				    		this.reckbackInfoService.saveOrUpdate(entity);
				    		sendRekeBack(entity,client);
				    	}
		 			 }
			    	
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
        }
    	}
    	
    }
    
    
    //补漏开交易账户   每1分钟查询一次，延迟50秒
    @Scheduled(fixedRate=60000,initialDelay=50000)
    private void configureTasks9() {
    	if(bl9&&mainSwitch) {
    		TradeAccount query=new TradeAccount();
        	query.setIsAvailable(1);
        	query.setTradeStatus(2);
        	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,1,"id","asc",query);
             if(pages2.getContent().size()>0) {
		        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		  		try {
		  			TradeAccount ta_bc=(TradeAccount)pages2.getContent().get(0);
		  			if(ta_bc.getTradeId()!=null&&!ta_bc.getTradeId().equals("")&&ta_bc.getUserId()!=null) {
		  				UserInfo userInfo=(UserInfo)this.userInfoService.findById(ta_bc.getUserId());
		  				WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						client.connect();
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(300);
							System.out.println("bc_register   "+new Date()+" "+client.getReadyState());
								client.reconnect();
						}
							HashMap map=new HashMap();
							map.put("reqtype", "register");
							map.put("reqid", new Date().getTime());
							map.put("login", new Integer(String.valueOf(ta_bc.getTradeId())));
							map.put("username", new String(userInfo.getFullname()));
							map.put("leverage", new Integer(ta_bc.getLeverShow()));
							map.put("groupname", ta_bc.getGroupName());
							map.put("password", ta_bc.getBackup6());
							map.put("investor", ta_bc.getBackup6()+"1");
							map.put("phonepwd", ta_bc.getBackup6()+"2");
						    JSONObject jsonObj=new JSONObject(map);
						    client.send(jsonObj.toString());
						    Thread.sleep(500);
							while (!client.getReadyState().equals(READYSTATE.OPEN)) {
								Thread.sleep(300);
								System.out.println("completRegister   "+new Date()+" "+client.getReadyState());
									client.reconnect();
							}
						    HashMap map1=new HashMap();
						    map1.put("reqtype", "updateuserinfo");
						    map1.put("reqid", new Date().getTime());
						    map1.put("login", new Integer(String.valueOf(ta_bc.getTradeId())));
						    map1.put("leverage", new Integer(ta_bc.getLeverShow()));
						    map1.put("email", userInfo.getUserName());
						    map1.put("country", userInfo.getCountry());
						    map1.put("state", userInfo.getProvince());
						    map1.put("city",userInfo.getCity());
						    map1.put("address", userInfo.getAdress());
						    map1.put("phone", userInfo.getTel());
						    map1.put("enable", 1);//是否启用  1 启用  0 禁用
						    client.send(new JSONObject(map1).toString());
						    ta_bc.setTradeStatus(1);
						    this.tradeAccountService.saveOrUpdate(ta_bc);
		  			}
		  			
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
        }
    	}
    	
    }
    
    
   
    
    
}
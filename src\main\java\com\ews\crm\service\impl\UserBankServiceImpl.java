package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.UserBank;
import com.ews.crm.repository.UserBankRepository;
import com.ews.crm.service.UserBankService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class UserBankServiceImpl implements UserBankService 
{
	@Autowired
	private UserBankRepository userBankRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<UserBank> findAll(Integer page, Integer size,String sortName,String sortOrder, UserBank userBank) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<UserBank> pages = userBankRepository.findAll(new Specification<UserBank>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<UserBank> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(userBank.getUserId())) { 
                predicates.add(criteriaBuilder.equal(root.get("userId").as(Long.class), userBank.getUserId()));
             }
             if(!StringUtils.isEmpty(userBank.getAccountName())) { 
                predicates.add(criteriaBuilder.like(root.get("accountName").as(String.class),"%"+userBank.getAccountName()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getBankName())) { 
                predicates.add(criteriaBuilder.like(root.get("bankName").as(String.class),"%"+userBank.getBankName()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getBankAccount())) { 
                predicates.add(criteriaBuilder.like(root.get("bankAccount").as(String.class),"%"+userBank.getBankAccount()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getAccountUsername())) { 
                predicates.add(criteriaBuilder.like(root.get("accountUsername").as(String.class),"%"+userBank.getAccountUsername()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getBankAddress())) { 
                predicates.add(criteriaBuilder.like(root.get("bankAddress").as(String.class),"%"+userBank.getBankAddress()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getTel())) { 
                predicates.add(criteriaBuilder.like(root.get("tel").as(String.class),"%"+userBank.getTel()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getSwift())) { 
                predicates.add(criteriaBuilder.like(root.get("swift").as(String.class),"%"+userBank.getSwift()+"%"));
             }
             if(!StringUtils.isEmpty(userBank.getStatus())) { 
                predicates.add(criteriaBuilder.equal(root.get("status").as(Integer.class), userBank.getStatus()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public UserBank findById(Long id) {
      Optional<UserBank> op = userBankRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.userBankRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         UserBank old = userBankRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 userBankRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 userBankRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.userBankRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(UserBank userBank) {
       
        Result vr = new Result();
		try {
        	if (userBank.getId()== null) {
            	userBank.setGmtCreate(new Date());
            	userBank.setGmtModified(new Date());
            	userBank.setIsDeleted(0);
            	if(userBank.getIsAvailable() == null) {
            		userBank.setIsAvailable(1);
            	}
            	
	    	} else {
            	userBank.setGmtModified(new Date());
        	}
            userBankRepository.save(userBank);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "reckback_info")
public class ReckbackInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

	
	    /**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchBegin;

	    /**
	    *gmtCreate 的列表查询条件
	    **/
		@Transient
		protected  String gmtCreateSearchEnd;
		
		
    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *返佣用户ID
    **/
	@Column(name = "user_id")
	protected  Long userId;

    /**
    *CRM用户ID
    **/
	@Column(name = "crm_user_id")
	protected  Long crmUserId;

    /**
    *交易订单号
    **/
	@Column(name = "crm_order_id")
	protected  String crmOrderId;

    /**
    *交易账号
    **/
	@Column(name = "crm_trade_id")
	protected  String crmTradeId;

    /**
    *交易手数
    **/
	@Column(name = "trade_qty")
	protected  Double tradeQty;

    /**
    *用户交易组
    **/
	@Column(name = "group_name")
	protected  String groupName;

    /**
    *交易品种
    **/
	@Column(name = "symbol")
	protected  String symbol;

    /**
    *下级返佣用户ID
    **/
	@Column(name = "reckback_user_id")
	protected  Long reckbackUserId;

    /**
    *返佣订单号
    **/
	@Column(name = "reckback_order_id")
	protected  String reckbackOrderId;

    /**
    *返佣交易账号
    **/
	@Column(name = "reckback_trade_id")
	protected  String reckbackTradeId;

    /**
    *返佣状态    0 待处理  1 成功  2 失败
    **/
	@Column(name = "reckback_status")
	protected  Integer reckbackStatus;

    /**
    *返佣类型  1 一级返佣  2 二级返佣  3  三级返佣   6销售返佣  7 销售经理返佣 8 总监返佣
    **/
	@Column(name = "reckback_type")
	protected  Integer reckbackType;

    /**
    *返佣金额
    **/
	@Column(name = "reckback_amount")
	protected  Double reckbackAmount;

    /**
    *backup1-- 返佣账号姓名
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

    /**
    *backup5
    **/
	@Column(name = "backup5")
	protected  Integer backup5;

    /**
    *backup6
    **/
	@Column(name = "backup6")
	protected  Integer backup6;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public Long  getCrmUserId()
    {
        return crmUserId;
    }
    public void setCrmUserId(Long  crmUserId)
    {
        this.crmUserId = crmUserId;
    }
    public String  getCrmOrderId()
    {
        return crmOrderId;
    }
    public void setCrmOrderId(String  crmOrderId)
    {
        this.crmOrderId = crmOrderId;
    }
    public String  getCrmTradeId()
    {
        return crmTradeId;
    }
    public void setCrmTradeId(String  crmTradeId)
    {
        this.crmTradeId = crmTradeId;
    }
    public Double  getTradeQty()
    {
        return tradeQty;
    }
    public void setTradeQty(Double  tradeQty)
    {
        this.tradeQty = tradeQty;
    }
    public String  getGroupName()
    {
        return groupName;
    }
    public void setGroupName(String  groupName)
    {
        this.groupName = groupName;
    }
    public String  getSymbol()
    {
        return symbol;
    }
    public void setSymbol(String  symbol)
    {
        this.symbol = symbol;
    }
    public Long  getReckbackUserId()
    {
        return reckbackUserId;
    }
    public void setReckbackUserId(Long  reckbackUserId)
    {
        this.reckbackUserId = reckbackUserId;
    }
    public String  getReckbackOrderId()
    {
        return reckbackOrderId;
    }
    public void setReckbackOrderId(String  reckbackOrderId)
    {
        this.reckbackOrderId = reckbackOrderId;
    }
    public String  getReckbackTradeId()
    {
        return reckbackTradeId;
    }
    public void setReckbackTradeId(String  reckbackTradeId)
    {
        this.reckbackTradeId = reckbackTradeId;
    }
    public Integer  getReckbackStatus()
    {
        return reckbackStatus;
    }
    public void setReckbackStatus(Integer  reckbackStatus)
    {
        this.reckbackStatus = reckbackStatus;
    }
    public Integer  getReckbackType()
    {
        return reckbackType;
    }
    public void setReckbackType(Integer  reckbackType)
    {
        this.reckbackType = reckbackType;
    }
    public Double  getReckbackAmount()
    {
        return reckbackAmount;
    }
    public void setReckbackAmount(Double  reckbackAmount)
    {
        this.reckbackAmount = reckbackAmount;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(Integer  backup5)
    {
        this.backup5 = backup5;
    }
    public Integer  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(Integer  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public String getGmtCreateSearchBegin() {
		return gmtCreateSearchBegin;
	}
	public void setGmtCreateSearchBegin(String gmtCreateSearchBegin) {
		this.gmtCreateSearchBegin = gmtCreateSearchBegin;
	}
	public String getGmtCreateSearchEnd() {
		return gmtCreateSearchEnd;
	}
	public void setGmtCreateSearchEnd(String gmtCreateSearchEnd) {
		this.gmtCreateSearchEnd = gmtCreateSearchEnd;
	}
    
    

}

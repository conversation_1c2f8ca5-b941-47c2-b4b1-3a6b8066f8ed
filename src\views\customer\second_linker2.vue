<template>
  <div class="app-container">
    <el-form ref="dataForm" :rules="rules" :inline="true" :model="temp" label-position="right" label-width="100px" style="margin-left:10px;">
      <el-form-item label="客户编号" prop="customerCode">
        <span>{{ temp.customerCode }}</span>
      </el-form-item>

      <el-form-item label="客户名称" prop="customerName">
        <span>{{ temp.customerName }}</span>
      </el-form-item>
      <el-form-item label="上级客户" prop="customerParent">
        <span>{{ temp.customerParent }}</span>
      </el-form-item>

      <el-form-item label="联系人" prop="linkerParent">
        <span>{{ temp.linkerParent }}</span>
      </el-form-item>
      <el-form-item label="电话" prop="tel">
        <span>{{ temp.tel }}</span>
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <span>{{ temp.address }}</span>
      </el-form-item>

      <el-form-item label="对公开户行" prop="bankName2">
        <span>{{ temp.bankName2 }}</span>
      </el-form-item>

      <el-form-item label="银行账号" prop="bankNumber">
        <span>{{ temp.bankNumber }}</span>
      </el-form-item>

      <el-form-item label="接口人" prop="liaisonOfficer">
        <span>{{ temp.liaisonOfficer }}</span>
      </el-form-item>

      <el-form-item label="电话" prop="liaisonTel">
        <span>{{ temp.liaisonTel }}</span>
      </el-form-item>

      <el-form-item label="创建人" prop="creater">
        <span>{{ temp.creater }}</span>
      </el-form-item>

      <el-form-item label="所有人" prop="owner">
        <span>{{ temp.owner }}</span>
      </el-form-item>

      <el-form-item label="创建日期" prop="createTime">
        <span>{{ temp.createTime| parseTime('{y}-{m}-{d} ') }}</span>
      </el-form-item>

      <el-form-item label="备注" prop="remark2">
        <span>{{ temp.remark2 }}</span>
      </el-form-item>

      <br><br>
      <el-table :data="temp.details" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;">

        <el-table-column label="联系人" prop="linkMan" min-width="200px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.linkMan }}</span>
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="linkPhone" min-width="150px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.linkPhone }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职位" prop="workTitle" min-width="150px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.workTitle }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开户行" prop="bankName" min-width="150px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.bankName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="银行账号" prop="bankAccount" min-width="150px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.bankAccount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150px" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>

    </el-form>
    <el-dialog title="新增" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">

        <el-form-item label="联系人" prop="linkMan">
          <el-input v-model="temp.linkMan" />
        </el-form-item>
        <el-form-item label="联系电话" prop="linkPhone">
          <el-input v-model="temp.linkPhone" />
        </el-form-item>
        <el-form-item label="职位" prop="workTitle">
          <el-input v-model="temp.workTitle" />
        </el-form-item>
        <el-form-item label="开户行" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>
        <el-form-item label="银行账号" prop="bankAccount">
          <el-input v-model="temp.bankAccount" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="temp.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <br><br>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleCreate()">新增</el-button>
      <el-button @click="fh()">返回</el-button>

    </div>

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchCustomer, createCustomer, updateCustomer, updateIsAvailable, removeCustomer } from '@/api/customer'
import { createSecondLiner } from '@/api/secondLinker'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'SaleOrderTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      multipleSelection: [],
      listQuery: {
			 		page: 1,
        limit: 10,
        orderSerial: undefined,
        customerName: undefined,
        salerName: undefined,
        linkerName: undefined,

        orderDateSearchBegin: undefined,
        orderDateSearchEnd: undefined
      },

      paymentTypes: [{
			    value: 1,
			    label: '银行转账'
			  }, {
			    value: 2,
			    label: '现金支付'
			  }, {
			    value: 3,
			    label: '其他途径'
			  }],
      pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }

        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
					 }]				},
      temp: {
        id: undefined,
        orderSerial: '',
        customerName: '',
        linkerName: '',
				    customerId: '',
        orderDate: '',
        salerName: '',
        orderAmount: '',
        discountAmount: '',
        returnAuditDate: '',
        linkMan: '',
        linkPhone: '',
        workTitle: '',
        bankName: '',
        bankAccount: '',
        remark: '',
        stype: '1',
        minusAuditDate: '',
        isEcard: '0',
        details: [],
        cardQty: '',
        orderAmount: '',
        taxAmount: '',
        rejectResonTj: '',
        orderStatus: 0,
        ssid: [],
        busId: '',
        busType: ''

      },
      dialogFormAddCardVisible: false,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      valueOrderDate: undefined,
      cardTypeLoading: false,
      cardSpecsLoading: false,
      customerIds: [],
				 cardtypeIds: [
        {

        }
      ],
      cardspecsIds: [
        {

        }
      ],
      rules: {
        linkMan: [
          { required: true, message: '联系人不能为空', trigger: 'change' }
        ],
        linkPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'change' }
        ],
        workTitle: [
        ],
        bankName: [
        ],
        bankAccount: [
        ],
        remark: [
        ]
      }
    }
  },
  created() {
    const id = this.$route.params && this.$route.params.id
    if (id != null && id != undefined && id != '') {
      this.getInfo(id)
    }
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    getInfo(id) {
      this.temp.id = id
      fetchCustomer(id).then(res => {
        var data = res.data

        this.temp = data
        this.temp.bankName2 = data.bankName
        this.temp.remark2 = data.remark
        this.temp.bankName = ''
        this.temp.remark = ''
      })
    },
    handleFilter() {
      if (this.valueOrderDate) {
        this.listQuery.orderDateSearchBegin = parseTime(new Date(this.valueOrderDate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
        this.listQuery.orderDateSearchEnd = parseTime(new Date(this.valueOrderDate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        this.listQuery.orderDateSearchBegin = undefined
        this.listQuery.orderDateSearchEnd = undefined
      }
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },

    handleAddDetails() {
      this.dialogFormAddCardVisible = true
      this.$nextTick(() => {
        this.$refs['dataGoodsAddForm'].clearValidate()
      })
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        orderSerial: '',
        customerName: '',
        linkerName: '',

        salerName: '',
        orderDate: '',
        orderAmount: '',
        returnAuditDate: '',
        minusAuditDate: ''
      }
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.stype = 1

          createSecondLiner(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.dialogFormAddVisible = false
              this.getInfo(this.temp.id)
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleCreate() {
      this.dialogFormAddVisible = true
      this.$refs['dataForm'].clearValidate()
    }, getCustomer(query) {
					  this.customerIds = []

						  fetchCustomer(query).then(res => {
							 this.customerIds = []
							 var data = res.data
							 	for (var j = 0; j < data.length; j++) {
							 		var id = data[j].id
							 		var name = data[j].customerName
							 		var per = []
							 		per.value = id
							 		per.label = name
							 		this.customerIds.push(per)
							 }
      })
    }, fh() {
      this.$router.push({ path: '/saleMange/customer/' })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          // this.temp.details = this.details

          updateSaleOrder(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeSaleOrder(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    },
				  handleSelectionChange(val) {
      this.multipleSelection = val
      this.temp.ssid = []
      for (var j = 0; j < val.length; j++) {
        this.temp.ssid[j] = val[j]['id']
      }
				  }

  }
}
</script>

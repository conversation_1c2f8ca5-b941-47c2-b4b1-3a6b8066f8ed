package com.ews.system.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.system.entity.Role;
import com.ews.system.service.PermissionService;
import com.ews.system.service.RoleService;

@RestController
@RequestMapping("/admin/role")
public class RoleController  {
	@Autowired
	private RoleService roleService;
	@Autowired
	private PermissionService permissionService;

	/**
	 * 获取所有权限信息
	 * 
	 * @param map
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/roles")
	public ResponseData roleList(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
		JSONObject result = new JSONObject();
		Role queryRole = new Role();
		return ResponseDataUtil.buildSuccess(roleService.findAll(queryRole));
	}

	/**
	 * 编辑
	 * 
	 * @param role
	 * @param bindingResult
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.PUT)
	@ResponseBody
	public ResponseData edit(@RequestBody JSONObject data, Long roleId, HttpServletRequest request) {
		JSONObject result = new JSONObject();
		Role role = this.roleService.findById(roleId);
		if (role != null) {
			try {
				role.setDescription(data.getString("description"));
				if(data.containsKey("roleSign")) {
					role.setRoleSign(data.getString("roleSign"));
				}
				
				role.setRoleName(data.getString("roleName"));
				// 处理权限
				// (String[]) data.getJSONArray("permissions").toArray(String[]);
				Object[] o = data.getJSONArray("permissions").toArray();
				String[] s = new String[o.length];

				for (int i = 0; i < o.length; i++) {
					s[i] = o[i].toString();
				}
				Result r = this.roleService.saveOrUpdateRoleAndPermission(role, s);
				if (r.getCode().equals(Result.CODESUCCESS)) {
					return ResponseDataUtil.buildSuccess();
				} else {
					return ResponseDataUtil.buildError(r.getMessage());
				}
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError();
		}
	}

	/**
	 * 新增
	 * 
	 * @param role
	 * @param bindingResult
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@ResponseBody
	public ResponseData  add(@RequestBody JSONObject data, HttpServletRequest request) {
		JSONObject result = new JSONObject();
		System.out.println(" 新增时的权限信息 permissions:" + data.get("permissions"));
		try {
			Role role = new Role();
			role.setDescription(data.getString("description"));
			
			role.setRoleSign("5");
			
			role.setRoleName(data.getString("roleName"));
			// 处理权限
			// (String[]) data.getJSONArray("permissions").toArray(String[]);
			Object[] o = data.getJSONArray("permissions").toArray();
			String[] s = new String[o.length];

			for (int i = 0; i < o.length; i++) {
				s[i] = o[i].toString();
			}
			Result r = this.roleService.saveOrUpdateRoleAndPermission(role, s);
			if (r.getCode().equals(Result.CODESUCCESS)) {
				return ResponseDataUtil.buildSuccess(role);
			} else {
				return ResponseDataUtil.buildError(r.getMessage());
			}
		} catch (Exception e) {
			e.printStackTrace();
			 return ResponseDataUtil.buildError(e.getMessage());
		}

	}
	/**
	 * 删除
	 * 
	 * @param roleId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/remove", method = RequestMethod.GET)
	@ResponseBody
	public ResponseData roleRemove(@Valid Long roleId, HttpServletRequest request) {
		JSONObject jo = new JSONObject();
		Result result = this.roleService.removeRoleByRoleId(roleId);
		if(result.getCode().equals(Result.CODESUCCESS)) {
			return ResponseDataUtil.buildSuccess();
		}
		return ResponseDataUtil.buildError("操作失败");
	}

	/**
	 * 改变用户状态
	 * 
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateIsAvailable", method = RequestMethod.GET)
	public ResponseData updateIsAvailable(@Valid Long roleId, @Valid Integer isAvailable, HttpServletRequest request) {
		if (roleId != null && isAvailable != null) {
			try {
				if (this.roleService.updateIsAvailableById(roleId, isAvailable)) {
					return ResponseDataUtil.buildSuccess();
				} else {
					return ResponseDataUtil.buildError("更新失败");
				}
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError("参数错误");
		}

	}

}

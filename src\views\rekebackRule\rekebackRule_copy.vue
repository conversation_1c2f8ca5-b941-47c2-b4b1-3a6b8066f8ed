<template>
  <div class="app-container">

    <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="150px" style="max-width: 600px; margin-left:10px;">
      <el-form-item :label="$t('rekebackRule.label1')" prop="ruleName">
        <el-input v-model="temp.ruleName" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label3')" prop="rekebackA1">
        <el-input v-model="temp.rekebackA1" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label4')" prop="rekebackA2">
        <el-input v-model="temp.rekebackA2" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label5')" prop="rekebackA3">
        <el-input v-model="temp.rekebackA3" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label11')" prop="backup5">
        <el-select v-model="temp.backup5">
          <el-option
            v-for="item in wbfyjs"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('rekebackRule.label6')" prop="rekebackB1">
        <el-input v-model="temp.rekebackB1" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label7')" prop="rekebackB2">
        <el-input v-model="temp.rekebackB2" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label8')" prop="rekebackB3">
        <el-input v-model="temp.rekebackB3" />
      </el-form-item>

      <el-form-item v-if="temp.backup5>=4" :label="$t('rekebackRule.label12')" prop="rekebackB4">
        <el-input v-model="temp.rekebackB4" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=5" :label="$t('rekebackRule.label13')" prop="rekebackB5">
        <el-input v-model="temp.rekebackB5" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=6" :label="$t('rekebackRule.label14')" prop="rekebackB6">
        <el-input v-model="temp.rekebackB6" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=7" :label="$t('rekebackRule.label15')" prop="rekebackB7">
        <el-input v-model="temp.rekebackB7" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=8" :label="$t('rekebackRule.label16')" prop="rekebackB8">
        <el-input v-model="temp.rekebackB8" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=9" :label="$t('rekebackRule.label17')" prop="rekebackB9">
        <el-input v-model="temp.rekebackB9" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=10" :label="$t('rekebackRule.label18')" prop="rekebackB10">
        <el-input v-model="temp.rekebackB10" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=11" :label="$t('rekebackRule.label19')" prop="rekebackB11">
        <el-input v-model="temp.rekebackB11" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=12" :label="$t('rekebackRule.label20')" prop="rekebackB12">
        <el-input v-model="temp.rekebackB12" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=13" :label="$t('rekebackRule.label21')" prop="rekebackB13">
        <el-input v-model="temp.rekebackB13" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=14" :label="$t('rekebackRule.label22')" prop="rekebackB14">
        <el-input v-model="temp.rekebackB14" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=15" :label="$t('rekebackRule.label23')" prop="rekebackB15">
        <el-input v-model="temp.rekebackB15" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=16" :label="$t('rekebackRule.label24')" prop="rekebackB16">
        <el-input v-model="temp.rekebackB16" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=17" :label="$t('rekebackRule.label25')" prop="rekebackB17">
        <el-input v-model="temp.rekebackB17" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=18" :label="$t('rekebackRule.label26')" prop="rekebackB18">
        <el-input v-model="temp.rekebackB18" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=19" :label="$t('rekebackRule.label27')" prop="rekebackB19">
        <el-input v-model="temp.rekebackB19" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=20" :label="$t('rekebackRule.label28')" prop="rekebackB20">
        <el-input v-model="temp.rekebackB20" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=21" :label="$t('rekebackRule.label29')" prop="rekebackB21">
        <el-input v-model="temp.rekebackB21" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=22" :label="$t('rekebackRule.label30')" prop="rekebackB22">
        <el-input v-model="temp.rekebackB22" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=23" :label="$t('rekebackRule.label31')" prop="rekebackB23">
        <el-input v-model="temp.rekebackB23" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=24" :label="$t('rekebackRule.label32')" prop="rekebackB24">
        <el-input v-model="temp.rekebackB24" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=25" :label="$t('rekebackRule.label33')" prop="rekebackB25">
        <el-input v-model="temp.rekebackB25" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=26" :label="$t('rekebackRule.label34')" prop="rekebackB26">
        <el-input v-model="temp.rekebackB26" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=27" :label="$t('rekebackRule.label35')" prop="rekebackB27">
        <el-input v-model="temp.rekebackB27" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=28" :label="$t('rekebackRule.label36')" prop="rekebackB28">
        <el-input v-model="temp.rekebackB28" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=29" :label="$t('rekebackRule.label37')" prop="rekebackB29">
        <el-input v-model="temp.rekebackB29" />
      </el-form-item>
      <el-form-item v-if="temp.backup5>=30" :label="$t('rekebackRule.label38')" prop="rekebackB30">
        <el-input v-model="temp.rekebackB30" />
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label39')">
        <el-select v-model="temp.tradeProds" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
          <el-option
            v-for="item in option1s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label40')">
        <el-select v-model="temp.userGroups" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
          <el-option
            v-for="item in option2s"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('rekebackRule.label41')" prop="remark">
        <el-input v-model="temp.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="qx()">{{ $t('table.cancel') }}</el-button>
      <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
    </div>

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchRekebackRule, createRekebackRule, updateRekebackRule, updateIsAvailable, removeRekebackRule } from '@/api/rekebackRule'
import { listshow } from '@/api/userGroup'
import { listshowtradeProd } from '@/api/tradeProd'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'RekebackRuleTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        ruleName: undefined
      },
      temp: {
        id: undefined,
        ruleName: '',
        rekebackA1: '',
        rekebackA2: '',
        rekebackA3: '',
        rekebackB1: '',
        rekebackB2: '',
        rekebackB3: '',
        rekebackB4: '',
        rekebackB5: '',
        rekebackB6: '',
        rekebackB7: '',
        rekebackB8: '',
        rekebackB9: '',
        rekebackB10: '',
        rekebackB11: '',
        rekebackB12: '',
        rekebackB13: '',
        rekebackB14: '',
        rekebackB15: '',
        rekebackB16: '',
        rekebackB17: '',
        rekebackB18: '',
        rekebackB19: '',
        rekebackB20: '',
        rekebackB21: '',
        rekebackB22: '',
        rekebackB23: '',
        rekebackB24: '',
        rekebackB25: '',
        rekebackB26: '',
        rekebackB27: '',
        rekebackB28: '',
        rekebackB29: '',
        rekebackB30: '',
        tradeProds: [],
        userGroups: [],
        remark: '',
        backup5: 3
      }, wbfyjs: [
        {
          value: 3,
          label: this.$t('rekebackRule.level3')
        }, {
          value: 4,
          label: this.$t('rekebackRule.level4')
        }, {
          value: 5,
          label: this.$t('rekebackRule.level5')
        }, {
          value: 6,
          label: this.$t('rekebackRule.level6')
        }, {
          value: 7,
          label: this.$t('rekebackRule.level7')
        }, {
          value: 8,
          label: this.$t('rekebackRule.level8')
        }, {
          value: 9,
          label: this.$t('rekebackRule.level9')
        }, {
          value: 10,
          label: this.$t('rekebackRule.level10')
        }, {
          value: 11,
          label: this.$t('rekebackRule.level11')
        }, {
          value: 12,
          label: this.$t('rekebackRule.level12')
        }, {
          value: 13,
          label: this.$t('rekebackRule.level13')
        }, {
          value: 14,
          label: this.$t('rekebackRule.level14')
        }, {
          value: 15,
          label: this.$t('rekebackRule.level15')
        }, {
          value: 16,
          label: this.$t('rekebackRule.level16')
        }, {
          value: 17,
          label: this.$t('rekebackRule.level17')
        }, {
          value: 18,
          label: this.$t('rekebackRule.level18')
        }, {
          value: 19,
          label: this.$t('rekebackRule.level19')
        }, {
          value: 20,
          label: this.$t('rekebackRule.level20')
        }, {
          value: 21,
          label: this.$t('rekebackRule.level21')
        }, {
          value: 22,
          label: this.$t('rekebackRule.level22')
        }, {
          value: 23,
          label: this.$t('rekebackRule.level23')
        }, {
          value: 24,
          label: this.$t('rekebackRule.level24')
        }, {
          value: 25,
          label: this.$t('rekebackRule.level25')
        }, {
          value: 26,
          label: this.$t('rekebackRule.level26')
        }, {
          value: 27,
          label: this.$t('rekebackRule.level27')
        }, {
          value: 28,
          label: this.$t('rekebackRule.level28')
        }, {
          value: 29,
          label: this.$t('rekebackRule.level29')
        }, {
          value: 30,
          label: this.$t('rekebackRule.level30')
        }],
      list_page: 1,
      list_ruleName: undefined,
      option1s: [],
      option2s: [],
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        ruleName: [
          { required: true, message: 'Rule name cannot be empty', trigger: 'change' },,
        ],
        rekebackA1: [
          { required: true, message: 'Director commission return cannot be empty', trigger: 'change' },,
        ],
        rekebackA2: [
          { required: true, message: 'Sales Manager Rebate cannot be empty', trigger: 'change' },,
        ],
        rekebackA3: [
          { required: true, message: 'Sales commission cannot be empty', trigger: 'change' },,
        ],
        rekebackA4: [
        ],
        rekebackA5: [
        ],
        rekebackB1: [
          { required: true, message: 'The commission returned by the higher-level agent cannot be empty', trigger: 'change' },,
        ],
        rekebackB2: [
          { required: true, message: 'The commission returned by the upper and lower level agents cannot be empty', trigger: 'change' },,
        ],
        rekebackB3: [
          { required: true, message: 'The commission rebate for the upper and third level agents cannot be empty', trigger: 'change' },,
        ],
        rekebackB4: [
        ],
        remark: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.list_page = this.$route.params.page
    this.list_ruleName = this.$route.params.ruleName

    this.option1s = []
    listshowtradeProd().then(response => {
      var datas4 = response.data.items
             	for (var j = 0; j < datas4.length; j++) {
             		var id = datas4[j].id
             		var name = datas4[j].prodName
             		var per = []
             		per.value = id
             		per.label = name
        this.option1s.push(per)
      }
    })
    this.option2s = []
    listshow().then(response => {
      var datas3 = response.data.items
             	for (var j = 0; j < datas3.length; j++) {
             		var id = datas3[j].id
             		var name = datas3[j].groupName
             		var per = []
             		per.value = id
             		per.label = name
        this.option2s.push(per)
      }
    })

    const id = this.$route.params && this.$route.params.id
    if (id != null && id != undefined && id != '') {
      this.getInfo(id)
    }
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    getInfo(id) {
      fetchRekebackRule(id).then(res => {
        var data = res.data
        this.temp.id = undefined
        this.temp.ruleName = data.ruleName + '_copy'
        this.temp.rekebackA1 = data.rekebackA1
        this.temp.rekebackA2 = data.rekebackA2
        this.temp.rekebackA3 = data.rekebackA3
        this.temp.rekebackB1 = data.rekebackB1
        this.temp.rekebackB2 = data.rekebackB2
        this.temp.rekebackB3 = data.rekebackB3
        this.temp.rekebackB4 = data.rekebackB4
        this.temp.rekebackB5 = data.rekebackB5
        this.temp.rekebackB6 = data.rekebackB6
        this.temp.rekebackB7 = data.rekebackB7
        this.temp.rekebackB8 = data.rekebackB8
        this.temp.rekebackB9 = data.rekebackB9
        this.temp.rekebackB10 = data.rekebackB10
        this.temp.rekebackB11 = data.rekebackB11
        this.temp.rekebackB12 = data.rekebackB12
        this.temp.rekebackB13 = data.rekebackB13
        this.temp.rekebackB14 = data.rekebackB14
        this.temp.rekebackB15 = data.rekebackB15
        this.temp.rekebackB16 = data.rekebackB16
        this.temp.rekebackB17 = data.rekebackB17
        this.temp.rekebackB18 = data.rekebackB18
        this.temp.rekebackB19 = data.rekebackB19
        this.temp.rekebackB20 = data.rekebackB20
        this.temp.rekebackB21 = data.rekebackB21
        this.temp.rekebackB22 = data.rekebackB22
        this.temp.rekebackB23 = data.rekebackB23
        this.temp.rekebackB24 = data.rekebackB24
        this.temp.rekebackB25 = data.rekebackB25
        this.temp.rekebackB26 = data.rekebackB26
        this.temp.rekebackB27 = data.rekebackB27
        this.temp.rekebackB28 = data.rekebackB28
        this.temp.rekebackB29 = data.rekebackB29
        this.temp.rekebackB30 = data.rekebackB30
        this.temp.backup5 = data.backup5
        this.temp.remark = ''

        var datas3 = data.prodList
        this.temp.tradeProds = []
        for (var j = 0; j < datas3.length; j++) {
                  		var id = datas3[j].prodId
          this.temp.tradeProds.push(id)
        }
        var datas4 = data.groupList
        this.temp.userGroups = []
        for (var j = 0; j < datas4.length; j++) {
                  		var id = datas4[j].groupId
          this.temp.userGroups.push(id)
        }
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        ruleName: '',
        rekebackA1: '',
        rekebackA2: '',
        rekebackA3: '',
        rekebackB1: '',
        rekebackB2: '',
        rekebackB3: '',
        rekebackB4: '',
        rekebackB5: '',
        rekebackB6: '',
        rekebackB7: '',
        rekebackB8: '',
        rekebackB9: '',
        rekebackB10: '',
        rekebackB11: '',
        rekebackB12: '',
        rekebackB13: '',
        rekebackB14: '',
        rekebackB15: '',
        rekebackB16: '',
        rekebackB17: '',
        rekebackB18: '',
        rekebackB19: '',
        rekebackB20: '',
        rekebackB21: '',
        rekebackB22: '',
        rekebackB23: '',
        rekebackB24: '',
        rekebackB25: '',
        rekebackB26: '',
        rekebackB27: '',
        rekebackB28: '',
        rekebackB29: '',
        rekebackB30: '',
        backup5: 3,
        remark: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    qx() {
      this.$router.push({ path: '/tradeSetting/rekebackRule/' })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createRekebackRule(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })

              this.$router.push({
                name: 'RekebackRule',
                params: {
                  page: this.list_page,
                  ruleName: this.list_ruleName
                }
              })

              // this.$router.push({path: '/tradeSetting/rekebackRule/'});
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          updateRekebackRule(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })

              this.$router.push({
                name: 'RekebackRule',
                params: {
                  page: this.list_page,
                  ruleName: this.list_ruleName
                }
              })

								  // this.$router.push({path: '/tradeSetting/rekebackRule/'});
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeRekebackRule(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

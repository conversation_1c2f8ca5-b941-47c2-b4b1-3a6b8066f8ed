<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.userName" placeholder="CRM用户名称" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.crmAccount" placeholder="CRM账号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tradeId" placeholder="交易账号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.orderId" placeholder="订单号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.bankName" placeholder="开户行" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.bankNum" placeholder="银行卡号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.accountName" placeholder="开户姓名" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.mobile" placeholder="预留手机号" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="CRM用户名称" prop="userName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="CRM账号" prop="crmAccount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.crmAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易账号" prop="tradeId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tradeId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="出金金额" prop="amount" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>${{ scope.row.amount }}(￥{{ scope.row.rateRmb }})</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="auditStatus" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditStatus==0?'待审核':scope.row.auditStatus==1?'审核通过':'驳回' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单号" prop="orderId" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.orderId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开户行" prop="bankName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="银行卡号" prop="bankNum" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开户姓名" prop="accountName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.accountName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预留手机号" prop="mobile" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column label="付款账户" prop="backup6" min-width="400px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup6 }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchWithdrawList, fetchFundInfo, createFundInfo, updateFundInfo, updateIsAvailable, removeFundInfo } from '@/api/fundInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'FundInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        userId: undefined,
        userName: undefined,
        crmAccount: undefined,
        tradeId: undefined,
        depositBankId: undefined,
        withdrawBankId: undefined,
        auditStatus: undefined,
        operStatus: undefined,
        auditId: undefined,
        orderId: undefined,
        bankName: undefined,
        bankNum: undefined,
        accountName: undefined,
        mobile: undefined,
        bankAddress: undefined
      },
      temp: {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        userId: [
        ],
        userName: [
        ],
        crmAccount: [
        ],
        tradeId: [
        ],
        amount: [
        ],
        depositBankId: [
        ],
        withdrawBankId: [
        ],
        type: [
        ],
        allocationAmount: [
        ],
        auditStatus: [
        ],
        operStatus: [
        ],
        auditId: [
        ],
        orderId: [
        ],
        bankName: [
        ],
        bankNum: [
        ],
        accountName: [
        ],
        mobile: [
        ],
        bankAddress: [
        ],
        remark: [
        ],
        annex: [
        ],
        fee: [
        ],
        actualAmount: [
        ],
        originalAmount: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ],
        rate: [
        ],
        rateRmb: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchWithdrawList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        userId: '',
        userName: '',
        crmAccount: '',
        tradeId: '',
        amount: '',
        depositBankId: '',
        withdrawBankId: '',
        allocationAmount: '',
        auditStatus: '',
        operStatus: '',
        auditId: '',
        orderId: '',
        bankName: '',
        bankNum: '',
        accountName: '',
        mobile: '',
        bankAddress: '',
        remark: '',
        annex: '',
        fee: '',
        actualAmount: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateFundInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeFundInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

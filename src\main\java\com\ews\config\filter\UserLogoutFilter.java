package com.ews.config.filter;

import javax.servlet.ServletContext;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.SessionException;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.LogoutFilter;

/**
 * 自定义用于管理后台shiro的退出
 * <AUTHOR>
 *
 */
public class UserLogoutFilter extends LogoutFilter  {
	  
	@Override  
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {  
		System.out.println("自定义 退出filter");
        //在这里执行退出系统前需要清空的数据  
        Subject subject=SecurityUtils.getSubject(); 
        String redirectUrl="/login";  
        
        
        ServletContext context= request.getServletContext();  
        try {  
            subject.logout();  
            context.removeAttribute("error");  
        }catch (SessionException e){  
            e.printStackTrace();  
        }  
        issueRedirect(request,response,redirectUrl);  
        return false;  
    }

}

package com.ews.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.ews.common.Result;
import com.ews.crm.entity.WebContent;
import com.ews.system.entity.User;
import com.ews.system.repository.WebContentRepository;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;
import com.ews.system.service.WebContentService;



@Service
public class WebContentServiceImpl implements WebContentService 
{
	@Autowired
	private WebContentRepository webContentRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<WebContent> findAll(Integer page, Integer size,String sortName,String sortOrder, WebContent webContent) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<WebContent> pages = webContentRepository.findAll(new Specification<WebContent>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<WebContent> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(webContent.getContentTitle())) { 
                predicates.add(criteriaBuilder.like(root.get("contentTitle").as(String.class),"%"+webContent.getContentTitle()+"%"));
             }
             if(!StringUtils.isEmpty(webContent.getContentType())) { 
                predicates.add(criteriaBuilder.equal(root.get("contentType").as(Integer.class), webContent.getContentType()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public WebContent findById(Long id) {
      Optional<WebContent> op = webContentRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.webContentRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         WebContent old = webContentRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 webContentRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 webContentRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.webContentRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(WebContent webContent) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (webContent.getId()== null) {
            	webContent.setGmtCreate(new Date());
            	webContent.setGmtModified(new Date());
            	webContent.setIsDeleted(0);
            	if(webContent.getIsAvailable() == null) {
            		webContent.setIsAvailable(1);
            	}
            	webContent.setUserCreate(loginUser.getUserId());
	    	} else {
            	webContent.setGmtModified(new Date());
        	}
            webContentRepository.save(webContent);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



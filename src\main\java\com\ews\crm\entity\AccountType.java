package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "account_type")
public class AccountType implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *类型名称
    **/
	@Column(name = "type_name")
	protected  String typeName;

    /**
    *MT组名称
    **/
	@Column(name = "group_name")
	protected  String groupName;

    /**
    *前台显示
    **/
	@Column(name = "is_show")
	protected  Integer isShow;

    /**
    *信用类型
    **/
	@Column(name = "open_type")
	protected  Integer openType;

    /**
    *信用值
    **/
	@Column(name = "credit_value")
	protected  Double creditValue;

    /**
    *杠杆信息
    **/
	@Column(name = "lever_info")
	protected  String leverInfo;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;
	
	
	@Column(name = "is_auto_audit1")
	protected  Integer isAutoAudit1;
	
	@Column(name = "is_auto_audit2")
	protected  Integer isAutoAudit2;
	
	@Column(name = "max_accounts")
	protected  Integer max_Accounts;
	
	@Column(name = "country_show")
	protected  String countryShow;


	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getTypeName()
    {
        return typeName;
    }
    public void setTypeName(String  typeName)
    {
        this.typeName = typeName;
    }
    public String  getGroupName()
    {
        return groupName;
    }
    public void setGroupName(String  groupName)
    {
        this.groupName = groupName;
    }
    public Integer  getIsShow()
    {
        return isShow;
    }
    public void setIsShow(Integer  isShow)
    {
        this.isShow = isShow;
    }
    public Integer  getOpenType()
    {
        return openType;
    }
    public void setOpenType(Integer  openType)
    {
        this.openType = openType;
    }
    public Double  getCreditValue()
    {
        return creditValue;
    }
    public void setCreditValue(Double  creditValue)
    {
        this.creditValue = creditValue;
    }
    public String  getLeverInfo()
    {
        return leverInfo;
    }
    public void setLeverInfo(String  leverInfo)
    {
        this.leverInfo = leverInfo;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }
	public Integer getIsAutoAudit1() {
		return isAutoAudit1;
	}
	public void setIsAutoAudit1(Integer isAutoAudit1) {
		this.isAutoAudit1 = isAutoAudit1;
	}
	public Integer getIsAutoAudit2() {
		return isAutoAudit2;
	}
	public void setIsAutoAudit2(Integer isAutoAudit2) {
		this.isAutoAudit2 = isAutoAudit2;
	}
	public Integer getMax_Accounts() {
		return max_Accounts;
	}
	public void setMax_Accounts(Integer max_Accounts) {
		this.max_Accounts = max_Accounts;
	}
	public String getCountryShow() {
		return countryShow;
	}
	public void setCountryShow(String countryShow) {
		this.countryShow = countryShow;
	}
    
    
    

}

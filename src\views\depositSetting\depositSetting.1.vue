<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次最低入金" prop="firstDepositMin" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.firstDepositMin }}</span>
        </template>
      </el-table-column>
      <el-table-column label="首次最高入金" prop="firstDepositMax" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.firstDepositMax }}</span>
        </template>
      </el-table-column>
      <el-table-column label="再次最低入金" prop="againDepositMin" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.againDepositMin }}</span>
        </template>
      </el-table-column>
      <el-table-column label="再次最高入金" prop="againDepositMax" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.againDepositMax }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费模式" prop="depositFeeType" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.depositFeeType }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="首次最低入金" prop="firstDepositMin">
          <el-input v-model="temp.firstDepositMin" />
        </el-form-item>
        <el-form-item label="首次最高入金" prop="firstDepositMax">
          <el-input v-model="temp.firstDepositMax" />
        </el-form-item>
        <el-form-item label="再次最低入金" prop="againDepositMin">
          <el-input v-model="temp.againDepositMin" />
        </el-form-item>
        <el-form-item label="再次最高入金" prop="againDepositMax">
          <el-input v-model="temp.againDepositMax" />
        </el-form-item>
        <el-form-item label="手续费模式" prop="depositFeeType">
          <el-select v-model="temp.depositFeeType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="depositFeeType in depositFeeTypes"
              :key="depositFeeType.value"
              :label="depositFeeType.label"
              :value="depositFeeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手续费比例" prop="feeProportion">
          <el-input v-model="temp.feeProportion" />
        </el-form-item>
        <el-form-item label="手续费金额" prop="feeAmount">
          <el-input v-model="temp.feeAmount" />
        </el-form-item>
        <el-form-item label="最低手续费" prop="feeMin">
          <el-input v-model="temp.feeMin" />
        </el-form-item>
        <el-form-item label="最高手续费" prop="feeMax">
          <el-input v-model="temp.feeMax" />
        </el-form-item>
        <el-form-item label="入金通知形式" prop="noticeType">
          <el-select v-model="temp.noticeType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="noticeType in noticeTypes"
              :key="noticeType.value"
              :label="noticeType.label"
              :value="noticeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知邮箱" prop="noticeEmail">
          <el-input v-model="temp.noticeEmail" />
        </el-form-item>
        <el-form-item label="通知手机号" prop="noticeMobile">
          <el-input v-model="temp.noticeMobile" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="首次最低入金" prop="firstDepositMin">
          <el-input v-model="temp.firstDepositMin" />
        </el-form-item>
        <el-form-item label="首次最高入金" prop="firstDepositMax">
          <el-input v-model="temp.firstDepositMax" />
        </el-form-item>
        <el-form-item label="再次最低入金" prop="againDepositMin">
          <el-input v-model="temp.againDepositMin" />
        </el-form-item>
        <el-form-item label="再次最高入金" prop="againDepositMax">
          <el-input v-model="temp.againDepositMax" />
        </el-form-item>
        <el-form-item label="手续费模式" prop="depositFeeType">
          <el-select v-model="temp.depositFeeType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="depositFeeType in depositFeeTypes"
              :key="depositFeeType.value"
              :label="depositFeeType.label"
              :value="depositFeeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手续费比例" prop="feeProportion">
          <el-input v-model="temp.feeProportion" />
        </el-form-item>
        <el-form-item label="手续费金额" prop="feeAmount">
          <el-input v-model="temp.feeAmount" />
        </el-form-item>
        <el-form-item label="最低手续费" prop="feeMin">
          <el-input v-model="temp.feeMin" />
        </el-form-item>
        <el-form-item label="最高手续费" prop="feeMax">
          <el-input v-model="temp.feeMax" />
        </el-form-item>
        <el-form-item label="入金通知形式" prop="noticeType">
          <el-select v-model="temp.noticeType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="noticeType in noticeTypes"
              :key="noticeType.value"
              :label="noticeType.label"
              :value="noticeType.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知邮箱" prop="noticeEmail">
          <el-input v-model="temp.noticeEmail" />
        </el-form-item>
        <el-form-item label="通知手机号" prop="noticeMobile">
          <el-input v-model="temp.noticeMobile" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchDepositSetting, createDepositSetting, updateDepositSetting, updateIsAvailable, removeDepositSetting } from '@/api/depositSetting'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'DepositSettingTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      },
      temp: {
        id: undefined,
        firstDepositMin: '',
        firstDepositMax: '',
        againDepositMin: '',
        againDepositMax: '',
        depositFeeType: '',
        feeProportion: '',
        feeAmount: '',
        feeMin: '',
        feeMax: '',
        noticeType: '',
        noticeEmail: '',
        noticeMobile: ''
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
				 depositFeeTypes: [
        {
          value: 1,
          label: '比例'
        },
        {
          value: 2,
          label: '固定'
        }
      ],
				 noticeTypes: [
        {
          value: 1,
          label: '邮件'
        },
        {
          value: 2,
          label: '手机号'
        }
      ],
      rules: {
        firstDepositMin: [
          { required: true, message: '首次最低入金不能为空', trigger: 'change' },,
        ],
        firstDepositMax: [
          { required: true, message: '首次最高入金不能为空', trigger: 'change' },,
        ],
        againDepositMin: [
          { required: true, message: '再次最低入金不能为空', trigger: 'change' },,
        ],
        againDepositMax: [
          { required: true, message: '再次最高入金不能为空', trigger: 'change' },,
        ],
        depositFeeType: [
          { required: true, message: '手续费模式不能为空', trigger: 'change' },,
        ],
        feeProportion: [
        ],
        feeAmount: [
        ],
        feeMin: [
        ],
        feeMax: [
        ],
        noticeType: [
          { required: true, message: '入金通知形式不能为空', trigger: 'change' },,
        ],
        noticeEmail: [
        ],
        noticeMobile: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        firstDepositMin: '',
        firstDepositMax: '',
        againDepositMin: '',
        againDepositMax: '',
        depositFeeType: '',
        feeProportion: '',
        feeAmount: '',
        feeMin: '',
        feeMax: '',
        noticeType: '',
        noticeEmail: '',
        noticeMobile: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createDepositSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateDepositSetting(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeDepositSetting(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

package com.ews.crm.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.ews.crm.entity.ThirdPartyPayment;
import com.ews.crm.repository.ThirdPartyPaymentRepository;
import com.ews.crm.service.ThirdPartyPaymentService;
import com.ews.common.DateUtil;
import com.ews.common.Result;
import com.ews.system.entity.User; 
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@Service
public class ThirdPartyPaymentServiceImpl implements ThirdPartyPaymentService 
{
	@Autowired
	private ThirdPartyPaymentRepository thirdPartyPaymentRepository;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;


    @Override
    public Page<ThirdPartyPayment> findAll(Integer page, Integer size,String sortName,String sortOrder, ThirdPartyPayment thirdPartyPayment) {
      Sort sortLocal = new Sort(sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC: Sort.Direction.DESC,sortName);
      Pageable pageable = PageRequest.of(page,size,sortLocal);
      Page<ThirdPartyPayment> pages = thirdPartyPaymentRepository.findAll(new Specification<ThirdPartyPayment>(){
        private static final long serialVersionUID = 1L;
        @Override
        public Predicate toPredicate(Root<ThirdPartyPayment> root, CriteriaQuery<?> query,
           CriteriaBuilder criteriaBuilder) {
             List<Predicate> predicates = new ArrayList<>();
             if(!StringUtils.isEmpty(thirdPartyPayment.getApiName())) { 
                predicates.add(criteriaBuilder.like(root.get("apiName").as(String.class),"%"+thirdPartyPayment.getApiName()+"%"));
             }
             if(!StringUtils.isEmpty(thirdPartyPayment.getRequestUrl())) { 
                predicates.add(criteriaBuilder.like(root.get("requestUrl").as(String.class),"%"+thirdPartyPayment.getRequestUrl()+"%"));
             }
             if(!StringUtils.isEmpty(thirdPartyPayment.getRequestType())) { 
                predicates.add(criteriaBuilder.equal(root.get("requestType").as(Integer.class), thirdPartyPayment.getRequestType()));
             }
             if(!StringUtils.isEmpty(thirdPartyPayment.getSignType())) { 
                predicates.add(criteriaBuilder.equal(root.get("signType").as(Integer.class), thirdPartyPayment.getSignType()));
             }
             predicates.add(criteriaBuilder.equal(root.get("isDeleted").as(Integer.class), 0));//过滤掉已经删除的记录
             query.where(criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()])));
             return query.getRestriction();
           }
        },pageable);
      return pages;
    }


    @Override
    public ThirdPartyPayment findById(Long id) {
      if(id == null) {
      	return null;
      }
      Optional<ThirdPartyPayment> op = thirdPartyPaymentRepository.findById(id);
      if (op.isPresent()) {
      	return op.get();
      }
      return null;
    }


    @Override
    @Transactional
    public Result removeEntityOfLogicalById(Long id) {
      Result vr = new Result();
      if(this.thirdPartyPaymentRepository.existsByIdAndIsDeleted(id, 0)) {//判断当前数据是否存在
         ThirdPartyPayment old = thirdPartyPaymentRepository.findById(id).get();
	     old.setIsDeleted(1);
		 old.setGmtModified(new Date());
		 thirdPartyPaymentRepository.save(old);
		 vr.setCode(Result.CODESUCCESS);
	  }else {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(1);
		 vr.setMessage("操作的数据不存在");
	  }
	  return vr;
	}


    @Override
    @Transactional
    public Result removeEntityById(Long id) {
      Result vr = new Result();
      try {
		 thirdPartyPaymentRepository.deleteById(id);
		 vr.setCode(Result.CODESUCCESS);
	  } catch (Exception e) {
		 vr.setCode(Result.CODEFAIL);
		 vr.setErrType(0);
		 vr.setMessage(e.getMessage());
         e.printStackTrace();
	  }
	  return vr;
	}


	@Override
	@Transactional
	public Result updateIsAvailableById(Long Id, Integer isAvailable) {
      Result vr = new Result();
      try {
           if (this.thirdPartyPaymentRepository.updateIsAvailableById(Id, isAvailable) == 1) {
		         vr.setCode(Result.CODESUCCESS);
		   }else{
				 vr.setCode(Result.CODEFAIL);
		 		 vr.setErrType(1);
		 		 vr.setMessage("操作失败");
		   }
	   } catch (Exception e) {
		   e.printStackTrace();
		   vr.setCode(Result.CODEFAIL);
		   vr.setErrType(0);
		   vr.setMessage(e.getMessage());
	   }
	  return vr;
	}


    @Override
    @Transactional
    public Result saveOrUpdate(ThirdPartyPayment thirdPartyPayment) {
        User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        if(loginUser==null) {
        	return null;
        }
        Result vr = new Result();
		try {
        	if (thirdPartyPayment.getId()== null) {
            	thirdPartyPayment.setGmtCreate(new Date());
            	thirdPartyPayment.setGmtModified(new Date());
            	thirdPartyPayment.setIsDeleted(0);
            	if(thirdPartyPayment.getIsAvailable() == null) {
            		thirdPartyPayment.setIsAvailable(1);
            	}
            	thirdPartyPayment.setUserCreate(loginUser.getUserId());
	    	} else {
            	thirdPartyPayment.setGmtModified(new Date());
        	}
            thirdPartyPaymentRepository.save(thirdPartyPayment);
            vr.setCode(Result.CODESUCCESS);
		}catch(Exception e){
            e.printStackTrace();
            vr.setCode(Result.CODEFAIL);
            vr.setMessage(e.getMessage());
            vr.setErrType(0);
		}		return vr;
    }
}



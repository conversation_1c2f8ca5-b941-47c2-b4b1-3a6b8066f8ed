package com.ews.crm.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.ews.crm.repository.BaseNativeSqlRepository;
import com.ews.crm.service.DataSqlService;



@Service
public class DataSqlServiceImpl extends BaseNativeSqlRepository implements DataSqlService 
{
	
	/**
	 * 根据代理ID、用户组、交易产品查询返佣金额
	 */
	public List queryRekeBackAmount(Long userID,String groupName,String prodName) {
		String querySql=" SELECT DISTINCT  " + 
				" IFNULL(rekeback_rule.rekeback_a1,0), " + 
				" IFNULL(rekeback_rule.rekeback_a2,0), " + 
				" IFNULL(rekeback_rule.rekeback_a3,0), " + 
				" IFNULL(rekeback_rule.rekeback_b1,0), " + 
				" IFNULL(rekeback_rule.rekeback_b2,0), " + 
				" IFNULL(rekeback_rule.rekeback_b3,0), " + 
				" IFNULL(rekeback_rule.rekeback_b4,0), " + 
				" IFNULL(rekeback_rule.rekeback_b5,0), " + 
				" IFNULL(rekeback_rule.rekeback_b6,0), " + 
				" IFNULL(rekeback_rule.rekeback_b7,0), " + 
				" IFNULL(rekeback_rule.rekeback_b8,0), " + 
				" IFNULL(rekeback_rule.rekeback_b9,0), " + 
				" IFNULL(rekeback_rule.rekeback_b10,0), " + 
				" IFNULL(rekeback_rule.rekeback_b11,0), " + 
				" IFNULL(rekeback_rule.rekeback_b12,0), " + 
				" IFNULL(rekeback_rule.rekeback_b13,0), " + 
				" IFNULL(rekeback_rule.rekeback_b14,0), " + 
				" IFNULL(rekeback_rule.rekeback_b15,0), " + 
				" IFNULL(rekeback_rule.rekeback_b16,0), " + 
				" IFNULL(rekeback_rule.rekeback_b17,0), " + 
				" IFNULL(rekeback_rule.rekeback_b18,0), " + 
				" IFNULL(rekeback_rule.rekeback_b19,0), " + 
				" IFNULL(rekeback_rule.rekeback_b20,0), " + 
				" IFNULL(rekeback_rule.rekeback_b21,0), " + 
				" IFNULL(rekeback_rule.rekeback_b22,0), " + 
				" IFNULL(rekeback_rule.rekeback_b23,0), " + 
				" IFNULL(rekeback_rule.rekeback_b24,0), " + 
				" IFNULL(rekeback_rule.rekeback_b25,0), " + 
				" IFNULL(rekeback_rule.rekeback_b26,0), " + 
				" IFNULL(rekeback_rule.rekeback_b27,0), " + 
				" IFNULL(rekeback_rule.rekeback_b28,0), " + 
				" IFNULL(rekeback_rule.rekeback_b29,0), " + 
				" IFNULL(rekeback_rule.rekeback_b30,0), " + 
				" rule_user_map.user_id,  " + 
				" trade_prod.prod_name, " + 
				" user_group.group_name " + 
				" FROM rule_user_map  " + 
				" LEFT JOIN  rekeback_rule ON rekeback_rule.id=rule_user_map.rule_id " + 
				" LEFT JOIN  rule_prod_map ON rule_prod_map.rule_id=rekeback_rule.id " + 
				" LEFT JOIN  rule_group_map ON rule_group_map.rule_id=rekeback_rule.id " + 
				" LEFT JOIN trade_prod ON rule_prod_map.prod_id=trade_prod.id " + 
				" LEFT JOIN user_group ON rule_group_map.group_id=user_group.id " + 
				" WHERE rule_user_map.user_id="+userID+" AND user_group.group_name='"+groupName.replace("\\","\\\\")+"' AND trade_prod.prod_name='"+prodName+"' ";
		
		
		
		//System.out.println(querySql);
		List list=this.nativeSqlList(querySql);
		return list;
	}
	
	
	public List getMyTradeObj(Long user_id,String tradeID) {
		
		String trade_str="";
		if(!tradeID.equals("-1")) {
			 trade_str=" AND  order_info.login_id='"+tradeID+"'  ";
		}
		
		String querySql=" SELECT " + 
				" order_info.symbol, " + 
				" IFNULL(SUM(CASE WHEN order_info.profit>=0 THEN order_info.profit ELSE 0 END),0) AS hl , " + 
				" IFNULL(SUM(CASE WHEN order_info.profit<0 THEN abs(order_info.profit) ELSE 0 END),0) AS ks , " + 
				" COUNT(order_info.symbol) " + 
				" FROM order_info  " + 
				" INNER JOIN trade_account ON trade_account.id=order_info.trade_id " + 
				" INNER JOIN user_info ON user_info.id=trade_account.user_id  " + 
				" WHERE user_info.id="+user_id+" "+trade_str+" AND (order_info.type=1 OR order_info.type=0 ) AND order_info.status=2 " + 
				" GROUP BY order_info.symbol " + 
				" ORDER BY COUNT(order_info.symbol) DESC " + 
				" LIMIT  5 ";
		List list=this.nativeSqlList(querySql);
		return list;
	}
	public List getProfitList(Long user_id,String tradeID) {
		
		String trade_str="";
		if(!tradeID.equals("-1")) {
			 trade_str=" AND  order_info.login_id='"+tradeID+"'  ";
		}
		
		String querySql=" SELECT FROM_UNIXTIME(close_time,'%Y-%m-%d'),SUM(order_info.profit) FROM order_info " + 
				" INNER JOIN trade_account ON trade_account.id=order_info.trade_id  " + 
				" INNER JOIN user_info ON user_info.id=trade_account.user_id  " + 
				" WHERE (order_info.type=1 OR order_info.type=0) AND user_info.id="+user_id+" AND order_info.status=2" + trade_str+
				" GROUP BY FROM_UNIXTIME(close_time,'%Y-%m-%d')" + 
				" ORDER BY FROM_UNIXTIME(close_time,'%Y-%m-%d') DESC" + 
				" LIMIT  30  ";
		
		List list=this.nativeSqlList(querySql);
		return list;
	}
	
	
	public List getImportCrmUserList() {
		String querySql=" SELECT Email,lang,Birth,Phone,Citizenship,ID_Number,Country_of_Residence,Address,Password,Given_Name_FirstName,Surname_LastName FROM crm_info3 " ;
		List list=this.nativeSqlList(querySql);
		return list;
	}
	
	
	public List getTradeAndCrmUserBindInfoList() {
		String querySql=" SELECT Login,Email,TypeID FROM tradeinfo3 " ;
		List list=this.nativeSqlList(querySql);
		return list;
	}

	public List getAgentAndCrmUserBindInfoList(){
		String querySql=" SELECT agent_email,crm_email,agent_name,agent_tel,agent_loginid FROM crm_agent " ;
		List list=this.nativeSqlList(querySql);
		
		return list;
	}
	
	public Double getFundTotal(Long crm_id,int type) {
		String querySql=" SELECT IFNULL(SUM(amount),0) FROM fund_info WHERE user_id="+crm_id+" AND audit_status=1 AND type="+type ;
		List list=this.nativeSqlList(querySql);
		Double ft=0d;
		if(list.size()>0) {
			Object obj=(Object)list.get(0);
			ft=new Double(obj.toString());
		}
		return ft;
	}
	
}



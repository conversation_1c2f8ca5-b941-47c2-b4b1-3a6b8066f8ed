package com.ews.system.service.impl;

import org.springframework.stereotype.Service;

import com.ews.system.entity.RolePermission;
import com.ews.system.repository.RolePermissionRepository;
import com.ews.system.service.RolePermissionService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

@Service
public class RolePermissionServiceImpl implements RolePermissionService 
{
	@Autowired
	private RolePermissionRepository rolePermissionRepository;

	@Override
	public List<RolePermission> findByRoleId(Long roleId) {
		// TODO Auto-generated method stub
		return this.rolePermissionRepository.findByRoleId(roleId);
	}

	@Override
	public List<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId) {
		// TODO Auto-generated method stub
		return this.rolePermissionRepository.findByRoleIdAndPermissionId(roleId, permissionId);
	}

	@Override
	public void deleteByRoleId(Long roleId) {
		this.rolePermissionRepository.removeByRoleId(roleId);
	}
	
}

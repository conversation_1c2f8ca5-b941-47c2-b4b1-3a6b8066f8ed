package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "request_record")
public class RequestRecord implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *订单ID
    **/
	@Column(name = "order_id")
	protected  String orderId;

    /**
    *sign
    **/
	@Column(name = "sign_str")
	protected  String signStr;

    /**
    *数量
    **/
	@Column(name = "amount")
	protected  Double amount;

    /**
    *账号
    **/
	@Column(name = "account")
	protected  String account;

    /**
    *状态（0 待处理  1 成功  2 失败）
    **/
	@Column(name = "status")
	protected  Integer status;

    /**
    *第三方流水号
    **/
	@Column(name = "third_no")
	protected  String thirdNo;

    /**
    *返回数据
    **/
	@Column(name = "return_str")
	protected  String returnStr;

    /**
    *回调数据
    **/
	@Column(name = "recall_str")
	protected  String recallStr;

    /**
    *入金ID
    **/
	@Column(name = "despoit_id")
	protected  Long despoitId;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public String  getOrderId()
    {
        return orderId;
    }
    public void setOrderId(String  orderId)
    {
        this.orderId = orderId;
    }
    public String  getSignStr()
    {
        return signStr;
    }
    public void setSignStr(String  signStr)
    {
        this.signStr = signStr;
    }
    public Double  getAmount()
    {
        return amount;
    }
    public void setAmount(Double  amount)
    {
        this.amount = amount;
    }
    public String  getAccount()
    {
        return account;
    }
    public void setAccount(String  account)
    {
        this.account = account;
    }
    public Integer  getStatus()
    {
        return status;
    }
    public void setStatus(Integer  status)
    {
        this.status = status;
    }
    public String  getThirdNo()
    {
        return thirdNo;
    }
    public void setThirdNo(String  thirdNo)
    {
        this.thirdNo = thirdNo;
    }
    public String  getReturnStr()
    {
        return returnStr;
    }
    public void setReturnStr(String  returnStr)
    {
        this.returnStr = returnStr;
    }
    public String  getRecallStr()
    {
        return recallStr;
    }
    public void setRecallStr(String  recallStr)
    {
        this.recallStr = recallStr;
    }
    public Long  getDespoitId()
    {
        return despoitId;
    }
    public void setDespoitId(Long  despoitId)
    {
        this.despoitId = despoitId;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

package com.wx.core.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.ConnectException;
import java.net.URL;
import java.net.URLEncoder;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wx.core.pojo.Token;





/**
 * 通用工具类
 * 
 * <AUTHOR>
 * @date 2015-06-02
 */

public class CommonUtil {

	private static Logger log = LoggerFactory.getLogger(CommonUtil.class);



	// 凭证获取（GET）

	public final static String token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";

	// 微信扫码登录
	public final static String token_url1 = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=APPSECRET&code=CODE&grant_type=authorization_code";

	// 获取用户信息
	public final static String userinfo_url = "https://api.weixin.qq.com/sns/userinfo?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN";

	//微信授权
	public final static String code_url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE#wechat_redirect";

	//获取js调用授权票据接口
	public final static String ticket_url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi";

	//下载微信临时素材接口
	public final static String remote_media_url = "https://api.weixin.qq.com/cgi-bin/media/get?access_token=ACCESS_TOKEN&media_id=MEDIA_ID";

	/**
	 * 发送https请求
	 * 
	 * @param requestUrl 请求地址
	 * @param requestMethod 请求方式（GET、POST）
	 * @param outputStr 提交的数据
	 * @return JSONObject(通过JSONObject.get(key)的方式获取json对象的属性值)
	 */

	public static JSONObject httpsRequest(String requestUrl, String requestMethod, String outputStr) {

		JSONObject jsonObject = null;

		try {

			// 创建SSLContext对象，并使用我们指定的信任管理器初始化

			TrustManager[] tm = { new MyX509TrustManager() };

			SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");

			sslContext.init(null, tm, new java.security.SecureRandom());

			// 从上述SSLContext对象中得到SSLSocketFactory对象

			SSLSocketFactory ssf = sslContext.getSocketFactory();



			URL url = new URL(requestUrl);

			HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();

			conn.setSSLSocketFactory(ssf);

			

			conn.setDoOutput(true);

			conn.setDoInput(true);

			conn.setUseCaches(false);

			// 设置请求方式（GET/POST）

			conn.setRequestMethod(requestMethod);



			// 当outputStr不为null时向输出流写数据

			if (null != outputStr) {

				OutputStream outputStream = conn.getOutputStream();

				// 注意编码格式

				outputStream.write(outputStr.getBytes("UTF-8"));

				outputStream.close();

			}



			// 从输入流读取返回内容

			InputStream inputStream = conn.getInputStream();

			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");

			BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

			String str = null;

			StringBuffer buffer = new StringBuffer();

			while ((str = bufferedReader.readLine()) != null) {

				buffer.append(str);

			}



			// 释放资源

			bufferedReader.close();

			inputStreamReader.close();

			inputStream.close();

			inputStream = null;

			conn.disconnect();

			jsonObject =(JSONObject) JSON.parse(buffer.toString());

		} catch (ConnectException ce) {

			System.out.println("连接超时：{}"+ ce);

		} catch (Exception e) {

			System.out.println("https请求异常：{}"+ e);

		}

		return jsonObject;

	}

	/**
	 * 获取临时code
	 * @param appid
	 * @return
	 */
	public static String getCode(String appid){
		//https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&state=STATE#wechat_redirect

		System.out.println("获取临时code");
		String requestUrl = code_url.replace("APPID", appid)
				.replace("REDIRECT_URI", URLEncoder.encode("http://weixin.everwings.cn/usermanage/weixinCallBack.do?qr=true"))
				.replace("SCOPE","snsapi_userinfo");
		return requestUrl;
	}

	/**
	 * 获取接口访问凭证
	 * 
	 * @param appid 凭证
	 * @param appsecret 密钥
	 * @return
	 */

	public static Token getToken(String appid, String appsecret) {

		Token token = null;

		String requestUrl = token_url.replace("APPID", appid).replace("APPSECRET", appsecret);

		// 发起GET请求获取凭证

		JSONObject jsonObject = httpsRequest(requestUrl, "GET", null);

		System.out.println(jsonObject.toString());


		if (null != jsonObject) {

			try {

				token = new Token();

				token.setAccessToken(jsonObject.getString("access_token"));

				token.setExpiresIn(jsonObject.getIntValue("expires_in"));

			} catch (Exception e) {

				token = null;

				// 获取token失败

				
				
				log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getIntValue("errcode"), jsonObject.getString("errmsg"));

			}

		}

		return token;

	}

	/**
	 * 调用微信JS接口的临时票据
	 *
	 * @param access_token 接口访问凭证
	 * @return
	 */
	public static String getJsApiTicket(String access_token) {
		String url = ticket_url;
		String requestUrl = url.replace("ACCESS_TOKEN", access_token);
		// 发起GET请求获取凭证
		JSONObject jsonObject = httpsRequest(requestUrl, "GET", null);
		String ticket = null;
		if (null != jsonObject) {
			try {
				System.out.println("#!@:"+jsonObject.toString());
				ticket = jsonObject.getString("ticket");
			} catch (Exception e) {
				// 获取token失败
				log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getIntValue("errcode"), jsonObject.getString("errmsg"));
			}
		}
		return ticket;
	}
	/**
	 * 微信扫码登录获取token
	 *
	 * @param appid 凭证
	 * @param appsecret 密钥
	 * @return
	 */

	public static Token getToken_login(String appid, String appsecret, String code) {

		Token token = null;

		String requestUrl = token_url1.replace("APPID", appid).replace("APPSECRET", appsecret).replace("CODE",code);

		System.out.printf(requestUrl);

		// 发起GET请求获取凭证

		JSONObject jsonObject = httpsRequest(requestUrl, "GET", null);

		System.out.println("jsonObject:"+jsonObject);
		
		if (null != jsonObject) {

			try {

				token = new Token();

				token.setAccessToken(jsonObject.getString("access_token"));

				token.setOpenid(jsonObject.getString("openid"));
			//	token.setUnionid(jsonObject.getString("unionid"));

				//token.setExpiresIn(jsonObject.getInt("expires_in"));

			} catch (Exception e) {

				token = null;

				// 获取token失败

				//log.error("获取token失败 errcode:{} errmsg:{}", jsonObject.getInt("errcode"), jsonObject.getString("errmsg"));

			}

		}

		return token;

	}
	/**

	 * URL编码（utf-8）

	 * 

	 * @param source

	 * @return

	 */

	public static String urlEncodeUTF8(String source) {

		String result = source;

		try {

			result = java.net.URLEncoder.encode(source, "utf-8");

		} catch (UnsupportedEncodingException e) {

			e.printStackTrace();

		}

		return result;

	}

	

	/**

	 * 根据内容类型判断文件扩展名

	 * 

	 * @param contentType 内容类型

	 * @return

	 */

	public static String getFileExt(String contentType) {

		String fileExt = "";

		if ("image/jpeg".equals(contentType))

			fileExt = ".jpg";

		else if ("audio/mpeg".equals(contentType))

			fileExt = ".mp3";

		else if ("audio/amr".equals(contentType))

			fileExt = ".amr";

		else if ("video/mp4".equals(contentType))

			fileExt = ".mp4";

		else if ("video/mpeg4".equals(contentType))

			fileExt = ".mp4";

		return fileExt;

	}

}
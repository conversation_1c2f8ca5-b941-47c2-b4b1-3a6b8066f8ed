package com.ews.crm.controller;


import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.HttpClient;
import com.ews.common.HttpUtils;
import com.ews.common.MyWebsocketClient4Register;
import com.ews.common.MyWebsocketClient4TradeAccount;
import com.ews.common.MyWebsocketClient4TradeAccount2;
import com.ews.common.RandomStrUtil;
import com.ews.common.SendCloudAPIV2;
import com.ews.common.SendEmail;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.AccountLeverMap;
import com.ews.crm.entity.AccountType;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.entity.Countries;
import com.ews.crm.entity.DepositBank;
import com.ews.crm.entity.DepositSetting;
import com.ews.crm.entity.EmailInfo;
import com.ews.crm.entity.ExchangeRate;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.Lever;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.PaymentParams;
import com.ews.crm.entity.RequestRecord;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.ThirdPartyPayment;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TradeObj;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserBank;
import com.ews.crm.entity.UserGroup;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.entity.WebContent;
import com.ews.crm.entity.WithdrawalBank;
import com.ews.crm.entity.WithdrawalSetting;
import com.ews.crm.service.AccountLeverMapService;
import com.ews.crm.service.AccountTypeService;
import com.ews.crm.service.CompanyInfoService;
import com.ews.crm.service.CountriesService;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.DepositBankService;
import com.ews.crm.service.DepositSettingService;
import com.ews.crm.service.EmailInfoService;
import com.ews.crm.service.ExchangeRateService;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.LeverService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.PaymentParamsService;
import com.ews.crm.service.RequestRecordService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.ThirdPartyPaymentService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserBankService;
import com.ews.crm.service.UserGroupService;
import com.ews.crm.service.UserInfoService;
import com.ews.crm.service.WithdrawalBankService;
import com.ews.crm.service.WithdrawalSettingService;
import com.ews.model.Applicant;
import com.ews.model.HttpMethod;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;
import com.ews.system.service.WebContentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wx.Config;
import com.wx.core.util.HttpClientUtil;
import com.wx.core.util.VerifyCodeUtil;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;



@Controller
@RequestMapping("/trade")
public class WebIndexController {
	
    public static final String SUMSUB_SECRET_KEY = "dBbalrmABk6dZEKApaeqROh2KR7iBYqg"; 
    public static final String SUMSUB_APP_TOKEN = "prd:yLXmlxzPKr0syXBV8fZTRZ25.5IZS0E0D2lDcpihCYEkAmjWJ1S9NUecQ"; 
    public static final String SUMSUB_TEST_BASE_URL = "https://api.sumsub.com";
    private static final ObjectMapper objectMapper = new ObjectMapper();
	
	
	@Autowired
	private CompanyInfoService companyInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private EmailInfoService emailInfoService;
	
	@Autowired
	private AccountTypeService accountTypeService;
	
	@Autowired
	private AccountLeverMapService accountLeverMapService;
	@Autowired
	private LeverService leverService;
	
	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private TradeAccountService tradeAccountService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private DepositBankService depositBankService;
	
	@Autowired
	private DepositSettingService depositSettingService;
	
	@Autowired
	private ExchangeRateService exchangeRateService;
	
	@Autowired
	private FundInfoService fundInfoService;
	
	@Autowired
	private WithdrawalSettingService withdrawalSettingService;
	
	@Autowired
	private UserBankService userBankService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private WithdrawalBankService withdrawalBankService;
	
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private WebContentService webContentService;
	
	
	@Autowired
	private ThirdPartyPaymentService thirdPartyPaymentService;
	
	@Autowired
	private PaymentParamsService paymentParamsService;
	
	
	@Autowired
	private RequestRecordService requestRecordService;
	
	@Autowired
	private CountriesService countriesService;
	
	@Autowired
	private UserGroupService userGroupService;

	
	/**
	 * 登录页
	 * @return
	 */
	@RequestMapping("/")
	public String defaut(Map<String, Object> map, HttpServletRequest request) {
		
		
	     map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		Locale locale=request.getLocale();
		String Lang=locale.getLanguage();
		
		
		if(request.getParameter("lang")!=null&&!request.getParameter("lang").equals("")) {
			Lang=request.getParameter("lang");
		}

		System.out.println("language="+Lang); 
		// 简体 zh  繁体 zh  英语 en   泰语 th  越南语 vi 印度尼西亚语 in    马来语 ms
		
		if(Lang.equals("zh")) {
			request.getSession().setAttribute("i18n_language_session","zh_CN");
		}else if(Lang.equals("en")) {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}else if(Lang.equals("th")) {
			request.getSession().setAttribute("i18n_language_session","th_TH");
		}else if(Lang.equals("vi")) {
			request.getSession().setAttribute("i18n_language_session","vi_VN");
		}else if(Lang.equals("in")) {
			request.getSession().setAttribute("i18n_language_session","in_ID");
		}else if(Lang.equals("ms")) {
			request.getSession().setAttribute("i18n_language_session","ms_MY");
		}else if(Lang.equals("ja")) {
			request.getSession().setAttribute("i18n_language_session","ja_JP");
		}else if(Lang.equals("tw")) {
			request.getSession().setAttribute("i18n_language_session","zh_TW");
		}else if(Lang.equals("tw")) {
			request.getSession().setAttribute("i18n_language_session","zh_TW");
		}else {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}
    	 return "/login";
	}
	
	/**
	 * 注册页-基础信息
	 * @return
	 */
	@RequestMapping("/register")
	public String register(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		Locale locale=request.getLocale();
		String Lang=locale.getLanguage();
		if(request.getParameter("lang")!=null&&!request.getParameter("lang").equals("")) {
			Lang=request.getParameter("lang");
		}
		System.out.println("language="+Lang); 
		if(Lang.equals("zh")) {
			request.getSession().setAttribute("i18n_language_session","zh_CN");
		}else if(Lang.equals("en")) {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}else if(Lang.equals("th")) {
			request.getSession().setAttribute("i18n_language_session","th_TH");
		}else if(Lang.equals("vi")) {
			request.getSession().setAttribute("i18n_language_session","vi_VN");
		}else if(Lang.equals("in")) {
			request.getSession().setAttribute("i18n_language_session","in_ID");
		}else if(Lang.equals("ms")) {
			request.getSession().setAttribute("i18n_language_session","ms_MY");
		}else if(Lang.equals("ja")) {
			request.getSession().setAttribute("i18n_language_session","ja_JP");
		}else if(Lang.equals("tw")) {
			request.getSession().setAttribute("i18n_language_session","zh_TW");
		}else {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}
		
		
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		 if(request.getParameter("tj")!=null&&!request.getParameter("tj").equals("")) {
			 map.put("tjcode", request.getParameter("tj"));
		 }
		
    	 return "/register";
	}
	/**
	 * 发送验证码
	 */
	@RequestMapping("/sendCodeByRegister")
	public void sendCodeByRegister(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		StringBuilder code = new StringBuilder();
		Random random = new Random();
		// 生成4位验证码
		for (int i = 0; i < 4; i++) {
			code.append(String.valueOf(random.nextInt(10)));
		}
		if (request.getParameter("memberName") != null && !request.getParameter("memberName").equals("")) {
			UserInfo query_userInfo=new UserInfo();
			query_userInfo.setUserName(request.getParameter("memberName"));
			Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);

			if (userInfo_page.getContent().size()>0) {
				response.getWriter().print("isHave");
			} else {
				try {
					EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
					CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
					SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your OTP for Email Verification",code.toString(),"","",emailInfo.getBackup1(),emailInfo.getFromEmail(),emailInfo.getEmailName(),request.getParameter("memberName"),"","");
					response.getWriter().print(code.toString());
				} catch (Exception e) {
					e.printStackTrace();
					response.getWriter().print(false);
				}
			}
			
		}
		
	}
	/**
	 * 保存基本信息
	 */
	@RequestMapping("/saveUserInfoStep1")
	public void saveUserInfoStep1(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		if (request.getParameter("memberName") != null && !request.getParameter("memberName").equals("")) {
			UserInfo query_userInfo=new UserInfo();
			query_userInfo.setUserName(request.getParameter("memberName"));
			Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);

			if (userInfo_page.getContent().size()>0) {
				response.getWriter().print("isHave");
			}else {
				UserInfo userInfo=new UserInfo();
				if(request.getParameter("memberName")!=null&&!request.getParameter("memberName").equals("")) {
					userInfo.setUserName(request.getParameter("memberName"));
				}
				if(request.getParameter("memberPsd")!=null&&!request.getParameter("memberPsd").equals("")) {
						userInfo.setPassword(request.getParameter("memberPsd"));	
				}
				if(request.getParameter("invitationCode")!=null&&!request.getParameter("invitationCode").equals("")) {
					userInfo.setInvitationCode(request.getParameter("invitationCode"));
				}
				if(request.getParameter("planguage")!=null&&!request.getParameter("planguage").equals("")) {
					userInfo.setBackup1(request.getParameter("planguage"));	
			    }
				userInfo.setIsAvailable(0);
				userInfo.setIsAgent(1);
				userInfo.setBackup5("[]");
			    try {
			    	
			    	if(userInfo.getInvitationCode()!=null&&!userInfo.getInvitationCode().equals("")) {
			    		User user_query=new User();
			    		user_query.setSaleSerial(userInfo.getInvitationCode());
			    		Page<User> user_page=this.userService.findAll(0,1,"userId","asc", user_query);
			    		if(user_page.getContent().size()>0) {
			    			userInfo.setParentId(user_page.getContent().get(0).getUserId());
			    		}
			    		
			    	}
			      this.userInfoService.saveOrUpdate(userInfo);
			      
			         EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
			         DepositSetting ds=(DepositSetting)this.depositSettingService.findById(1L);
			         try {
			         SendEmail.sendSimpleMail(emailInfo.getApiUser(),emailInfo.getApiKey(),emailInfo.getFromEmail(), ds.getNoticeEmail(), "open account application", "user email："+userInfo.getUserName());
			         }catch(Exception e) {
			         	System.out.println(e);
			         }
			    }catch(Exception e) {
			    	System.out.println(e);
			    }
				
				 this.userInfoService.saveOrUpdate(userInfo);
				   response.getWriter().print(true);
				
				
			}
		}else {
			response.getWriter().print(false);
		}
		
	
		
	}
	/**
	 * 注册页-完善资料
	 * @return
	 */
	@RequestMapping("/replenishMaterial")
	public String replenishMaterial(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		
    	 return "/replenishMaterial";
	}
	
	
	/**
	 * 保存资料信息
	 */
	@RequestMapping("/saveUserInfoStep2")
	public void saveUserInfoStep2(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("registerUserInfo")!=null) {
			
			   UserInfo userInfo=(UserInfo)session.getAttribute("registerUserInfo");
			   if(request.getParameter("surname")!=null&&!request.getParameter("surname").equals("")) {
					userInfo.setSurname(request.getParameter("surname"));
				}else {
					userInfo.setSurname("");
				}
				if(request.getParameter("name")!=null&&!request.getParameter("name").equals("")) {
						userInfo.setName(request.getParameter("name"));	
				}else {
					userInfo.setName("");	
				}
				if(request.getParameter("gender")!=null&&!request.getParameter("gender").equals("")) {
					userInfo.setGender(new Integer(request.getParameter("gender")));
				}
				if(request.getParameter("country")!=null&&!request.getParameter("country").equals("")) {
					userInfo.setCountry(request.getParameter("country"));
				}
				if(request.getParameter("province")!=null&&!request.getParameter("province").equals("")) {
					userInfo.setProvince(request.getParameter("province"));
				}
				if(request.getParameter("tel")!=null&&!request.getParameter("tel").equals("")) {
					userInfo.setTel(request.getParameter("tel"));	
			    }
				if(request.getParameter("city")!=null&&!request.getParameter("city").equals("")) {
						userInfo.setCity(request.getParameter("city"));	
				}
				if(request.getParameter("adress")!=null&&!request.getParameter("adress").equals("")) {
					userInfo.setAdress(request.getParameter("adress"));
				}
				if(request.getParameter("identityNum")!=null&&!request.getParameter("identityNum").equals("")) {
					userInfo.setIdentityNum(request.getParameter("identityNum"));
				}
				if(request.getParameter("imageFront")!=null&&!request.getParameter("imageFront").equals("")) {
						userInfo.setImageFront(request.getParameter("imageFront"));	
				}
				if(request.getParameter("imageBack")!=null&&!request.getParameter("imageBack").equals("")) {
					userInfo.setImageBack(request.getParameter("imageBack"));
				}
				userInfo.setFullname(userInfo.getSurname()+userInfo.getName());
				
				
			
			    session.setAttribute("registerUserInfo", userInfo);
			    response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
		
	}
	/**
	 * 注册页-交易信息
	 * @return
	 */
	@RequestMapping("/replenishTradeInfo")
	public String replenishTradeInfo(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 AccountType accountType_query=new AccountType();
		 accountType_query.setIsShow(1);
		 Page<AccountType> accountType_page=this.accountTypeService.findAll(0,1000,"id","desc", accountType_query);
		 
		
		 map.put("accountTypeList", accountType_page.getContent());
		 
		
    	 return "/replenishTradeInfo";
	}
	/**
	 * 根据用户类型获取交易杠杆
	 */
	@RequestMapping("/getLeverByUserType")
	public void getLeverByUserType(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		 AccountLeverMap accountLeverMap_query=new AccountLeverMap();
		 accountLeverMap_query.setAccountTypeId(new Long(request.getParameter("userTypeID")));
         Page<AccountLeverMap> accountLeverMap_pages = accountLeverMapService.findAll(0,1000, "id","asc", accountLeverMap_query);
         List accountLeverMapList=new ArrayList();
         AccountType aty=(AccountType)this.accountTypeService.findById(new Long(request.getParameter("userTypeID")));
         for(int i=0;i<accountLeverMap_pages.getContent().size();i++) {
        	 AccountLeverMap alm=(AccountLeverMap)accountLeverMap_pages.getContent().get(i);
        	 Lever lever=(Lever)this.leverService.findById(alm.getLeverId());
        	 if(aty.getCreditValue().doubleValue()>0) {
        		 lever.setBackup1("true");
        	 }else {
        		 lever.setBackup1("false");
        	 }
        	 accountLeverMapList.add(lever);
         }
         response.getWriter().print(JSON.toJSONString(accountLeverMapList));
	}
	
	/**
	 * 保存交易信息
	 */
	@RequestMapping("/saveUserInfoStep3")
	public void saveUserInfoStep3(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("registerUserInfo")!=null) {
			
			    UserInfo userInfo=(UserInfo)session.getAttribute("registerUserInfo");
			  
				if(request.getParameter("userType")!=null&&!request.getParameter("userType").equals("")) {
					userInfo.setUserType(new Long(request.getParameter("userType")));
				}
				if(request.getParameter("backup1")!=null&&!request.getParameter("backup1").equals("")) {
					userInfo.setBackup1(request.getParameter("backup1"));
				}
			    session.setAttribute("registerUserInfo", userInfo);
			    response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
	}
	
	/**
	 * 注册页-签订协议
	 * @return
	 */
	@RequestMapping("/treaty")
	public String treaty(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		
    	 return "/treaty";
	}
	
	/**
	 * 保存交易信息
	 */
	@RequestMapping("/saveUserInfoStep4")
	public void saveUserInfoStep4(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("registerUserInfo")!=null) {
			
			    UserInfo userInfo=(UserInfo)session.getAttribute("registerUserInfo");
			    userInfo.setIsAvailable(2);
			    try {
			    	
			    	if(userInfo.getInvitationCode()!=null&&!userInfo.getInvitationCode().equals("")) {
			    		User user_query=new User();
			    		user_query.setSaleSerial(userInfo.getInvitationCode());
			    		Page<User> user_page=this.userService.findAll(0,1,"userId","asc", user_query);
			    		if(user_page.getContent().size()>0) {
			    			userInfo.setParentId(user_page.getContent().get(0).getUserId());
			    		}
			    		
			    	}
			      this.userInfoService.saveOrUpdate(userInfo);
			    }catch(Exception e) {
			    	System.out.println(e);
			    }
			    session.setAttribute("registerUserInfo",null);
			    session.removeAttribute("registerUserInfo");
			    response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
	}
	
	/**
	 * 保存交易信息
	 */
	@RequestMapping("/saveUserInfoStep5")
	public void saveUserInfoStep5(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("registerUserInfo")!=null) {
			
			    UserInfo userInfo=(UserInfo)session.getAttribute("registerUserInfo");
			    
			    if(request.getParameter("userType")!=null&&!request.getParameter("userType").equals("")) {
					userInfo.setUserType(new Long(request.getParameter("userType")));
				}
				if(request.getParameter("backup1")!=null&&!request.getParameter("backup1").equals("")) {
					userInfo.setBackup1(request.getParameter("backup1"));
				}
			    userInfo.setIsAvailable(2);
			    try {
			    	
			    	if(userInfo.getInvitationCode()!=null&&!userInfo.getInvitationCode().equals("")) {
			    		User user_query=new User();
			    		user_query.setSaleSerial(userInfo.getInvitationCode());
			    		Page<User> user_page=this.userService.findAll(0,1,"userId","asc", user_query);
			    		if(user_page.getContent().size()>0) {
			    			userInfo.setParentId(user_page.getContent().get(0).getUserId());
			    		}
			    		
			    	}
			      this.userInfoService.saveOrUpdate(userInfo);
			      
			         EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
			         DepositSetting ds=(DepositSetting)this.depositSettingService.findById(1L);
			         try {
			         SendEmail.sendSimpleMail(emailInfo.getApiUser(),emailInfo.getApiKey(),emailInfo.getFromEmail(), ds.getNoticeEmail(), "开户申请", "待审核用户邮箱："+userInfo.getUserName()+";用户姓名："+userInfo.getFullname());
			         }catch(Exception e) {
			         	System.out.println(e);
			         }
			    }catch(Exception e) {
			    	System.out.println(e);
			    }
			    session.setAttribute("registerUserInfo",null);
			    session.removeAttribute("registerUserInfo");
			    response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
	}
	
	/**
	 * 注册页-注册完成
	 * @return
	 */
	@RequestMapping("/registerComplete")
	public String registerComplete(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		 
    	 return "/registerComplete";
	}
	/**
	 * 登陆验证
	 */
	@RequestMapping("/login")
	public void login(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		Locale locale=request.getLocale();
		String Lang=locale.getLanguage();

		if(request.getParameter("lang")!=null&&!request.getParameter("lang").equals("")) {
			Lang=request.getParameter("lang");
		}
		System.out.println("language="+Lang); 
		if(Lang.equals("zh")) {
			request.getSession().setAttribute("i18n_language_session","zh_CN");
		}else if(Lang.equals("en")) {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}else if(Lang.equals("th")) {
			request.getSession().setAttribute("i18n_language_session","th_TH");
		}else if(Lang.equals("vi")) {
			request.getSession().setAttribute("i18n_language_session","vi_VN");
		}else if(Lang.equals("in")) {
			request.getSession().setAttribute("i18n_language_session","in_ID");
		}else if(Lang.equals("ms")) {
			request.getSession().setAttribute("i18n_language_session","ms_MY");
		}else if(Lang.equals("ja")) {
			request.getSession().setAttribute("i18n_language_session","ja_JP");
		}else if(Lang.equals("tw")) {
			request.getSession().setAttribute("i18n_language_session","zh_TW");
		}else {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}
		
		HttpSession session=request.getSession();
		   
		if(request.getParameter("j_username")!=null&&request.getParameter("j_passowrd")!=null) {
			
			
			
			
			if(request.getParameter("code")!=null&&!request.getParameter("code").equals("")) {
				
				System.out.println(session.getAttribute("verifyCode").toString());
				System.out.println(request.getParameter("code"));
				if(session.getAttribute("verifyCode")!=null&&session.getAttribute("verifyCode").toString().equals(request.getParameter("code")))
				{
					UserInfo ui_query=new UserInfo();
					ui_query.setUserName(request.getParameter("j_username").toString());
					ui_query.setPassword(request.getParameter("j_passowrd").toString());
					//ui_query.setIsAvailable(1);
					
					Page<UserInfo> userInfo_page=this.userInfoService.findAll(0, 1, "id", "asc", ui_query);
					if(userInfo_page.getContent().size()>0) {
						UserInfo ui=userInfo_page.getContent().get(0);
						
						if(ui.getIsAgent()!=null&&ui.getIsAgent().intValue()==1) {
							response.getWriter().print(true);
							request.getSession().setAttribute("loginUser", (UserInfo)userInfo_page.getContent().get(0));
						}else {
							response.getWriter().print("freeze");
						
					    }	
						}else {
						response.getWriter().print(false);
					    }
				}else {
					response.getWriter().print("codeerror");
				}
				
				
				
			}else {
				response.getWriter().print("codeerror");
					
			}
			
		}else {
			response.getWriter().print(false);
		}
	}
	
	/**
	 * 首页
	 */
	@RequestMapping("main")
	public String main(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		
		if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
			map.put("trade_id", request.getParameter("trade_id"));
		}else {
			map.put("trade_id", "-1");
		}
		Page<WebContent> page_wc=this.webContentService.findAll(0, 1, "id", "desc", new WebContent());
		
          if(page_wc.getContent().size()>0) {
			
			map.put("webcontent", page_wc.getContent().get(0));
			
			if(request.getSession().getAttribute("webcontent"+page_wc.getContent().get(0).getId())!=null) {
				map.put("isroad","true");
			}else {
				map.put("isroad","false");
				request.getSession().setAttribute("webcontent"+page_wc.getContent().get(0).getId(), "");
			}
		}else {
			map.put("isroad","true");
			map.put("webcontent", new WebContent());
		}
		
		
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		 
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser"); 
		     map.put("userInfo", this.userInfoService.findById(myInfo.getId()));
		    
		    if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
		    	map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
		    }else {
		    	map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
		    }
		    
		    
		    if(myInfo.getBackup1().equals("zh-cn")) {
				request.getSession().setAttribute("i18n_language_session","zh_CN");
			}else if(myInfo.getBackup1().equals("en")) {
				request.getSession().setAttribute("i18n_language_session","en_US");
			}else if(myInfo.getBackup1().equals("th")) {
				request.getSession().setAttribute("i18n_language_session","th_TH");
			}else if(myInfo.getBackup1().equals("vi")) {
				request.getSession().setAttribute("i18n_language_session","vi_VN");
			}else if(myInfo.getBackup1().equals("in")) {
				request.getSession().setAttribute("i18n_language_session","in_ID");
			}else if(myInfo.getBackup1().equals("ms")) {
				request.getSession().setAttribute("i18n_language_session","ms_MY");
			}else if(myInfo.getBackup1().equals("ja")) {
				request.getSession().setAttribute("i18n_language_session","ja_JP");
			}else if(myInfo.getBackup1().equals("zh-tw")) {
				request.getSession().setAttribute("i18n_language_session","zh_TW");
			}else {
				request.getSession().setAttribute("i18n_language_session","en_US");
			}
		    
		    Double totalBalance=0.0d;
		    
		    //查询当前CRM用户下所有的交易账号
		    TradeAccount ta_query1=new TradeAccount();
		    ta_query1.setUserId(myInfo.getId());
		    ta_query1.setIsAvailable(1);
		  
		    Page<TradeAccount> ta_page1=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query1);
		    
		    for(int i=0;i<ta_page1.getContent().size();i++) {
		    	TradeAccount ta=(TradeAccount)ta_page1.getContent().get(i);
		    	if(ta.getType()!=null) {
		    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
		    	ta.setBackup3(aty.getTypeName());
		    	
		    	 UserGroup ug_query=new UserGroup();
				  ug_query.setGroupName(ta.getGroupName());
				  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
				  if(page.getContent().size()>0) {
					UserGroup ug_t=(UserGroup)page.getContent().get(0);
					
					if(ug_t.getBackup1().equals("USC")) {
						totalBalance=totalBalance+(ta.getBalance3()/100);
						
					}else {
						totalBalance=totalBalance+ta.getBalance3();
					}
					
				  }else {
					  totalBalance=totalBalance+ta.getBalance3();
				  }
				  
		    	
		    	}
		    }
		    map.put("tradeList", ta_page1.getContent());
		    map.put("totalBalance", totalBalance);
		    
		    if(ta_page1.getContent().size()>0) {
		    	map.put("isHaveTrade", "1");
		    }else {
		    	map.put("isHaveTrade", "0");
		    }
		    
		    //账号情况
		    //查询所有出入金及信用情况并列出最新5条记录
		    
		    FundInfo fi_query =new FundInfo();
		    fi_query.setUserId(myInfo.getId());
		    fi_query.setAuditStatus(1);
		    fi_query.setOperStatus(1);
		    
		    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	fi_query.setTradeId(request.getParameter("trade_id"));
			}
		    
		    if(ta_page1.getContent().size()<=0) {
		    	fi_query.setTradeId("-9999TTT999999");
		    }
		    
		    Page<FundInfo> ui_page=this.fundInfoService.findAll(0,10000,"id","desc", fi_query);
		    List outList1=new ArrayList();
		    Double rj=0d;
		    Double cj=0d;
		    Double xy=0d;
		    for(int i=0;i<ui_page.getContent().size();i++) {
		    	FundInfo fi=ui_page.getContent().get(i);
		    	if(i<5) {
		    		outList1.add(fi);
		    	}
		    	if(fi.getType().intValue()==1) {//入金
		    		rj=rj+fi.getAmount().doubleValue();
                    if(fi.getAllocationAmount()!=null) {
                    	xy=xy+fi.getAllocationAmount();
                    }
		    		
		    	}else if(fi.getType().intValue()==2) {//出金
		    		cj=cj+fi.getAmount().doubleValue();
		    	}
		    	
		    }
		    map.put("crjList", outList1);
		    map.put("rj", rj);
		    map.put("cj", cj);
		    map.put("xy", xy);
		    
		    //盈亏情况
		    
		    String tradeID="-1";
		    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	tradeID=request.getParameter("trade_id");
			}
		    
		    if(ta_page1.getContent().size()<=0) {
		    	tradeID="-9999TTT999999";
		    }
		    List profitList=this.dataSqlService.getProfitList(myInfo.getId(),tradeID);
		    
		    List out_date_1=new ArrayList();
		    List out_profit_1=new ArrayList();
		    for(int i=profitList.size()-1;i>=0;i--) {
		    	Object[] obj=(Object[])profitList.get(i);
		    	out_date_1.add(obj[0].toString());
		    	out_profit_1.add(new Double(obj[1].toString()));
		    }
		    map.put("out_date", JSONArray.parseArray(JSON.toJSONString(out_date_1)));
		    map.put("out_profit", JSONArray.parseArray(JSON.toJSONString(out_profit_1)));
		    
		    
		    //交易情况
		    List tradeObjList=this.dataSqlService.getMyTradeObj(myInfo.getId(),tradeID);
		    List outList2=new ArrayList();
		    for(int i=0;i<tradeObjList.size();i++) {
		    	Object[] obj=(Object[])tradeObjList.get(i);
		    	
		    	TradeObj tob=new TradeObj();
		    	tob.setProfit(new Double(obj[1].toString()));
		    	tob.setLoft(new Double(obj[2].toString()));
		    	tob.setProduct(obj[0].toString());
		    	outList2.add(tob);
		    }
		    
		    map.put("yjqkStr", JSONArray.parseArray(JSON.toJSONString(outList2)));
		    
		    
		    //交易记录
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
		    OrderInfo order_query=new OrderInfo();
	    	order_query.setTradeList(ta_page.getContent());
	    	
	    	 if(ta_page1.getContent().size()<=0) {
	    		 TradeAccount taaaaa=new TradeAccount();
	    		 taaaaa.setId(-********L);
	    		 List lsisi=new ArrayList();
	    		 lsisi.add(taaaaa);
	    		 order_query.setTradeList(lsisi);
			    }
	    	 
	    	order_query.setStatus(2);
	    	List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	//ll.add(2);
	    	//ll.add(3);
	    	//ll.add(4);
	    	//ll.add(5);
	    	order_query.setTypeList(ll);
	    	
	    	
	    	if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	order_query.setLoginId(request.getParameter("trade_id"));
			}
	    	
	    	  if(ta_page1.getContent().size()<=0) {
			    	order_query.setLoginId("-9999TTT999999");
			  }
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,5, "id","desc", order_query);
		    map.put("jyjl", oi_page.getContent());
		    
		    
		  
		    
			 
			 AccountType accountType_query=new AccountType();
			 accountType_query.setIsShow(1);
			 Page<AccountType> accountType_page=this.accountTypeService.findAll(0,1000,"id","desc", accountType_query);
			 
			 List accountTypeList=new ArrayList();
			 //判断居住地可见
			// accountTypeList=accountType_page.getContent();  //全部
			 
			 for(int m=0;m<accountType_page.getContent().size();m++) {
				 
				 AccountType aType=(AccountType)accountType_page.getContent().get(m);
				 if(aType.getCountryShow()==null||aType.getCountryShow().equals("[]")) {
					 accountTypeList.add(aType);
				 }else {
					 if(myInfo.getCountry()!=null&&aType.getCountryShow().contains(myInfo.getCountry())){
						 accountTypeList.add(aType);
					 }
				 }
			 }
			 
			 map.put("accountTypeList", accountTypeList);
			 

			 Page<Countries> pages_countries = this.countriesService.findAll(0,500,"id","asc", new Countries());
			 
			 map.put("countries", pages_countries.getContent());
		  
    	    return "/main";
		}else {
			return "/login";
		}
	}
	
	
	
	/**
	 * fundTransfer
	 */
	@RequestMapping("fundTransfer")
	public String fundTransfer(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
			map.put("trade_id", request.getParameter("trade_id"));
		}else {
			map.put("trade_id", "-1");
		}
		Page<WebContent> page_wc=this.webContentService.findAll(0, 1, "id", "desc", new WebContent());
		
          if(page_wc.getContent().size()>0) {
			
			map.put("webcontent", page_wc.getContent().get(0));
			
			if(request.getSession().getAttribute("webcontent"+page_wc.getContent().get(0).getId())!=null) {
				map.put("isroad","true");
			}else {
				map.put("isroad","false");
				request.getSession().setAttribute("webcontent"+page_wc.getContent().get(0).getId(), "");
			}
		}else {
			map.put("isroad","true");
			map.put("webcontent", new WebContent());
		}
		
		
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		 
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser"); 
		     map.put("userInfo", this.userInfoService.findById(myInfo.getId()));
		    if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
		    	map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
		    	}else {
		    	map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
		    	}
		    Double totalBalance=0.0d;
		    
		    //查询当前CRM用户下所有的交易账号
		    TradeAccount ta_query1=new TradeAccount();
		    ta_query1.setUserId(myInfo.getId());
		    ta_query1.setIsAvailable(1);
		  
		    Page<TradeAccount> ta_page1=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query1);
		    
		    for(int i=0;i<ta_page1.getContent().size();i++) {
		    	TradeAccount ta=(TradeAccount)ta_page1.getContent().get(i);
		    	if(ta.getType()!=null) {
		    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
		    	ta.setBackup3(aty.getTypeName());
		    	 UserGroup ug_query=new UserGroup();
				  ug_query.setGroupName(ta.getGroupName());
				  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
				  if(page.getContent().size()>0) {
					UserGroup ug_t=(UserGroup)page.getContent().get(0);
					
					if(ug_t.getBackup1().equals("USC")) {
						totalBalance=totalBalance+(ta.getBalance3()/100);
						
					}else {
						totalBalance=totalBalance+ta.getBalance3();
					}
					
				  }else {
					  totalBalance=totalBalance+ta.getBalance3();
				  }
		    	}
		    }
		    map.put("tradeList", ta_page1.getContent());
		    map.put("totalBalance", totalBalance);
		    
		    
		    
		    
		    //账号情况
		    //查询所有出入金及信用情况并列出最新5条记录
		    
		    FundInfo fi_query =new FundInfo();
		    fi_query.setUserId(myInfo.getId());
		    fi_query.setAuditStatus(1);
		    fi_query.setOperStatus(1);
		    
		    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	fi_query.setTradeId(request.getParameter("trade_id"));
			}
		    
		    if(ta_page1.getContent().size()<=0) {
		    	fi_query.setTradeId("-9999TTT999999");
		    }
		    
		    Page<FundInfo> ui_page=this.fundInfoService.findAll(0,10000,"id","desc", fi_query);
		    List outList1=new ArrayList();
		    Double rj=0d;
		    Double cj=0d;
		    Double xy=0d;
		    for(int i=0;i<ui_page.getContent().size();i++) {
		    	FundInfo fi=ui_page.getContent().get(i);
		    	if(i<5) {
		    		outList1.add(fi);
		    	}
		    	if(fi.getType().intValue()==1) {//入金
		    		rj=rj+fi.getAmount().doubleValue();
                    if(fi.getAllocationAmount()!=null) {
                    	xy=xy+fi.getAllocationAmount();
                    }
		    		
		    	}else if(fi.getType().intValue()==2) {//出金
		    		cj=cj+fi.getAmount().doubleValue();
		    	}
		    	
		    }
		    map.put("crjList", outList1);
		    map.put("rj", rj);
		    map.put("cj", cj);
		    map.put("xy", xy);
		    
		    //盈亏情况
		    
		    String tradeID="-1";
		    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	tradeID=request.getParameter("trade_id");
			}
		    
		    if(ta_page1.getContent().size()<=0) {
		    	tradeID="-9999TTT999999";
		    }
		    List profitList=this.dataSqlService.getProfitList(myInfo.getId(),tradeID);
		    
		    List out_date_1=new ArrayList();
		    List out_profit_1=new ArrayList();
		    for(int i=profitList.size()-1;i>=0;i--) {
		    	Object[] obj=(Object[])profitList.get(i);
		    	out_date_1.add(obj[0].toString());
		    	out_profit_1.add(new Double(obj[1].toString()));
		    }
		    map.put("out_date", JSONArray.parseArray(JSON.toJSONString(out_date_1)));
		    map.put("out_profit", JSONArray.parseArray(JSON.toJSONString(out_profit_1)));
		    
		    
		    //交易情况
		    List tradeObjList=this.dataSqlService.getMyTradeObj(myInfo.getId(),tradeID);
		    List outList2=new ArrayList();
		    for(int i=0;i<tradeObjList.size();i++) {
		    	Object[] obj=(Object[])tradeObjList.get(i);
		    	
		    	TradeObj tob=new TradeObj();
		    	tob.setProfit(new Double(obj[1].toString()));
		    	tob.setLoft(new Double(obj[2].toString()));
		    	tob.setProduct(obj[0].toString());
		    	outList2.add(tob);
		    }
		    
		    map.put("yjqkStr", JSONArray.parseArray(JSON.toJSONString(outList2)));
		    
		    
		    //交易记录
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
		    OrderInfo order_query=new OrderInfo();
	    	order_query.setTradeList(ta_page.getContent());
	    	
	    	 if(ta_page1.getContent().size()<=0) {
	    		 TradeAccount taaaaa=new TradeAccount();
	    		 taaaaa.setId(-********L);
	    		 List lsisi=new ArrayList();
	    		 lsisi.add(taaaaa);
	    		 order_query.setTradeList(lsisi);
			    }
	    	 
	    	order_query.setStatus(2);
	    	List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	//ll.add(2);
	    	//ll.add(3);
	    	//ll.add(4);
	    	//ll.add(5);
	    	order_query.setTypeList(ll);
	    	
	    	
	    	if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("-1")) {
		    	order_query.setLoginId(request.getParameter("trade_id"));
			}
	    	
	    	  if(ta_page1.getContent().size()<=0) {
			    	order_query.setLoginId("-9999TTT999999");
			  }
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,5, "id","desc", order_query);
		    map.put("jyjl", oi_page.getContent());
		    
		    
		     AccountType accountType_query=new AccountType();
			 accountType_query.setIsShow(1);
			 Page<AccountType> accountType_page=this.accountTypeService.findAll(0,100,"id","desc", accountType_query);
			 map.put("accountTypeList", accountType_page.getContent());
		    

		  
    	    return "/fundTransfer";
		}else {
			return "/login";
		}
	}
	
	
	/**
	 * transfer
	 */
	@RequestMapping("transfer")
	public String transfer(Map<String, Object> map, HttpServletRequest request) {
		
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser"); 
		     map.put("userInfo", this.userInfoService.findById(myInfo.getId()));
			   if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
				   map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
				   }else {
				   map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
				   }
			   
			   Double totalBalance=0.0d;
			    
			    //查询当前CRM用户下所有的交易账号
			    TradeAccount ta_query1=new TradeAccount();
			    ta_query1.setUserId(myInfo.getId());
			    ta_query1.setIsAvailable(1);
			  
			    Page<TradeAccount> ta_page1=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query1);
			    List tradeList=new ArrayList();
			    
			    for(int i=0;i<ta_page1.getContent().size();i++) {
			    	TradeAccount ta=(TradeAccount)ta_page1.getContent().get(i);
			    	if(ta.getType()!=null) {
			    		
			    		System.out.println(ta.getGroupName());
			    		if(ta.getGroupName()!=null&&!ta.getGroupName().contains("demo")) {
			    			AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
					    	ta.setBackup3(aty.getTypeName());
					    	totalBalance=totalBalance+ta.getBalance3();
					    	tradeList.add(ta);
			    		}else {
			    		
			    		}
			    
			    	}
			    }
			    map.put("tradeList", tradeList);
			    map.put("totalBalance", totalBalance);
			    
			    
		  
    	    return "/transfer";
		}else {
			return "/login";
		}
	}
	
	
	
	/**
	 * deposit
	 */
	@RequestMapping("deposit")
	public String deposit(Map<String, Object> map, HttpServletRequest request) {
		
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser"); 
		     map.put("userInfo", this.userInfoService.findById(myInfo.getId()));
			   if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
				   map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
				   }else {
				   map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
				   }
			   
			 //查询当前CRM用户下所有的交易账号
			    TradeAccount ta_query=new TradeAccount();
			    ta_query.setUserId(myInfo.getId());
			    ta_query.setIsAvailable(1);
			    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			    
                 List tradeList=new ArrayList();
			    
			    for(int i=0;i<ta_page.getContent().size();i++) {
			    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
			    	if(ta.getType()!=null) {
			    		
			    		System.out.println(ta.getGroupName());
			    		if(ta.getGroupName()!=null&&!ta.getGroupName().contains("demo")) {
			    			AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
					    	ta.setBackup3(aty.getTypeName());
					    	tradeList.add(ta);
			    		}else {
			    		
			    		}
			    
			    	}
			    }
			    map.put("tradeList", tradeList);
			    
			    
			    Page<Countries> pages_countries = this.countriesService.findAll(0,500,"id","asc", new Countries());
				 
				 map.put("countries", pages_countries.getContent());
			   
		  
    	    return "/deposit";
		}else {
			return "/login";
		}
	}
	
	
	
	/**
	 * withdraw
	 */
	@RequestMapping("withdraw")
	public String withdraw(Map<String, Object> map, HttpServletRequest request) {
		
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser"); 
		     map.put("userInfo", this.userInfoService.findById(myInfo.getId()));
			   if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
				   map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
				   }else {
				   map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
				   }
			   
			   //查询当前CRM用户下所有的交易账号
			    TradeAccount ta_query=new TradeAccount();
			    ta_query.setUserId(myInfo.getId());
			    ta_query.setIsAvailable(1);
			    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			    
               List tradeList=new ArrayList();
			    
			    for(int i=0;i<ta_page.getContent().size();i++) {
			    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
			    	if(ta.getType()!=null) {
			    		
			    		System.out.println(ta.getGroupName());
			    		if(ta.getGroupName()!=null&&!ta.getGroupName().contains("demo")) {
			    			AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
					    	ta.setBackup3(aty.getTypeName());
					    	tradeList.add(ta);
			    		}else {
			    		
			    		}
			    
			    	}
			    }
			    map.put("tradeList", tradeList);
			    
			   
			    Page<Countries> pages_countries = this.countriesService.findAll(0,500,"id","asc", new Countries());
				 
				 map.put("countries", pages_countries.getContent());
			    
				 UserBank ub_query=new UserBank();
				 ub_query.setUserId(myInfo.getId());
				 Page<UserBank> ub_page=this.userBankService.findAll(0,100,"id","asc", ub_query);
				 if(ub_page.getContent().size()>0) {
				 map.put("mybank", ub_page.getContent().get(0));
				 }else {
					 map.put("mybank", new UserBank());
				 }
		  
				 
				 List bankList_mala=new ArrayList();
				 List bankList_thai=new ArrayList();
				 List bankList_vitn=new ArrayList();
				 List bankList_ind=new ArrayList();
				 bankList_mala.add("Affin Bank / Affin Islamic Bank");
				 bankList_mala.add("Alliance Bank");
				 bankList_mala.add("Ambank");
				 bankList_mala.add("Bank Islam Malaysia");
				 bankList_mala.add("Bank Rakyat");
				 bankList_mala.add("Bank Simpanan Nasional");
				 bankList_mala.add("CIMB Bank");
				 bankList_mala.add("CITI Bank");
				 bankList_mala.add("Hong Leong Bank");
				 bankList_mala.add("HSBC Bank Malaysia");
				 bankList_mala.add("Maybank");
				 bankList_mala.add("OCBC Bank (MALAYSIA)");
				 bankList_mala.add("Public Bank");
				 bankList_mala.add("RHB Bank");
				 bankList_mala.add("Standard Chartered Bank");
				 bankList_mala.add("United Overseas Bank");
				 
				 
				 bankList_thai.add("Bangkok Bank");
				 bankList_thai.add("Kasikorn Bank");
				 bankList_thai.add("Krung Thai Bank");
				 bankList_thai.add("TMB Tanachat Bank");
				 bankList_thai.add("Siam Commercial Bank");
				 bankList_thai.add("CITIBANK N.A.");
				 bankList_thai.add("Standard Chartered Bank (Thai)");
				 bankList_thai.add("CIMB Thai Bank");
				 bankList_thai.add("United Overseas Bank (Thai)");
				 bankList_thai.add("Bank of Ayudhya");
				 bankList_thai.add("Government Savings Bank");
				 bankList_thai.add("Government Housing Bank");
				 bankList_thai.add("Bank for Agriculture and Agricultural Cooperatives");
				 bankList_thai.add("Islamic Bank of Thailand");
				 bankList_thai.add("Tisco Bank");
				 bankList_thai.add("Kiatnakin Phatra Bank");
				 bankList_thai.add("Industrial and Commercial Bank of China (Thai)");
				 bankList_thai.add("Thai Credit Retail Bank");
				 bankList_thai.add("Land and Houses Bank");
				 bankList_thai.add("Thai Military Bank");
				 bankList_thai.add("Krungsri Bank");
				 bankList_thai.add("Hong Kong Shanghai Bank");
				 
				 
				 
				 
				 bankList_vitn.add("Techcom Bank");
				 bankList_vitn.add("Sacombank");
				 bankList_vitn.add("Vietcom Bank");
				 bankList_vitn.add("Asia Commercial Bank");
				 bankList_vitn.add("Dong A Bank");
				 bankList_vitn.add("Vietinbank");
				 bankList_vitn.add("BIDV Bank");
				 bankList_vitn.add("EximBank");
				 bankList_vitn.add("Agribank");
				 bankList_vitn.add("Vietnam International Bank");
				 bankList_vitn.add("Vietnam Prosperity Joint-Stock Commercial Bank");
				 bankList_vitn.add("Saigon Hanoi Commercial Joint Stock Bank");
				 bankList_vitn.add("AB Bank");
				 bankList_vitn.add("Military Commercial Joint Stock Bank");
				 bankList_vitn.add("Global Petro Bank");
				 bankList_vitn.add("Saigon Bank");
				 bankList_vitn.add("PG Bank");
				 bankList_vitn.add("Ocean Bank");
				 bankList_vitn.add("Nam A Commercial Joint Stock Bank");
				 bankList_vitn.add("Tien Phong Commercial");
				 bankList_vitn.add("Ho Chi Minh City Development Joint Stock Commercia");
				 bankList_vitn.add("VietNam Asia Commercial");
				 
				 
				 bankList_ind.add("Bank Central Asia");
				 bankList_ind.add("Bank Negara Indonesia");
				 bankList_ind.add("Bank Mandiri");
				 bankList_ind.add("Bank Rakyat Indonesia");
				 bankList_ind.add("Bank Artha Graha (Support VA Wallet Only)");
				 bankList_ind.add("Bank Bukopin (Support VA Wallet Only)");
				 bankList_ind.add("Bank Commonwealth (Support VA Wallet Only)");
				 bankList_ind.add("Bank Danamon (Support VA Wallet Only)");
				 bankList_ind.add("Bank Mega (Support VA Wallet Only)");
				 bankList_ind.add("Bank Maspion (Support VA Wallet Only)");
				 bankList_ind.add("Bank Mestika (Support VA Wallet Only)");
				 bankList_ind.add("Bank Panin (Support VA Wallet Only)");
				 bankList_ind.add("Bank Sumut (Support VA Wallet Only)");
				 bankList_ind.add("Bank Sinar Mas (Support VA Wallet Only)");
				 bankList_ind.add("Bank Btpn (Support VA Wallet Only)");
				 bankList_ind.add("CIMB Niaga (Support VA Wallet Only)");
				 bankList_ind.add("HSBC Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Maybank /bii (Support VA Wallet Only)");
				 bankList_ind.add("OCBC Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank Permata (Support VA Wallet Only)");
				 bankList_ind.add("UOB Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank BJB (Support VA Wallet Only)");
				 bankList_ind.add("Bank DKI (Support VA Wallet Only)");
				 bankList_ind.add("Bank BPD DIY (Support VA Wallet Only)");
				 bankList_ind.add("Bank Jateng (Support VA Wallet Only)");
				 bankList_ind.add("Bank Jatim (Support VA Wallet Only)");
				 bankList_ind.add("Bank Nagari (Support VA Wallet Only)");
				 bankList_ind.add("Bank Riau Kepri (Support VA Wallet Only)");
				 bankList_ind.add("Bank Sumselbabel (Support VA Wallet Only)");
				 bankList_ind.add("Bank Kalbar (Support VA Wallet Only)");
				 bankList_ind.add("Bank Kaltimtara (Support VA Wallet Only)");
				 bankList_ind.add("Bank Kalteng (Support VA Wallet Only)");
				 bankList_ind.add("Bank Sulselbar (Support VA Wallet Only)");
				 bankList_ind.add("Bank BPD Bali (Support VA Wallet Only)");
				 bankList_ind.add("Bank Maluku Malut (Support VA Wallet Only)");
				 bankList_ind.add("Bank Papua (Support VA Wallet Only)");
				 bankList_ind.add("Bank Banten (Support VA Wallet Only)");
				 bankList_ind.add("Bank BNP (Support VA Wallet Only)");
				 bankList_ind.add("Bank Muamalat (Support VA Wallet Only)");
				 bankList_ind.add("Bank Shinhan (Support VA Wallet Only)");
				 bankList_ind.add("Bank QNB Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank BTN (Support VA Wallet Only)");
				 bankList_ind.add("Bank Woori Saudara (Support VA Wallet Only)");
				 bankList_ind.add("Bank Jasa Jakarta (Support VA Wallet Only)");
				 bankList_ind.add("Bank KEB Hana Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank MNC (Support VA Wallet Only)");
				 bankList_ind.add("Bank SBI Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank Digital BCA (Blu) / Bank Royal (Support VA Wallet Only)");
				 bankList_ind.add("Bank National Nobu (Support VA Wallet Only)");
				 bankList_ind.add("Bank Ina Perdana (Support VA Wallet Only)");
				 bankList_ind.add("Prima Master Bank (Support VA Wallet Only)");
				 bankList_ind.add("Bank Sahabat Sampoerna (Support VA Wallet Only)");
				 bankList_ind.add("Bank Multiarta Sentosa (Support VA Wallet Only)");
				 bankList_ind.add("Bank Index Selindo (Support VA Wallet Only)");
				 bankList_ind.add("Bank Victoria (Support VA Wallet Only)");
				 bankList_ind.add("Bank Harda Internasional (Support VA Wallet Only)");
				 bankList_ind.add("IBK Bank Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank CTBC Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Citibank Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank CCB Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("MUFG Bank (Support VA Wallet Only)");
				 bankList_ind.add("Bank DBS Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Standard Chartered Bank (Support VA Wallet Only)");
				 bankList_ind.add("Bank Capital Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank of China Jakarta Branch (Support VA Wallet Only)");
				 bankList_ind.add("Bank Bumi Arta (Support VA Wallet Only)");
				 bankList_ind.add("Bank Rabobank (Support VA Wallet Only)");
				 bankList_ind.add("Bank Jtrust Indonesia (Support VA Wallet Only)");
				 bankList_ind.add("Bank Mayapada (Support VA Wallet Only)");
				 bankList_ind.add("Bank IDRK (Support VA Wallet Only)");
				 
				 
				
				 map.put("bankList_mala",bankList_mala);
				 map.put("bankList_thai",bankList_thai);
				 map.put("bankList_vitn",bankList_vitn);
				 map.put("bankList_ind",bankList_ind);

    	    return "/withdraw";
		}else {
			return "/login";
		}
	}
	
	
	
	
	/**
	 * profile
	 */
	@RequestMapping("profile")
	public String profile(Map<String, Object> map, HttpServletRequest request) {
		
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			
			 
			   UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
			   if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
				   map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
				   }else {
				   map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
				   }
			   
			   UserBank ub_query=new UserBank();
				 ub_query.setUserId(myInfo.getId());
				 Page<UserBank> ub_page=this.userBankService.findAll(0,100,"id","asc", ub_query);
				 if(ub_page.getContent().size()>0) {
				 map.put("mybank", ub_page.getContent().get(0));
				 }else {
					 map.put("mybank", new UserBank());
				 }
				 
				 UserInfo myInfo_new=this.userInfoService.findById(myInfo.getId());
				 map.put("userInfo", myInfo_new);
				 
				 Page<Countries> pages_countries = this.countriesService.findAll(0,500,"id","asc", new Countries());
				 
				 map.put("countries", pages_countries.getContent());
				 
				 
				 
				 if(request.getSession().getAttribute("kyc_token")!=null) {
					 
				 }else {
					 
					 if(myInfo_new.getIsAvailable().intValue()==2) {
					 try {
						 String externalUserId = getMD5(myInfo_new.getUserName());
						 String levelName = "basic-kyc-level";
						 String accessTokenStr = getAccessToken(externalUserId, levelName);
						 
						 JSONObject jsono=JSONObject.parseObject(accessTokenStr);
						 
						 System.out.println("token:"+jsono.getString("token"));
						 
						 if(jsono.getString("token")!=null&&!jsono.getString("token").equals("")) {
							 
							 request.getSession().setAttribute("kyc_token", jsono.getString("token"));
							 
							 if(myInfo_new.getBackup1().equals("zh-cn")||myInfo_new.getBackup1().equals("zh-tw")) {
								 request.getSession().setAttribute("kyc_lang", "zh"); 
							 }else {
								 request.getSession().setAttribute("kyc_lang", myInfo_new.getBackup1());
							 }
							 
							 request.getSession().setAttribute("kyc_email", myInfo_new.getUserName());
						 }
					 }catch(Exception e) {
						 
					 }
					 }
					 
				 }
		  
    	    return "/profile";
		}else {
			return "/login";
		}
	}
	
	
	/**
	 * saveProfile
	 */
	@RequestMapping("/saveProfile")
	public void saveProfile(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)session.getAttribute("loginUser");
			 
			 UserInfo newMyInfo=this.userInfoService.findById(myInfo.getId());
			 try {
				 
			 
			 if(request.getParameter("name")!=null&&!request.getParameter("name").equals("")) {
				 newMyInfo.setName(request.getParameter("name"));
			 }
			 
			 if(request.getParameter("surname")!=null&&!request.getParameter("surname").equals("")) {
				 newMyInfo.setSurname(request.getParameter("surname"));
			 }
			 
			 if(request.getParameter("birthday")!=null&&!request.getParameter("birthday").equals("")) {
				 SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
				 newMyInfo.setBirthday(format.parse(request.getParameter("birthday")));
			 }
			 
			 if(request.getParameter("province")!=null&&!request.getParameter("province").equals("")) {
				 newMyInfo.setProvince(request.getParameter("province"));
			 }
			 
			 if(request.getParameter("identityNum")!=null&&!request.getParameter("identityNum").equals("")) {
				 newMyInfo.setIdentityNum(request.getParameter("identityNum"));
			 }
			 
			 if(request.getParameter("country")!=null&&!request.getParameter("country").equals("")) {
				 newMyInfo.setCountry(request.getParameter("country"));
			 }
			 
			 if(request.getParameter("city")!=null&&!request.getParameter("city").equals("")) {
				 newMyInfo.setCity(request.getParameter("city"));
			 }
			 
			 if(request.getParameter("tel")!=null&&!request.getParameter("tel").equals("")) {
				 newMyInfo.setTel(request.getParameter("tel"));
			 }
			 
			 if(request.getParameter("adress")!=null&&!request.getParameter("adress").equals("")) {
				 newMyInfo.setAdress(request.getParameter("adress"));
			 }
			 
			 if(request.getParameter("imageFront")!=null&&!request.getParameter("imageFront").equals("")) {
				 newMyInfo.setImageFront(request.getParameter("imageFront"));
			 }
			 
			 if(request.getParameter("imageBack")!=null&&!request.getParameter("imageBack").equals("")) {
				 newMyInfo.setImageBack(request.getParameter("imageBack"));
			 }
			 
			 if(request.getParameter("backup3")!=null&&!request.getParameter("backup3").equals("")) {
				 newMyInfo.setBackup3(request.getParameter("backup3"));
			 }
			
			 newMyInfo.setFullname(newMyInfo.getName()+newMyInfo.getSurname());
			 newMyInfo.setIsAvailable(2);
			 this.userInfoService.saveOrUpdate(newMyInfo);
			 session.setAttribute("loginUser", newMyInfo);
			 }catch(Exception e) {
				 System.out.println(e);
			 }
			 newMyInfo.setIsAvailable(3);
			 this.userInfoService.saveOrUpdate(newMyInfo);
			 response.getWriter().print("none");
			 
			 //进入KYC验证环节
			/*
			 * try { String externalUserId = getMD5(newMyInfo.getUserName()); String
			 * levelName = "basic-kyc-level"; String accessTokenStr =
			 * getAccessToken(externalUserId, levelName);
			 * 
			 * JSONObject jsono=JSONObject.parseObject(accessTokenStr);
			 * 
			 * System.out.println("token:"+jsono.getString("token"));
			 * 
			 * if(jsono.getString("token")!=null&&!jsono.getString("token").equals("")) {
			 * 
			 * session.setAttribute("kyc_token", jsono.getString("token"));
			 * 
			 * if(newMyInfo.getBackup1().equals("zh-cn")||newMyInfo.getBackup1().equals(
			 * "zh-tw")) { session.setAttribute("kyc_lang", "zh"); }else {
			 * session.setAttribute("kyc_lang", newMyInfo.getBackup1()); }
			 * 
			 * session.setAttribute("kyc_email", newMyInfo.getUserName());
			 * 
			 * if(newMyInfo.getBackup4()!=null) {
			 * this.resetApplicants(newMyInfo.getBackup4()); }else { String applicantId =
			 * createApplicant(externalUserId,
			 * levelName,newMyInfo.getUserName(),newMyInfo.getTel());
			 * newMyInfo.setBackup4(applicantId); }
			 * this.userInfoService.saveOrUpdate(newMyInfo);
			 * session.setAttribute("loginUser", newMyInfo);
			 * 
			 * EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
			 * CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new
			 * Long(1L)); try {
			 * SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey()
			 * ,"My Profile Verification Link","","","",emailInfo.getBackup3(),emailInfo.
			 * getFromEmail(),emailInfo.getEmailName()
			 * ,newMyInfo.getUserName(),newMyInfo.getFullname(),""); }catch(Exception e) {
			 * System.out.println(e); }
			 * 
			 * response.getWriter().print(true); }else {
			 * 
			 * newMyInfo.setIsAvailable(3); this.userInfoService.saveOrUpdate(newMyInfo);
			 * response.getWriter().print("none"); } }catch(Exception e) {
			 * response.getWriter().print(false); }
			 */
			 
			}else {
				response.getWriter().print(false);
			}
	}
	
	
	/**
	 * kycverified
	 */
	@RequestMapping("kycverified")
	public String kycverified(Map<String, Object> map, HttpServletRequest request) {
		
		
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		 
		if(request.getSession().getAttribute("loginUser")!=null) {
			
			 map.put("kyc_email", request.getSession().getAttribute("kyc_email"));
			 map.put("kyc_lang", request.getSession().getAttribute("kyc_lang"));
			 map.put("kyc_token", request.getSession().getAttribute("kyc_token"));
			   
		  
    	    return "/kycverified";
		}else {
			return "/login";
		}
	}
	
	public static String getMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

	
	
	
	/**
	 * 交易账户
	 */
	@RequestMapping("tradeAccount")
	public String tradeAccount(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    if(!myInfo.getBackup1().equals("zh-cn")&&!myInfo.getBackup1().equals("zh-tw")&&!myInfo.getBackup1().equals("th")) {
		    	map.put("SupportCenter", Config.SUPPORT_URL+"en-us");
		    	}else {
		    	map.put("SupportCenter", Config.SUPPORT_URL+myInfo.getBackup1());
		    	}
		    
		    if(request.getParameter("accountinfo")!=null&&!request.getParameter("accountinfo").equals("")) {
		    	
		    	
		    	if(request.getParameter("accountinfo").split("_").length==3)
		    	{
		    		String account_id=request.getParameter("accountinfo").split("_")[1];
		    		 
		    		if(ismatches(account_id)) {
		    			 TradeAccount ta=this.tradeAccountService.findById(new Long(account_id));
		    			 if(ta!=null&&ta.getId()!=null) {
		    				 if(ta.getUserId().toString().equals(myInfo.getId().toString())) {
			    				 
			    				 if(ta.getType()!=null) {
			    				    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
			    				    	ta.setBackup3(aty.getTypeName());
			    				    	
			    				    	 map.put("accountType",aty.getId());
			    				    	}
			    				 
			    				    map.put("account",ta);
			    				 
			    				 
			    				
			    				    List profitList=this.dataSqlService.getProfitList(myInfo.getId(),ta.getTradeId());
			    				    
			    				    List out_date_1=new ArrayList();
			    				    List out_profit_1=new ArrayList();
			    				    for(int i=profitList.size()-1;i>=0;i--) {
			    				    	Object[] obj=(Object[])profitList.get(i);
			    				    	out_date_1.add(obj[0].toString());
			    				    	out_profit_1.add(new Double(obj[1].toString()));
			    				    }
			    				    map.put("out_date", JSONArray.parseArray(JSON.toJSONString(out_date_1)));
			    				    map.put("out_profit", JSONArray.parseArray(JSON.toJSONString(out_profit_1)));
			    				 
			    				 return "/tradeAccount";
		    			 }else
		    			 {
		    				 return "/login";
		    			 }
		    			 }else {
		    				 return "/login";
		    			 }
		    		}else {
			    		return "/login";
			    	}
		    	}else {
		    		return "/login";
		    	}
		    }else {
		    	return "/login";
		    }
		}else {
			return "/login";
		}
	}
	
	 public boolean ismatches(String bot){
		 boolean flag=false;
		 try{
			  String regex="^[1-9]+[0-9]*$";
			  Pattern p=Pattern.compile(regex);
			  Matcher m=p.matcher(bot);
			  if(m.find()){
				  return true;
			  }else{
					  
			  }
		 }catch(Exception e){
		  
		 }
	     return flag;
	 }
	
	/**
	 * 退出系统
	 */
	@RequestMapping("logout")
	public String logout(Map<String, Object> map, HttpServletRequest request) {
		  map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		request.getSession().setAttribute("loginUser", null);
		request.getSession().removeAttribute("loginUser");
		return "/login";
		
	}
	
	/**
	 * 入金申请
	 */
	@RequestMapping("toDeposit")
	public String toDeposit(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    
		    //查询当前CRM用户下所有的交易账号
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
		    
		    for(int i=0;i<ta_page.getContent().size();i++) {
		    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
		    	
		    	if(ta.getType()!=null) {
		    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
		    	ta.setBackup3(aty.getTypeName());
		    	}
		    }
		    map.put("tradeList", ta_page.getContent());
		    
		    
		    //查询所有入金银行
		    DepositBank db_query=new DepositBank();
		    db_query.setIsShow(1);
		    Page<DepositBank> db_page=this.depositBankService.findAll(0,1000, "id", "asc", db_query);
		    map.put("dbList", db_page.getContent());
		    
		    //入金设置
		    DepositSetting ds=(DepositSetting)this.depositSettingService.findById(1L);
		    map.put("depositSetting", ds);
		    
		    //汇率设置
		    
		    ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
		    map.put("rate",er);
		    
		    
		    
		   
		    
		    
		    
		    FundInfo fi_query =new FundInfo();
		    fi_query.setType(1);
		    fi_query.setUserId(myInfo.getId());
		    Page<FundInfo> ui_page=this.fundInfoService.findAll(0,1,"id","desc", fi_query);
		    if(ui_page.getContent().size()>0) {
		    	map.put("fundInfo",ui_page.getContent().get(0));
		    }else {
		    	map.put("fundInfo",new FundInfo());
		    	
		    }
		    
			 UserBank ub_query=new UserBank();
			 ub_query.setUserId(myInfo.getId());
			 Page<UserBank> ub_page=this.userBankService.findAll(0,100,"id","asc", ub_query);
			 map.put("bankList", ub_page.getContent());
			 
			 if(er.getBackup4()!=null&&er.getBackup4().equals("PAY")) {
				 return "/toDeposit3";
			 }else if(er.getBackup4()!=null&&er.getBackup4().equals("ONLYPAY")){
				 return "/toDeposit4";
			 }
			 else{
				 if(er.getBackup3()!=null&&er.getBackup3().equals("CASH")) {
					 return "/toDeposit2";
				 }
				 else {
					 return "/toDeposit";
				 }
			 }
		}else {
			  return "/login";
		}
		
	}
	
	/**
	 * 根据交易用户ID查当前的余额和冻结余额
	 */
	@RequestMapping("/getTradeBalance")
	public void getTradeBalance(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")&&!request.getParameter("trade_id").equals("null")&&!request.getParameter("trade_id").equals("undefined")) {
			TradeAccount ta=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("trade_id")));
			if(ta!=null) {
				
			if(ta.getBalance6()==null) {
				ta.setBalance6(0d);
			}
			
			}else {
				ta.setBackup6("");
			}
			
			response.getWriter().print(JSON.toJSONString(ta));
		}else {
			response.getWriter().print(false);
		}
      
	}
	
	/**
	 * 根据入金银行卡ID查银行信息
	 */
	@RequestMapping("/getBanInfo")
	public void getBanInfo(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
			DepositBank db=(DepositBank)this.depositBankService.findById(new Long(request.getParameter("bank_id")));

			response.setCharacterEncoding("UTF-8");
			response.setContentType("text/html;charset=UTF-8");

			response.getWriter().print(JSON.toJSONString(db));
		}else {
			response.getWriter().print(false);
		}
      
	}
	
	@RequestMapping("/getBankListByLoginId")
	public void getBankListByLoginId(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		if(request.getParameter("loginid")!=null&&!request.getParameter("loginid").equals("")) {
			
			UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
			
			TradeAccount taccount=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("loginid")));
			
			 //查询所有入金通道
		    DepositBank db_query=new DepositBank();
		    Page<DepositBank> db_page=this.depositBankService.findAll(0,1000, "id", "asc", db_query);
		    List depositDB=new ArrayList();
		    for(int i=0;i<db_page.getContent().size();i++) {
		    	DepositBank db_t=(DepositBank)db_page.getContent().get(i);
		    	
		    	if(db_t.getBackup1().equals("[]")||db_t.getBackup1().contains(myInfo.getCountry())) {//用户的居住地国家符合条件
		    		boolean isShow=true;
		    		
		    		if(db_t.getIsShow().intValue()==1) {
		    			
		    			if(myInfo.getIsAvailable().intValue()==1) {
		    				
		    			}else {
		    				isShow=false;
		    			}
		    			
		    		}
		    		
		    		boolean isShowType=true;
		    		
		    		if(!db_t.getBackup2().equals("[]")&&db_t.getBackup2().contains("\""+taccount.getType()+"\"")) {
		    			isShowType=false;
		    		}
		    		boolean isShowTag=true;
		    		
		    		if(!db_t.getBackup3().equals("[]")) {
		    		JSONArray ja=JSONArray.parseArray(myInfo.getBackup5());
		    		for(int m=0;m<ja.size();m++) {
		    			
		    			if(db_t.getBackup3().contains(String.valueOf(ja.get(m)))){
		    				isShowTag=false;
		    			}
		    			
		    		}
		    		}
		    		if(isShow&&isShowType&&isShowTag) {//是否符合KYC验证标准&&符合显示的账号类型&&符合用户Tag
		    			depositDB.add(db_t);
		    		}
		    		
		    	}
		    	
		    }

			response.setCharacterEncoding("UTF-8");
			response.setContentType("text/html;charset=UTF-8");

			response.getWriter().print(JSON.toJSONString(depositDB));
		}else {
			response.getWriter().print(false);
		}
      
	}
	
	/**
	 * 保存入金信息
	 */
	@RequestMapping("/saveDepositInfo")
	public void saveDepositInfo(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)session.getAttribute("loginUser");
			    
			    FundInfo fundInfo=new FundInfo();
			    fundInfo.setUserId(myInfo.getId());
			    fundInfo.setUserName(myInfo.getFullname());
			    fundInfo.setCrmAccount(myInfo.getUserName());
				if(request.getParameter("remark")!=null&&!request.getParameter("remark").equals("")) {
					fundInfo.setRemark(request.getParameter("remark"));
				}
			    TradeAccount ta=null;
			    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
			    	
			    	 ta=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("trade_id")));
			    	fundInfo.setTradeId(ta.getTradeId());
			    }
          
			    fundInfo.setAmount(new Double(request.getParameter("depositAmount")));
		
			    fundInfo.setType(1);
			    fundInfo.setAuditStatus(0);
			    fundInfo.setOperStatus(99);
			    this.fundInfoService.saveOrUpdate(fundInfo);
			    
			    if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
			    	fundInfo.setDepositBankId(new Long(request.getParameter("bank_id")));
			    }
			    
			    if(request.getParameter("depositType")!=null&&request.getParameter("depositType").equals("1")) {
			    	 fundInfo.setIsAvailable(0);
			    }else {
			    	 fundInfo.setIsAvailable(1);
			    	 
			    	 if(request.getParameter("bank_name")!=null) {
			    		 fundInfo.setBankName(request.getParameter("bank_name"));
			    	 }
			    	 
			    	 if(request.getParameter("bank_address")!=null) {
			    		 fundInfo.setBankAddress(request.getParameter("bank_address"));
			    	 }
			    	 if(request.getParameter("imageFront")!=null) {
			    		 fundInfo.setAnnex(request.getParameter("imageFront"));
			    	 }
			    }
			   
			    this.fundInfoService.saveOrUpdate(fundInfo);
			    SimpleDateFormat format2 = new SimpleDateFormat("yyyyMMdd");
			    String orderno="DEPOSIT"+format2.format(new Date())+"_"+fundInfo.getId()+"_"+RandomStrUtil.generate(4)+"_"+new Date().getTime();  
			    
			    fundInfo.setOrderId(orderno);
			    CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
			    String zhjeStr="********.99";
			    
			    if(request.getParameter("depositType")!=null&&request.getParameter("depositType").equals("1")) {
					 
					    Double zhje=fundInfo.getAmount();
					    
					   if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
						   
						   DepositBank depositBank=this.depositBankService.findById(new Long(request.getParameter("bank_id")));
						   
						   if(depositBank.getBankAddress()!=null&&!depositBank.getBankAddress().equals("")) {
							   Double rjjd=Double.parseDouble(depositBank.getBankAddress());
							   
							   Double dqje=((100d+rjjd)*zhje)/100;
							   
							   String host = "https://jisuhuilv.market.alicloudapi.com";
							    String path = "/exchange/convert";
							    String method = "GET";
							    String appcode = "cfe5c51e7637470e9b0043bf07de8093";
							    Map<String, String> headers = new HashMap<String, String>();
							    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
							    headers.put("Authorization", "APPCODE " + appcode);
							    //根据API的要求，定义相对应的Content-Type
							    headers.put("Content-Type", "application/json; charset=UTF-8");
							    Map<String, String> querys = new HashMap<String, String>();
							   
							    UserGroup ug_query=new UserGroup();
								  ug_query.setGroupName(ta.getGroupName());
								  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
								 if(page.getContent().size()>0) {
									UserGroup ug_t=(UserGroup)page.getContent().get(0);
									
									if(ug_t.getBackup1().equals("USC")) {
										if(ug_t.getBackup1().equals("USC")&&depositBank.getBankAccount().equals("USD")) {
											DecimalFormat df = new DecimalFormat("#.00");
									    	zhjeStr=df.format(dqje/100);
											zhje=dqje/100;
										}else {
											querys.put("amount", String.valueOf(dqje/100));
											 querys.put("from", "USD");  
											 querys.put("to", depositBank.getBankAccount());   //IDR  MYR  THB  VND
											    try {
											    	HttpResponse response2 = HttpUtils.doGet(host, path, method, headers, querys);
											    	JSONObject json=JSONObject.parseObject(EntityUtils.toString(response2.getEntity()));
											    	System.out.println("JS:"+json.toJSONString());
											    	JSONObject json2=JSONObject.parseObject(json.getString("result"));
											    	DecimalFormat df = new DecimalFormat("#.00");
											    	zhje=df.parse(json2.getString("camount")).doubleValue();
											    	zhjeStr=df.format(new Double(json2.getString("camount")));
											    } catch (Exception e) {
											    	e.printStackTrace();
											    }
										}
										
									}else {
										
										if(ug_t.getBackup1().equals("USD")&&depositBank.getBankAccount().equals("USD")) {
											DecimalFormat df = new DecimalFormat("#.00");
									    	zhjeStr=df.format(dqje);
											zhje=dqje;
										}else {
											 querys.put("from", ug_t.getBackup1());
											 querys.put("amount", String.valueOf(dqje));
											 querys.put("to", depositBank.getBankAccount());   //IDR  MYR  THB  VND
											    try {
											    	HttpResponse response2 = HttpUtils.doGet(host, path, method, headers, querys);
											    	System.out.println("JS:"+ug_t.getBackup1()+"   "+String.valueOf(dqje)+"  "+depositBank.getBankAccount()+"   ");
											    	JSONObject json=JSONObject.parseObject(EntityUtils.toString(response2.getEntity()));
											    
											    	JSONObject json2=JSONObject.parseObject(json.getString("result"));
											    	DecimalFormat df = new DecimalFormat("#.00");
											    	zhje=df.parse(json2.getString("camount")).doubleValue();
											    	zhjeStr=df.format(new Double(json2.getString("camount")));
											    } catch (Exception e) {
											    	e.printStackTrace();
											    }
										}
										
									}
								  }else {
									   querys.put("from", "USD");  
									   querys.put("amount", String.valueOf(dqje));
									   querys.put("to", depositBank.getBankAccount());   //IDR  MYR  THB  VND
									    try {
									    	HttpResponse response2 = HttpUtils.doGet(host, path, method, headers, querys);
									    	JSONObject json=JSONObject.parseObject(EntityUtils.toString(response2.getEntity()));
									    	System.out.println("JS:"+json.toJSONString());
									    	JSONObject json2=JSONObject.parseObject(json.getString("result"));
									    	DecimalFormat df = new DecimalFormat("#.00");
									    	zhje=df.parse(json2.getString("camount")).doubleValue();
									    	zhjeStr=df.format(new Double(json2.getString("camount")));
									    } catch (Exception e) {
									    	e.printStackTrace();
									    }
								  }
							    
							   
						   }
					   }
					    
					   fundInfo.setRateRmb(zhje);
			    }else {
			    	fundInfo.setAmount(null);
			    	fundInfo.setRateRmb(new Double(request.getParameter("depositAmount")));
			    	 fundInfo.setIsAvailable(1);
			    }
			    
			   
			   
			   this.fundInfoService.saveOrUpdate(fundInfo);
			    String sign=DigestUtils.md5Hex(orderno+"|"+fundInfo.getTradeId()+"|"+request.getParameter("currency2")+"|"+zhjeStr+"|"+companyInfo.getBackup2());
			    
			    JSONObject json=new JSONObject();
			    json.put("serial_number",orderno );
			    json.put("sign",sign );
			    json.put("amount",zhjeStr );
			    json.put("login_id",fundInfo.getTradeId() );
			    response.getWriter().print(json.toJSONString());
			   
			}else {
				response.getWriter().print(false);
			}
	}
	
	public JSONObject getJSONParam(HttpServletRequest request){
	    JSONObject jsonParam = null;
	    try {
	        // 获取输入流
	        BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));

	        // 数据写入Stringbuilder
	        StringBuilder sb = new StringBuilder();
	        String line = null;
	        while ((line = streamReader.readLine()) != null) {
	            sb.append(line);
	        }
	        jsonParam = JSONObject.parseObject(sb.toString());
	        System.out.println(jsonParam.toJSONString());
	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	    return jsonParam;
	}
	
	
	@PostMapping(value = "/payResult")
	public synchronized  void payResult(HttpServletRequest request, HttpServletResponse response) {
		
		
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		String status=request.getParameter("status");
		String sign=request.getParameter("sign");
		String type=request.getParameter("type");
		String transaction_id=request.getParameter("transaction_id");
		String currency=request.getParameter("currency");
		String serial_number=request.getParameter("serial_number");
		String amount=request.getParameter("amount");
		String login_id=request.getParameter("login_id");
		
		
	    
	    String signStr=DigestUtils.md5Hex(transaction_id+"|"+login_id+"|"+currency+"|"+amount+"|"+companyInfo.getBackup2());
	
		   
		   
          if (sign.equals(signStr)) {
        	   
        	  if(request.getParameter("status")!=null&&status.equals("success")) {
         		   FundInfo fundInfo_query=new FundInfo();
         		  fundInfo_query.setOrderId(serial_number);
         		   Page<FundInfo> page_record=this.fundInfoService.findAll(0, 1, "id", "asc", fundInfo_query);
         		   if(page_record.getContent().size()>0) {
         			   FundInfo fundInfo=(FundInfo)page_record.getContent().get(0);
         			if(fundInfo.getAuditStatus().intValue()!=1&&fundInfo.getOperStatus()!=1&&fundInfo.getIsAvailable()!=1)  
         			  fundInfo.setAuditStatus(1);
    				  fundInfo.setBackup4(1);
    				  fundInfo.setIsAvailable(1);
    				  this.fundInfoService.saveOrUpdate(fundInfo);
    				  
    				  if(fundInfo.getType().intValue()==1&&type.equals("deposit")) {//入金
    					  
    					  try {
    		 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    		 				   HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
    		 					boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
    		 					
    		 					if(blll) {
 					 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(fundInfo.getTradeId()),"2",String.valueOf(fundInfo.getAmount()),"Auto Deposit");
 					 			  if(json.getString("retcode").equals("0 Done"))  {
 					 				 fundInfo.setOperStatus(1);
     		 				 		this.fundInfoService.saveOrUpdate(fundInfo);
     		 				 		 TradeAccount query=new TradeAccount();
 		 		    					query.setTradeId(fundInfo.getTradeId());
 		 		    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
 		 		    					if(ta_page.getContent().size()>0) {
 		 		    						TradeAccount tasss=(TradeAccount)ta_page.getContent().get(0);
 		 		    						
 		 		    						if(tasss!=null) {
 		 		    							if(tasss.getBalance6()!=null) {
 		 		    								if(tasss.getBalance6()>=fundInfo.getAmount()) {
 		 		    									tasss.setBalance6(tasss.getBalance6()-fundInfo.getAmount());
 		 		    								}else {
 		 		    									tasss.setBalance6(0D);
 		 		    								}
 		 		    								this.tradeAccountService.saveOrUpdate(tasss);
 		 		    							}
 		 		    						}
 		 		    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
 		 		    						 if(json_ta.getString("retcode").equals("0 Done"))  {
 		 		    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
 	 		    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
 	 		    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
 	 		    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
 	 		    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
 	 		    			                	 this.tradeAccountService.saveOrUpdate(tasss);
 		 		    							 
 		 		    						 }
 		 		    					}
 					 			  }else {
 					 				 fundInfo.setOperStatus(2);
		 		 				 		this.fundInfoService.saveOrUpdate(fundInfo);
 					 			  }
    		 					}else {
    		 						 fundInfo.setOperStatus(2);
		 		 				 		this.fundInfoService.saveOrUpdate(fundInfo);
    		 					}
    		 					}catch(Exception e) {
    		 						
    		 					}
    					  
    				  }else if (fundInfo.getType().intValue()==2&&type.equals("withdraw")) {//出金
    					  try {
    		 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    		 						
    		 						JSONObject jsonObject=new JSONObject();
    		 			            fundInfo.setAuditStatus(1);
    		 			            fundInfo.setOperStatus(1);
    		 			            fundInfo.setCurrencyType(1);
    		 				 		this.fundInfoService.saveOrUpdate(fundInfo);
    		 					    
    		 					}catch(Exception e) {
    		 						
    		 					}
    				  }
    				  
    				  
         		   }
        		  
        		  try {
        				 response.getWriter().print("success");
        			}catch(Exception e) {
        			}
        		  
        		
        	  }else {
        		  
        		  
        		   FundInfo fundInfo_query=new FundInfo();
          		   fundInfo_query.setOrderId(serial_number);
          		   Page<FundInfo> page_record=this.fundInfoService.findAll(0, 1, "id", "asc", fundInfo_query);
          		   if(page_record.getContent().size()>0) {
          			   FundInfo fundInfo=(FundInfo)page_record.getContent().get(0);
          			   
          			   if(fundInfo.getType().intValue()==2) {
          				 fundInfo.setCurrencyType(2);
          				 fundInfo.setAuditStatus(2);
          				   this.fundInfoService.saveOrUpdate(fundInfo);
          			   }

          		   }
        		
       		  
        	  }
          }
	}
	
	/**
	 * 出金申请
	 */
	@RequestMapping("toWithdraw")
	public String toWithdraw(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    
		    //查询当前CRM用户下所有的交易账号
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
		    
		    for(int i=0;i<ta_page.getContent().size();i++) {
		    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
		    	if(ta.getType()!=null) {
		    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
		    	ta.setBackup3(aty.getTypeName());
		    	}
		    }
		    
		    map.put("tradeList", ta_page.getContent());
		    
		    //出金设置
		    WithdrawalSetting ww=(WithdrawalSetting)this.withdrawalSettingService.findById(1L);
		    map.put("withdrawalSetting", ww);
		    
		    //查询所有出金银行
		    WithdrawalBank db_query=new WithdrawalBank();
		    db_query.setIsShow(1);
		    Page<WithdrawalBank> db_page=this.withdrawalBankService.findAll(0,1000, "id", "asc", db_query);
		    map.put("dbList", db_page.getContent());
		    
		    //汇率设置
		    
		    ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
		    map.put("rate",er);
		    
		    FundInfo fi_query =new FundInfo();
		    fi_query.setType(2);
		    fi_query.setUserId(myInfo.getId());
		    Page<FundInfo> ui_page=this.fundInfoService.findAll(0,1,"id","desc", fi_query);
		    if(ui_page.getContent().size()>0) {
		    	map.put("fundInfo",ui_page.getContent().get(0));
		    }else {
		    	map.put("fundInfo",new FundInfo());
		    	
		    }
		    
			 UserBank ub_query=new UserBank();
			 ub_query.setUserId(myInfo.getId());
			 Page<UserBank> ub_page=this.userBankService.findAll(0,100,"id","asc", ub_query);
			 map.put("bankList", ub_page.getContent());
			 if(er.getBackup3()!=null&&er.getBackup3().equals("CASH")) {
				 return "/toWithdraw2";
			 }else {
				 return "/toWithdraw";
			 }
		}else {
			  return "/login";
		}
		
	}
	
	/**
	 * 保存出金信息
	 */
	@RequestMapping("/saveWithdrawInfo")
	public void saveWithdrawInfo(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("loginUser")!=null) {
			 UserInfo myInfo=(UserInfo)session.getAttribute("loginUser");
			    
			    FundInfo fundInfo=new FundInfo();
			    fundInfo.setUserId(myInfo.getId());
			    fundInfo.setUserName(myInfo.getFullname());
			    fundInfo.setCrmAccount(myInfo.getUserName());
			    TradeAccount ta=null;
			    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
			    	
			    	 ta=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("trade_id")));
			    	
			    	if(ta.getBalance6()!=null) {
			    		ta.setBalance6(ta.getBalance6()+new Double(request.getParameter("withdrawalAmount")));
			    	}else {
			    		ta.setBalance6(new Double(request.getParameter("withdrawalAmount")));
			    	}
			    	this.tradeAccountService.saveOrUpdate(ta);
			    	
			    	fundInfo.setTradeId(ta.getTradeId());
			    }
			    fundInfo.setOperStatus(99);
			    this.fundInfoService.saveOrUpdate(fundInfo);
			    fundInfo.setRemark(request.getParameter("remark"));
			    fundInfo.setAmount(new Double(request.getParameter("withdrawalAmount")));
			    fundInfo.setType(2);
			    fundInfo.setAuditStatus(0);
			   
			    fundInfo.setIsAvailable(0);
			    //fundInfo.setBackup3(1);
			    String bankNamesss="";
			    if(request.getParameter("tel").equals("MY")||request.getParameter("tel").equals("ID")||request.getParameter("tel").equals("VN")||request.getParameter("tel").equals("TH")) {
			    	bankNamesss=request.getParameter("bank_name");
			    }else {
			    	bankNamesss=request.getParameter("bank_name3");
			    }
			    
			    fundInfo.setBankName(bankNamesss);
			    fundInfo.setBankNum(request.getParameter("bank_num"));
			    fundInfo.setBankAddress(request.getParameter("withdrawal_name"));
			    fundInfo.setMobile(request.getParameter("tel"));
			   
			    fundInfo.setBackup6(request.getParameter("withdraw_currency"));
			   
			    
			    UserBank ub_query=new UserBank();
			    ub_query.setUserId(myInfo.getId());
				Page<UserBank> pages = userBankService.findAll(0, 1, "id","asc", ub_query);
				if(pages.getContent().size()>0) {
					UserBank ub=(UserBank)pages.getContent().get(0);
					   ub.setBankName(bankNamesss);
					   ub.setAccountUsername(request.getParameter("withdrawal_name"));
					   ub.setBankAccount(request.getParameter("bank_num"));
					   ub.setTel(request.getParameter("tel"));
					   this.userBankService.saveOrUpdate(ub);
					
				}else {
					
					   UserBank ub=new UserBank();
						   ub.setGmtCreate(new Date());
						   ub.setGmtModified(new Date());
						   ub.setIsDeleted(0);
						   ub.setIsAvailable(1);
					   ub.setBankName(bankNamesss);
					   ub.setAccountUsername(request.getParameter("withdrawal_name"));
					   ub.setBankAccount(request.getParameter("bank_num"));
					   ub.setTel(request.getParameter("tel"));
					   ub.setUserId(myInfo.getId());
					   this.userBankService.saveOrUpdate(ub);
					
				  }
				
			
				    this.fundInfoService.saveOrUpdate(fundInfo);
				    
				    System.out.println("出金記錄：fundInfoID:"+fundInfo.getId()+"   traderID:"+ta.getTradeId());
				    
				    
				    
				    //先判断出金风控
				    
				    WithdrawalSetting dsetting=(WithdrawalSetting)this.withdrawalSettingService.findById(new Long(1L));
				    
				    boolean isShow=false;
				    boolean isShowType=false;
					boolean isShowTag=false;
				    
				    if(dsetting.getNoticeEmail().equals("[]")||dsetting.getNoticeEmail().contains(myInfo.getCountry())) {//用户的居住地国家符合条件
				    	isShow=true;
				    	System.out.println("用户的居住地国家符合条件");
			    	}else {
			    		System.out.println("用户的居住地国家不符合条件");
			    	}
			    		
			    	if(!dsetting.getNoticeMobile().equals("[]")) {
			    			//&&!dsetting.getNoticeMobile().contains("\""+ta.getType()+"\"")
			    			 JSONArray ja=JSONArray.parseArray(dsetting.getNoticeMobile());
				    		   for(int m=0;m<ja.size();m++) {
				    			   
				    			   System.out.println(String.valueOf(ja.get(m))+"   "+String.valueOf(ta.getType()));
					    			if(String.valueOf(ja.get(m)).equals(String.valueOf(ta.getType()))){
					    				isShowType=true;
					    			}
					    			
					    		}
			    	}else {
			    		isShowType=false;
			    	}
			    	
			    	if(isShowType) {
		    			 System.out.println("用户在屏蔽类型内t1");
		    		}else {
		    			System.out.println("用户不在屏蔽类型内t1");
		    		}
			    	
			    		
		    		if(!dsetting.getBackup1().equals("[]")) {
		    		JSONArray ja=JSONArray.parseArray(myInfo.getBackup5());
		    		for(int m=0;m<ja.size();m++) {
		    			
		    			if(dsetting.getBackup1().contains(String.valueOf(ja.get(m)))){
		    				isShowTag=true;
		    			}
		    			
		    		}
		    		}else {
		    			isShowTag=false;
		    		}
		    		
		    		
		    		if(isShowTag) {
		    			 System.out.println("用户在屏蔽标签内");
		    		}else {
		    			System.out.println("用户不在屏蔽标签内");
		    		}
		    		JSONObject json=new JSONObject();
		    		
		    		fundInfo.setBackup3(2);
		    		fundInfo.setBackup4(2);
		    		fundInfo.setCurrencyType(0);
		    		this.fundInfoService.saveOrUpdate(fundInfo);
		    		
                      if(request.getParameter("withdraw_currency").equals("USDT TRC20")||request.getParameter("withdraw_currency").equals("USDT ERC20")||request.getParameter("withdraw_currency").equals("BTC")) {
		    			
						/* 之前判断什么已经无从考究了，故注释掉这段代码
						if(fundInfo.getBackup3().intValue()==1) {
		    				fundInfo.setBackup4(2);
		    			}else if(fundInfo.getBackup3().intValue()==2) {
		    				fundInfo.setBackup4(null);
		    			}
		    			 */
		    			fundInfo.setMobile("");
		    			fundInfo.setBankName("");
		    			if(request.getParameter("bank_address2")!=null&&!request.getParameter("bank_address2").equals(""))
		    			{
		    				fundInfo.setBankNum(request.getParameter("bank_address2"));
		    			}else {
		    				fundInfo.setBankNum("");
		    			}
		    			
		    			fundInfo.setBankAddress("");
		    			this.fundInfoService.saveOrUpdate(fundInfo);
		    		}
		    		
				   
				    response.getWriter().print(json.toJSONString());
				
				}else {
					response.getWriter().print(false);
				}
	}
	
	/**
	 * 资金流水
	 */
	@RequestMapping("fundList")
	public String fundList(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		map=this.getSetting(map);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    FundInfo fi_query =new FundInfo();
		    fi_query.setUserId(myInfo.getId());
		    Page<FundInfo> ui_page=this.fundInfoService.findAll(0,50,"id","desc", fi_query);
		    map.put("funList", ui_page.getContent());
		    
		    
		    ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
		    
		     map.put("isPay", er.getBackup4()!=null?"1":"0");
			 
				 return "/fundList";
			 
		}else {
			  return "/login";
		}
		
	}

	/**
	 * 订单记录
	 */
	@RequestMapping("orderList")
	public String orderList(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		map=this.getSetting(map);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		   
            OrderInfo query  = new OrderInfo();
            
            // 处理日期范围参数
            String startDate = request.getParameter("startDate");
            String endDate = request.getParameter("endDate");
            
            if(startDate != null && !startDate.equals("")) {
                try {
                    // 将日期字符串转换为时间戳（秒）
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date start = sdf.parse(startDate);
                    int startTimestamp = (int)(start.getTime() / 1000);
                    query.setQueryBeginTime(startTimestamp);
                    map.put("startDate", startDate);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            
            if(endDate != null && !endDate.equals("")) {
                try {
                    // 将日期字符串转换为时间戳（秒），结束日期设为当天23:59:59
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date end = sdf.parse(endDate);
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(end);
                    cal.set(Calendar.HOUR_OF_DAY, 23);
                    cal.set(Calendar.MINUTE, 59);
                    cal.set(Calendar.SECOND, 59);
                    int endTimestamp = (int)(cal.getTimeInMillis() / 1000);
                    query.setQueryEndTime(endTimestamp);
                    map.put("endDate", endDate);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            
            // 兼容旧的时间戳参数
			if(request.getParameter("closeTime1")!=null&&!request.getParameter("closeTime1").equals("")) {
				query.setQueryBeginTime(Integer.parseInt(request.getParameter("closeTime1").trim()));
			}else{
				query.setQueryBeginTime(Integer.parseInt(String.valueOf(new Date().getTime()/1000-(60*60*24*60))));
			}
			if(request.getParameter("closeTime2")!=null&&!request.getParameter("closeTime2").equals("")) {
				query.setQueryEndTime(Integer.parseInt(request.getParameter("closeTime2").trim()));
			}	

			TradeAccount tradeAccount_query = new TradeAccount();
			tradeAccount_query.setUserId(myInfo.getId());
			List tradeLoginIdList = new ArrayList();
			Page<TradeAccount> pages_tradeAccount = tradeAccountService.findAll(0, 10000, "id", "desc", tradeAccount_query);
			List<TradeAccount> tradeAccountList = pages_tradeAccount.getContent();
			for(int k=0;k<tradeAccountList.size();k++) {
				TradeAccount tii=(TradeAccount)tradeAccountList.get(k);
				tradeLoginIdList.add(tii.getTradeId());
			}

			if(tradeLoginIdList.size()>0) {
				query.setTradeLoginIdList(tradeLoginIdList);
			}else{
				tradeLoginIdList.add("A****************99");
				query.setLoginId("A********") ;
			}
                List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	
	    	query.setTypeList(ll);

			/* 
			Page<OrderInfo> pages = orderInfoService.findAll(0, 10000, "closeTime", "desc", query);
        	JSONObject datas = new JSONObject();
        	List<OrderInfo> orderInfos = pages.getContent();
        	for(int i=0;i<orderInfos.size();i++) {
        		OrderInfo entity  = orderInfos.get(i);
        		
        	}
            */
			Page<OrderInfo> pages2 = orderInfoService.findAll(0, 10000, "closeTime", "desc", query);
        	    double yjss=0d;
				double hlje=0d;
	            	for(int m=0;m<pages2.getContent().size();m++) {
	            		OrderInfo oii=(OrderInfo)pages2.getContent().get(m);
	            		BigDecimal bg = new BigDecimal(oii.getVolume());
	            		double f1 = bg.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
	            		yjss=yjss+f1;

						BigDecimal bg2=new BigDecimal(oii.getProfit());
						double f2=bg2.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
						hlje=hlje+f2;
	            	}
					DecimalFormat df3=new DecimalFormat("##0.00");
					DecimalFormat df4=new DecimalFormat("##0.00");
	            	map.put("yjss", df3.format(yjss));
					map.put("hlje", df4.format(hlje));
					map.put("orderList", pages2.getContent());


			return "/orderList";
			 
		}else {
			  return "/login";
		}
		
	}
	/**
	 * 历史记录
	 */
	@RequestMapping("tradeHistory")
	public String tradeHistory(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			OrderInfo order_query=new OrderInfo();
	    	order_query.setTradeList(ta_page.getContent());
	    	if(ta_page.getContent().size()<=0) {
	    		 TradeAccount taaaaa=new TradeAccount();
	    		 taaaaa.setId(-********L);
	    		 List lsisi=new ArrayList();
	    		 lsisi.add(taaaaa);
	    		 order_query.setTradeList(lsisi);
			    }
	    	order_query.setStatus(2);
	    	List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	ll.add(2);
	    	ll.add(3);
	    	ll.add(4);
	    	ll.add(5);
	    	order_query.setTypeList(ll);
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,100, "id","desc", order_query);
			
			map.put("orderList",oi_page.getContent());
			
		    return "/tradeHistory";
		}else {
			  return "/login";
		}
		
	}

	
	/**
	 * 进入修改个人信息页面
	 */
	@RequestMapping("modifyMyInfo")
	public String modifyMyInfo(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    return "/modifyMyInfo";
		}else {
			  return "/login";
		}
		
	}
	
	
	@RequestMapping("changeLanguage")
	public String changeLanguage(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    return "/changeLanguage";
		}else {
			  return "/login";
		}
		
	}
	
	/**
	 * 保存修改的资料信息
	 */
	@RequestMapping("/saveLanguage")
	public void saveLanguage(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)session.getAttribute("loginUser");
			
			   userInfo_session.setBackup1(request.getParameter("planguage"));
				 this.userInfoService.saveOrUpdate(userInfo_session);
				 session.setAttribute("loginUser", userInfo_session);
				 
				 if(userInfo_session.getBackup1().equals("zh-cn")) {
						request.getSession().setAttribute("i18n_language_session","zh_CN");
					}else if(userInfo_session.getBackup1().equals("en")) {
						request.getSession().setAttribute("i18n_language_session","en_US");
					}else if(userInfo_session.getBackup1().equals("th")) {
						request.getSession().setAttribute("i18n_language_session","th_TH");
					}else if(userInfo_session.getBackup1().equals("vi")) {
						request.getSession().setAttribute("i18n_language_session","vi_VN");
					}else if(userInfo_session.getBackup1().equals("id")){
						request.getSession().setAttribute("i18n_language_session","in_ID");
					}else if(userInfo_session.getBackup1().equals("ms")) {
						request.getSession().setAttribute("i18n_language_session","ms_MY");
					}else if(userInfo_session.getBackup1().equals("zh-tw")) {
						request.getSession().setAttribute("i18n_language_session","zh_TW");
					}else if(userInfo_session.getBackup1().equals("ja")) {
						request.getSession().setAttribute("i18n_language_session","ja_JP");
					}else if(userInfo_session.getBackup1().equals("zh-tw")) {
						request.getSession().setAttribute("i18n_language_session","zh_TW");
					}else {
						request.getSession().setAttribute("i18n_language_session","en_US");
					}
			   
		}
	}
	
	
	/**
	 * 保存修改的资料信息
	 */
	@RequestMapping("/updateaMyInfo")
	public void updateaMyInfo(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		
		HttpSession session=request.getSession();
		if(session.getAttribute("loginUser")!=null) {
			
			   UserInfo userInfo_session=(UserInfo)session.getAttribute("loginUser");
			 
			    UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			    
				if(request.getParameter("country")!=null&&!request.getParameter("country").equals("")) {
					userInfo.setCountry(request.getParameter("country"));
				}
				if(request.getParameter("province")!=null&&!request.getParameter("province").equals("")) {
					userInfo.setProvince(request.getParameter("province"));
				}
				if(request.getParameter("tel")!=null&&!request.getParameter("tel").equals("")) {
					userInfo.setTel(request.getParameter("tel"));	
			    }
				if(request.getParameter("city")!=null&&!request.getParameter("city").equals("")) {
						userInfo.setCity(request.getParameter("city"));	
				}
				if(request.getParameter("adress")!=null&&!request.getParameter("adress").equals("")) {
					userInfo.setAdress(request.getParameter("adress"));
				}
				if(request.getParameter("identityNum")!=null&&!request.getParameter("identityNum").equals("")) {
					userInfo.setIdentityNum(request.getParameter("identityNum"));
				}
				if(request.getParameter("imageFront")!=null&&!request.getParameter("imageFront").equals("")) {
						userInfo.setImageFront(request.getParameter("imageFront"));	
				}
				if(request.getParameter("imageBack")!=null&&!request.getParameter("imageBack").equals("")) {
					userInfo.setImageBack(request.getParameter("imageBack"));
				}
				this.userInfoService.saveOrUpdate(userInfo);
			
			    session.setAttribute("loginUser", userInfo);
			    response.getWriter().print(true);
			}else {
				response.getWriter().print(false);
			}
		
	}
	
	/**
	 * 进入修改密码
	 */
	@RequestMapping("modifyPwd")
	public String modifyPwd(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    return "/modifyPwd";
		}else {
			  return "/login";
		}
		
	}
	/**
	 * 保存修改的密码
	 */
	@RequestMapping("saveMyPwd")
	public void saveMyPwd(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   if(!userInfo.getPassword().equals(request.getParameter("memberPsd_old"))) {
				   response.getWriter().print(false);
			   }else {
				   userInfo.setPassword(request.getParameter("memberPsd_new"));
				   this.userInfoService.saveOrUpdate(userInfo);
				   response.getWriter().print(true);
			   }
			
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	/**
	 * 进入忘记密码
	 */
	@RequestMapping("forgetPwd")
	public String forgetPwd(Map<String, Object> map, HttpServletRequest request) {
		map=this.getSetting(map);
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		    return "/forgetPwd";
	}
	
	/**
	 * 发送验证码-忘记密码
	 */
	@RequestMapping("/sendCodeByForget")
	public void sendCodeByForget(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		StringBuilder code = new StringBuilder();
		Random random = new Random();
		// 生成4位验证码
		for (int i = 0; i < 4; i++) {
			code.append(String.valueOf(random.nextInt(10)));
		}
		
		if (request.getParameter("memberName") != null && !request.getParameter("memberName").equals("")) {
			request.getSession().setAttribute("forgetPwd", code);
			UserInfo query_userInfo=new UserInfo();
			
			query_userInfo.setUserName(request.getParameter("memberName"));
			Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);
			if (userInfo_page.getContent().size()>0) {
				try {
					EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
					CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
					//SendEmail.sendSimpleMail(emailInfo.getApiUser(),emailInfo.getApiKey(),emailInfo.getFromEmail(), request.getParameter("memberName"), "欢迎您使用 "+companyInfo.getSystemName(), "您的验证码是："+code);
					SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Your OTP for Email Verification",code.toString(),"","",emailInfo.getBackup1(),emailInfo.getFromEmail(),emailInfo.getEmailName(),request.getParameter("memberName"),"","");
					request.getSession().setAttribute("forgetPwd", code);
					response.getWriter().print(true);
				} catch (Exception e) {
					e.printStackTrace();
					response.getWriter().print(false);
				}
			} else {
				response.getWriter().print(false);
			}
			
		}
		
	}
	
	/**
	 * 重置密码
	 */
	@RequestMapping("resetForgetPwd")
	public void resetForgetPwd(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		   if(request.getParameter("memberName")!=null&&request.getParameter("memberPsd")!=null&&request.getParameter("code")!=null) {
			   if(request.getParameter("code").equals(request.getSession().getAttribute("forgetPwd").toString())){
				   UserInfo query_userInfo=new UserInfo();
					query_userInfo.setUserName(request.getParameter("memberName"));
					Page<UserInfo> userInfo_page=this.userInfoService.findAll(0,1,"id","asc", query_userInfo);
					if (userInfo_page.getContent().size()>0) {
						userInfo_page.getContent().get(0).setPassword(request.getParameter("memberPsd"));
						this.userInfoService.saveOrUpdate(userInfo_page.getContent().get(0));
						  response.getWriter().print(true);
					}else {
						 response.getWriter().print(false);
					}
			   }else {
				   response.getWriter().print(false);
			   }
		   }else {
			   response.getWriter().print(false);
		   }
		
	}
	
	/**
	 * 账户余额
	 */
	@RequestMapping("tradeBalance")
	public String tradeBalance(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			OrderInfo order_query=new OrderInfo();
	    	order_query.setTradeList(ta_page.getContent());
	    	if(ta_page.getContent().size()<=0) {
	    		 TradeAccount taaaaa=new TradeAccount();
	    		 taaaaa.setId(-********L);
	    		 List lsisi=new ArrayList();
	    		 lsisi.add(taaaaa);
	    		 order_query.setTradeList(lsisi);
			    }
	    	order_query.setStatus(2);
	    	order_query.setType(6);
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,100, "id","desc", order_query);
			
			map.put("orderList",oi_page.getContent());
			
		    return "/tradeBalance";
		}else {
			  return "/login";
		}
		
	}
	
	/**
	 * 信用配资
	 */
	@RequestMapping("tradeCredit")
	public String tradeCredit(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			OrderInfo order_query=new OrderInfo();
	    	order_query.setTradeList(ta_page.getContent());
	    	if(ta_page.getContent().size()<=0) {
	    		 TradeAccount taaaaa=new TradeAccount();
	    		 taaaaa.setId(-********L);
	    		 List lsisi=new ArrayList();
	    		 lsisi.add(taaaaa);
	    		 order_query.setTradeList(lsisi);
			    }
	    	order_query.setStatus(2);
	    	order_query.setType(7);
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,100, "id","desc", order_query);
			
			map.put("orderList",oi_page.getContent());
			
		    return "/tradeCredit";
		}else {
			  return "/login";
		}
		
	}
	
	/**
	 * 进入创建账户
	 */
	@RequestMapping("toBank")
	public String toBank(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			 
			 ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
			 if(er.getBackup3()!=null&&er.getBackup3().equals("CASH")) {
				 return "/toBank2";
			 }else {
				 return "/toBank";
			 }
		}else {
			 return "/login";
		}
		
	}
	/**
	 * 保存银行账号
	 */
	@RequestMapping("/saveBank")
	public void saveBank(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   UserBank ub=new UserBank();
			   if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
				   ub.setId(new Long(request.getParameter("bank_id")));
				   ub.setGmtCreate(new Date());
				   ub.setGmtModified(new Date());
				   ub.setIsDeleted(0);
				   ub.setIsAvailable(1);
			   }
			   ub.setAccountName(request.getParameter("account_name"));
			   ub.setBankName(request.getParameter("bank_name"));
			   ub.setAccountUsername(request.getParameter("user_name"));
			   ub.setBankAccount(request.getParameter("bank_num"));
			   ub.setTel(request.getParameter("tel"));
			   ub.setUserId(userInfo.getId());
			   this.userBankService.saveOrUpdate(ub);
			   response.getWriter().print(true);
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	/**
	 * 账户列表
	 */
	@RequestMapping("bankList")
	public String bankList(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			 
			 UserBank ub_query=new UserBank();
			 ub_query.setUserId(myInfo.getId());
			 Page<UserBank> ub_page=this.userBankService.findAll(0,100,"id","asc", ub_query);
			 map.put("bankList", ub_page.getContent());
			 
			 ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
			 if(er.getBackup3()!=null&&er.getBackup3().equals("CASH")) {
				 return "/bankList2";
			 }else {
				 return "/bankList";
			 }
		}else {
			 return "/login";
		}
		
	}
	
	/**
	 * 进入创建账户
	 */
	@RequestMapping("editBank")
	public String editBank(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			 
			 if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
				 UserBank ub=this.userBankService.findById(new Long(request.getParameter("bank_id")));
				 map.put("bank", ub);
				 
				 if(ub.getUserId().intValue()==myInfo.getId()) {
					 
					 ExchangeRate er=(ExchangeRate)this.exchangeRateService.findById(1L);
					 if(er.getBackup3()!=null&&er.getBackup3().equals("CASH")) {
						 return "/editBank2";
					 }else {
						 return "/editBank";
					 }
				 }else {
					 return "/login";
				 }
			 }else {
				 return "/login";
			 }
			  
		}else {
			 return "/login";
		}
		
	}
	
	/**
	 * 根据用户银行卡ID查银行信息
	 */
	@RequestMapping("/getUserBanInfo")
	public void getUserBanInfo(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		if(request.getParameter("bank_id")!=null&&!request.getParameter("bank_id").equals("")) {
			UserBank db=(UserBank)this.userBankService.findById(new Long(request.getParameter("bank_id")));
			response.setCharacterEncoding("UTF-8");
			response.setContentType("text/html;charset=UTF-8");
			response.getWriter().print(JSON.toJSONString(db));
		}else {
			response.getWriter().print(false);
		}
      
	}
	
	/**
	 * 进入创建账户
	 */
	@RequestMapping("createAccount")
	public String createAccount(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			 
			 AccountType accountType_query=new AccountType();
			 accountType_query.setIsShow(1);
			 Page<AccountType> accountType_page=this.accountTypeService.findAll(0,100,"id","desc", accountType_query);
			 map.put("accountTypeList", accountType_page.getContent());
			  return "/createAccount";
						  
		}else {
			 return "/login";
		}
		
	}
	
	
	/**
	 * 保存交易账号
	 */
	@RequestMapping("/saveCreateAccount")
	public void saveCreateAccount(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   
			   AccountType atype_pre=this.accountTypeService.findById(new Long(request.getParameter("userType")));
			   int max_accounts=1;
			   if(atype_pre.getMax_Accounts()!=null) {
				   max_accounts=atype_pre.getMax_Accounts();
			   }
			   
			   TradeAccount ta_query=new TradeAccount();
			   ta_query.setIsAvailable(1);
			   ta_query.setUserId(userInfo.getId());
			   ta_query.setType(new Integer(request.getParameter("userType")));
			   Page<TradeAccount> pages_ta = tradeAccountService.findAll(0, 100, "id", "asc", ta_query);
			   
			   if(pages_ta.getContent().size()>=max_accounts) {
				   response.getWriter().print("MAX");
			   }else {
				   
				   try {
					   if(userInfo.getName()==null) {
						   userInfo.setName(request.getParameter("firstname"));
					   }
					   
					   if(userInfo.getSurname()==null) {
						   userInfo.setSurname(request.getParameter("lastname"));
					   }
					   
					   if(userInfo.getBirthday()==null) {
						   SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
						   userInfo.setBirthday(format.parse(request.getParameter("birthday")));
					   }
					   
					   if(userInfo.getCountry()==null) {
						   userInfo.setCountry(request.getParameter("country"));
					   }
					   
					   if(userInfo.getAdress()==null) {
						   userInfo.setAdress(request.getParameter("adress"));
					   }
					   this.userInfoService.saveOrUpdate(userInfo);
					   request.getSession().setAttribute("loginUser",userInfo);
					   }catch(Exception e) {
						   
					   }
					   
					   
					   TradeAccount ta=new TradeAccount();
					   ta.setIsAvailable(0);
					   ta.setUserId(userInfo.getId());
					   ta.setUserAccount(userInfo.getUserName());
					   ta.setTradeStatus(0);
					   ta.setType(new Integer(request.getParameter("userType")));
					   AccountType atype=this.accountTypeService.findById(new Long(request.getParameter("userType")));
					   ta.setGroupName(atype.getGroupName());
					   ta.setLeverShow(request.getParameter("backup1"));
					   ta.setBackup6(request.getParameter("accountPwd"));
					   ta.setBalance1(0d);
					   ta.setBalance2(0d);
					   ta.setBalance3(0d);
					   this.tradeAccountService.saveOrUpdate(ta);
					   
					   
					   if(atype_pre.getIsAutoAudit1()!=null&&atype_pre.getIsAutoAudit1().intValue()==1) {
						   try {
								if(ta!=null&&ta.getIsAvailable().intValue()!=1) {
								String password=new UserInfoController().genRandomNum(8,1)+String.valueOf(new Random().nextInt(99));
								ta.setIsAvailable(1);
								ta.setTradeStatus(1);
								//begin
								
								    Long beginID=2000L;
					                Long endID=********9L;
					                if(atype.getBackup1()!=null&&!atype.getBackup1().equals("")) {//起始账号
					                	
					                	try {
					                		beginID=new Long(atype.getBackup1().toString());
					                	}catch(Exception e) {
					                		
					                	}
					                }
					                if(atype.getBackup2()!=null&&!atype.getBackup2().equals("")) {//结束账号
					                	try {
					                		endID=new Long(atype.getBackup2().toString());
					                	}catch(Exception e) {
					                		
					                	}
					                }
					                Long taccountID_MR=-1L;
					                for(Long cur=beginID;cur<=endID;cur++) {
					                	TradeAccount taaa_query=new TradeAccount();
					                	taaa_query.setTradeId(cur.toString());
					                	Page<TradeAccount> taaa_page=this.tradeAccountService.findAll(0, 1, "id", "asc", taaa_query);
					                	
					                	if(taaa_page.getContent().size()>0) {
					                		
					                	}else {
					                		 taccountID_MR=cur;
					                		 break;
					                	}
					                }
								
								//end
					            if(taccountID_MR!=-1L) {
					            ta.setTradeId(taccountID_MR.toString());//交易账号
				                this.tradeAccountService.saveOrUpdate(ta);
				            	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
							    //begin crm 接口
								 try {
									  HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
								      if(httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api")) {
								    	  org.json.JSONObject json=httpClient.addNewLogin(String.valueOf(ta.getTradeId()),ta.getBackup6(),ta.getBackup6()+"1",ta.getGroupName(),userInfo.getName()+userInfo.getSurname(),ta.getLeverShow(),userInfo.getUserName(),userInfo.getCountry());
								    	  if(json.getString("retcode").equals("0 Done")) {
								    		  
								    		   // UserInfo userInfo=(UserInfo)this.userInfoService.findById(ta.getUserId());
								                EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
								                CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
								                try {
								                	SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Account Opening Notice","",ta.getTradeId(),ta.getBackup6(),emailInfo.getBackup2(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,userInfo.getUserName(),userInfo.getFullname(),"");
								                }catch(Exception e) {
								                	System.out.println(e);
								                }
									    	 
									      }else {
									      }
								      }
									}catch(Exception e) {
										System.out.println(e);
									}
								
					            }else {
					            	 ta.setIsAvailable(0);
					            	 this.tradeAccountService.saveOrUpdate(ta);
					            }
								}else {
								}
								
							}catch(Exception e){
								response.getWriter().print(false);
				 			}
					   }
					   
					   response.getWriter().print(true);
			   }
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	
	/**
	 * 更改杠杆
	 */
	@RequestMapping("/changeLev")
	public void changeLev(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   
			   try {
			   TradeAccount ta=this.tradeAccountService.findById(new Long(request.getParameter("account_id")));
			   
			   if(ta!=null&&ta.getId()!=null&&ta.getUserId().toString().equals(userInfo.getId().toString())) {
				    
				     ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
					
				     HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
				     
				      if(httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api")) {
				    	  
				          org.json.JSONObject json=httpClient.updateLoginLeverage(ta.getTradeId(),request.getParameter("backup1"));
				          
					      if(json.getString("retcode").equals("0 Done")) {
					    	  ta.setLeverShow(request.getParameter("backup1"));
			                	 this.tradeAccountService.saveOrUpdate(ta);
			                	 response.getWriter().print(true);
					      }else {
					    	  response.getWriter().print(false); 
					      }
				      }else {
				    	  response.getWriter().print(false); 
				      }
			   }else {
				   response.getWriter().print(false); 
			   }
			   }catch(Exception e) {
				   System.out.println(e);
				   response.getWriter().print(false); 
			   }
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	/**
	 * 更改密码
	 */
	@RequestMapping("/changePwd")
	public void changePwd(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   try {
			   TradeAccount ta=this.tradeAccountService.findById(new Long(request.getParameter("account_id")));
			   
			   System.out.println(ta.getUserId().toString().equals(userInfo.getId().toString()));
			   if(ta!=null&&ta.getId()!=null&&ta.getUserId().toString().equals(userInfo.getId().toString())) {
				    
				     ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
				
				     HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
				     
				      if(httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api")) {
				    	  org.json.JSONObject json=httpClient.updateLoginPwd(ta.getTradeId(),request.getParameter("account_pwd"));
				    	  
				    	  if(json.getString("retcode").equals("0 Done")) {
				    		  ta.setBackup6(request.getParameter("account_pwd"));
			                	 this.tradeAccountService.saveOrUpdate(ta);
			                	 response.getWriter().print(true);
					      }else {
					    	  response.getWriter().print(false); 
					      }
				      }else {
				    	  response.getWriter().print(false); 
				      }
			   }else {
				   response.getWriter().print(false); 
			   }
			   }catch(Exception e) {
				   System.out.println(e);
				   response.getWriter().print(false); 
			   }
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	/**
	 * 保存交易账号
	 */
	@RequestMapping("/saveBindAccount")
	public void saveBindAccount(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   
			 try {
						ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
						MyWebsocketClient4TradeAccount client= new MyWebsocketClient4TradeAccount(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						client.setTradeAccountService(this.tradeAccountService);
						client.setUserInfoService(this.userInfoService);
						    client.connect();
							Thread.sleep(300);
							if (client.getReadyState().equals(READYSTATE.OPEN)) {
								System.out.println("bindAccount   "+new Date()+" "+client.getReadyState());
								HashMap map=new HashMap();
								map.put("reqtype", "resetuserpswinfo");
								map.put("reqid", new Date().getTime()+"___"+userInfo.getId());
								map.put("login", new Integer(request.getParameter("jyzh")));
								map.put("type", 3); // 1为主交易密码  2为投资者密码 3 校验主密码
								map.put("oldpassword", request.getParameter("jymm"));
								JSONObject jsonObj=new JSONObject(map);
								client.send(jsonObj.toString());
							 }
							
			 }catch(Exception e) {
				 response.getWriter().print(false);
			 }
						
					
					
					
			   
			  
			   response.getWriter().print(true);
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	
	/**
	 * 进入绑定账户
	 */
	@RequestMapping("bindAccount")
	public String bindAccount(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			
			  return "/bindAccount";
						  
		}else {
			 return "/login";
		}
		
	}
	
	/**
	 * 进入同名转账
	 */
	@RequestMapping("toTransfer")
	public String toTransfer(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			    //查询当前CRM用户下所有的交易账号
			    TradeAccount ta_query=new TradeAccount();
			    ta_query.setIsAvailable(1);
			    ta_query.setUserId(myInfo.getId());
			    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
			    
			    for(int i=0;i<ta_page.getContent().size();i++) {
			    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
			    	if(ta.getType()!=null) {
			    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
			    	ta.setBackup3(aty.getTypeName());
			    	}
			    }
			    map.put("tradeList", ta_page.getContent());
			
			    return "/toTransfer";
						  
		}else {
			 return "/login";
		}
		
	}
	
	/**
	 * 保存交易账号
	 */
	@RequestMapping("/saveTranser")
	public void saveTranser(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {

		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   TransferInfo ti=new TransferInfo();
			   ti.setUserId(userInfo.getId());
			   TradeAccount ti_out=this.tradeAccountService.findById(new Long(request.getParameter("transferOut")));
			   ti.setTransferOut(new Integer(ti_out.getTradeId()));
			   TradeAccount ti_in=this.tradeAccountService.findById(new Long(request.getParameter("transferIn")));
			   ti.setTransferIn(new Integer(ti_in.getTradeId()));
			   ti.setTransferAmount(new Double(request.getParameter("transferAmount")));
			   
			   AccountType atype_pre=this.accountTypeService.findById(new Long(ti_out.getType()));
			   
			   if(atype_pre.getIsAutoAudit2()!=null&&atype_pre.getIsAutoAudit2().intValue()==1) {
				   ti.setAuditStatus(1);
			   }else {
				   ti.setAuditStatus(0);
			   }
			   ti.setOutStatus(0);
			   ti.setInStatus(0);
			   ti.setTransferStatus(0);
			   ti.setRemark(request.getParameter("remark"));
			   this.transferInfoService.saveOrUpdate(ti);
			   
			   EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
		       DepositSetting ds=(DepositSetting)this.depositSettingService.findById(1L);
		         
			   response.getWriter().print(true);
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	/**
	 * 进入同名转账
	 */
	@RequestMapping("transferList")
	public String transferList(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    String requestHeader = request.getHeader("user-agent");
			 if(IndexController.isMobileDevice(requestHeader)) {
		        	map.put("isMobile", 1);
		        }else {
		        	map.put("isMobile", 0);
		        }
			    //查询当前CRM用户下所有的转账记录
			    TransferInfo ti_query=new TransferInfo();
			    ti_query.setUserId(myInfo.getId());
			    Page<TransferInfo> ti_page=this.transferInfoService.findAll(0,100, "id", "desc", ti_query);
			    map.put("transferList", ti_page.getContent());
			    return "/transferList";
						  
		}else {
			 return "/login";
		}
		
	}
	
	
	/**
	 * 进入修改MT4交易密码
	 */
	@RequestMapping("modifyTradePwd")
	public String modifyTradePwd(Map<String, Object> map, HttpServletRequest request) {
		CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
		map.put("command", companyInfo);
		String requestHeader = request.getHeader("user-agent");
		 if(IndexController.isMobileDevice(requestHeader)) {
	        	map.put("isMobile", 1);
	        }else {
	        	map.put("isMobile", 0);
	        }
		if(request.getSession().getAttribute("loginUser")!=null) {
		    map.put("userInfo", request.getSession().getAttribute("loginUser"));
		    UserInfo myInfo=(UserInfo)request.getSession().getAttribute("loginUser");
		    //查询当前CRM用户下所有的交易账号
		    TradeAccount ta_query=new TradeAccount();
		    ta_query.setUserId(myInfo.getId());
		    ta_query.setIsAvailable(1);
		    Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1000, "id", "asc", ta_query);
		    
		    for(int i=0;i<ta_page.getContent().size();i++) {
		    	TradeAccount ta=(TradeAccount)ta_page.getContent().get(i);
		    	if(ta.getType()!=null) {
		    	AccountType aty=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
		    	ta.setBackup3(aty.getTypeName());
		    	}
		    }
		    map.put("tradeList", ta_page.getContent());
		    
		    return "/modifyTradePwd";
		}else {
			  return "/login";
		}
		
	}
	/**
	 * 保存修改的MT4交易密码
	 */
	@RequestMapping("saveMyTradePwd")
	public void saveMyTradePwd(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		
		if(request.getSession().getAttribute("loginUser")!=null) {
			   UserInfo userInfo_session=(UserInfo)request.getSession().getAttribute("loginUser");
			   UserInfo userInfo=this.userInfoService.findById(userInfo_session.getId());
			   
			   if(!userInfo.getPassword().equals(request.getParameter("memberPsd_old"))) {
				   response.getWriter().print(false);
			   }else {
				    TradeAccount ta=null;
				    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
				    	
				    	 ta=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("trade_id")));
				    	 
				    	 if(ta.getBackup5()!=null&&ta.getBackup5().equals("true")) {
				    		 try {
				 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
				 					WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
				 					client.connect();
				 					int mm=0;
									while (!client.getReadyState().equals(READYSTATE.OPEN)) {
										Thread.sleep(100);
										System.out.println("modifyPwd   "+new Date()+" "+client.getReadyState());
										mm=mm+1;
										if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
											client.reconnect();
										mm=0;
										}
									}
				 					    HashMap map=new HashMap();
				 					    map.put("reqtype", "resetuserpswinfo");
				 						map.put("reqid", new Date().getTime());
				 						map.put("login", new Integer(ta.getTradeId()));
				 						map.put("type", 1); // 1为主交易密码  2为投资者密码 3 校验主密码
				 						map.put("oldpassword", ta.getBackup6());
				 						map.put("newpassword", request.getParameter("memberPsd_new"));
				 					    JSONObject jsonObj=new JSONObject(map);
				 					    client.send(jsonObj.toString());
				 					    ta.setBackup6(request.getParameter("memberPsd_new"));
				 					    this.tradeAccountService.saveOrUpdate(ta);
				 					}catch(Exception e) {
				 						
				 					}
				 					 response.getWriter().print(true);
				    	 }else {
				    		 response.getWriter().print(false);
				    	 }
				   
			       }else {
			    	   response.getWriter().print(false);
			       }
			   }
			
		 
		}else {
			response.getWriter().print(false);
		}
		
	}
	
	
	/**
	  * 切换语言
	 */
	@RequestMapping("/toSetLanguage")
	public void toSetLanguage(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
			throws IOException, ServletException {
		//System.out.println("type"+request.getParameter("type"));
		
		if(request.getParameter("type").equals("2")) {
			request.getSession().setAttribute("i18n_language_session","zh_TW");
		}else if(request.getParameter("type").equals("3")) {
			request.getSession().setAttribute("i18n_language_session","en_US");
		}else {
			request.getSession().setAttribute("i18n_language_session","zh_CN");
		}

		 response.getWriter().print(true);
		
	}
	
	/*
	 * 支付接口
	 */
	public String toPayInfo(Long _payID,String _memberName,String _amount,String _depositID){
		
		String memberName=_memberName;
		String amount=_amount;
		Long payID=_payID;
		
		ThirdPartyPayment tpp=this.thirdPartyPaymentService.findById(payID);
		String orderID=tpp.getOrderPrefix()+new Date().getTime()+_depositID;
		
		
		PaymentParams pps_query=new PaymentParams();
		pps_query.setPaymentId(payID);
		Page<PaymentParams> tpp_page=this.paymentParamsService.findAll(0, 100, "sort", "asc", pps_query);
		
		
		String signStr="";
		String sign="";
		String finalStr="";
		
		String status_key="";
		String status_str="";
		String data_key="";
		
		for(int i=0;i<tpp_page.getContent().size();i++) {
			PaymentParams param1=(PaymentParams)tpp_page.getContent().get(i);
			
			String paramName="";
			String paramValue="";
			if(i==0) {
				   paramName=param1.getParamName();
				if(param1.getParamType().intValue()==1) {
					paramValue=amount;
				}else if(param1.getParamType().intValue()==2) {
					paramValue=orderID;
				}else if(param1.getParamType().intValue()==3) {
					paramValue=memberName;
				}else if(param1.getParamType().intValue()==6) {
					paramValue=param1.getParamValue();
				}else if(param1.getParamType().intValue()==4) {
					status_key=param1.getParamName();
					status_str=param1.getParamValue();
				}else if(param1.getParamType().intValue()==5) {
					data_key=param1.getParamName();
				}
			}else {
				 paramName="&"+param1.getParamName();
				if(param1.getParamType().intValue()==1) {
					paramValue=amount;
				}else if(param1.getParamType().intValue()==2) {
					paramValue=orderID;
				}else if(param1.getParamType().intValue()==3) {
					paramValue=memberName;
				}else if(param1.getParamType().intValue()==6) {
					paramValue=param1.getParamValue();
				}else if(param1.getParamType().intValue()==4) {
					if(param1.getIsReturn()!=null&&param1.getIsReturn().intValue()==1) {
					status_key=param1.getParamName();
					status_str=param1.getParamValue();
					}
				}else if(param1.getParamType().intValue()==5) {
					if(param1.getIsReturn()!=null&&param1.getIsReturn().intValue()==1) {
					data_key=param1.getParamName();
					}
				}
				
			}
			
			
			if(tpp.getSignType()!=null&&tpp.getSignType().intValue()==1) {//方式1(SHA1)加密
				if(param1.getBackup1()!=null&&param1.getBackup1().equals("1")) {//是加密参数
					signStr=signStr+paramName.replace("&","")+paramValue;
				}
			}else {
				//待完善其他sign加密方式
			}
			
			
			if(param1.getIsSubmit()!=null&&param1.getIsSubmit().intValue()==1) {//是提交参数
			      finalStr=finalStr+paramName+"="+paramValue;
			}
			
		}
		
		if(tpp.getSignType()!=null&&tpp.getSignType().intValue()==1) {//方式1(SHA1)加密
			signStr=signStr.replace("KEY_NOSHOW","");
			sign=getSha1(signStr).toUpperCase();
		}else {
			//待完善其他sign加密方式
		}
		
		finalStr=finalStr+"&sign="+sign;
		
	    String sr="";
	    
	    
	    
	  
	    
	    try {
	    	System.out.println(finalStr);
	    	
	    	sr=reqPost(tpp.getRequestUrl(), finalStr);
	    }catch(Exception e) {
	    	
	    }
	    
	    RequestRecord rr=new RequestRecord();
	    rr.setOrderId(orderID);
	    rr.setSignStr(sign);
	    rr.setAmount(new Double(_amount));
	    rr.setAccount(_memberName);
	    rr.setStatus(0);
	    rr.setDespoitId(new Long(_depositID));
	    rr.setReturnStr(sr);
	    this.requestRecordService.saveOrUpdate(rr);
	    
	    JSONObject jsonObject = JSONObject.parseObject(sr);
	    
	    String returnStr="";
	    if(jsonObject.getString(status_key).equals(status_str)) {//返回成功
	    	returnStr=jsonObject.getString(data_key);
	    }else {
	    	returnStr="error";
	    }
		return returnStr;
	}
	
	public static String getSha1(String str) {

        char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f' };
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }
	
	 public static String unicodeDecode(String string) {
	        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
	        Matcher matcher = pattern.matcher(string);
	        char ch;
	        while (matcher.find()) {
	            ch = (char) Integer.parseInt(matcher.group(2), 16);
	            string = string.replace(matcher.group(1), ch + "");
	        }
	        return string;
	    }
	 
	 /** post请求 */
		public static String reqPost(String url, String param) throws IOException {
			String res = "";
			  URL realUrl = new URL(url);
	            // 打开和URL之间的连接
	            URLConnection conn = realUrl.openConnection();
	           // PrintWriter out = null;
	            OutputStreamWriter out = null;
			conn.setDoOutput(true); // 必须设置这两个请求属性为true，就表示默认使用POST发送
			conn.setDoInput(true);
			conn.setRequestProperty("charsert", "utf-8");
			conn.setRequestProperty("accept", "*/*");
			   conn.setRequestProperty("connection", "Keep-Alive");
			   conn.setRequestProperty("user-agent",
			     "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			   conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			   conn.setRequestProperty("method","POST");
			// 请求参数必须使用conn获取的OutputStream输出到请求体参数
			// 用PrintWriter进行包装
			//out = new PrintWriter(conn.getOutputStream());
	        // 发送请求参数
	       // out.println(param.trim());
			   
			   out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
			   // 发送请求参数
			   out.write(param);
			   out.flush(); // 立即充刷至请求体）PrintWriter默认先写在内存缓存中
			
			try// 发送正常的请求（获取资源）
			{
				BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
				String line;
				while ((line = in.readLine()) != null) {
					res += line + "\n";
				}
			} catch (Exception e) {
				e.printStackTrace(); 
				
			}
			return res;
		}
		
		/**
		 * 验证MT4交易密码
		 */
		@RequestMapping("validMyTradePwd")
		public void validMyTradePwd(HttpServletRequest request, HttpServletResponse response, Map<String, Object> model)
				throws IOException, ServletException {
			
			if(request.getSession().getAttribute("loginUser")!=null) {
				 
				  TradeAccount ta=null;
				    if(request.getParameter("trade_id")!=null&&!request.getParameter("trade_id").equals("")) {
				    	
				    	 ta=(TradeAccount)this.tradeAccountService.findById(new Long(request.getParameter("trade_id")));
					    	System.out.println("TradeAccount_tradeID:"+ta.getTradeId()+"   PSD:"+request.getParameter("memberPsd_old2"));
					    	 try {
									ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
									MyWebsocketClient4TradeAccount2 client= new MyWebsocketClient4TradeAccount2(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
									client.setTradeAccountService(this.tradeAccountService);
									client.setUserInfoService(this.userInfoService);
									    client.connect();
										Thread.sleep(300);
										System.out.println("进入密码验证");
										if (client.getReadyState().equals(READYSTATE.OPEN)) {
											HashMap map=new HashMap();
											map.put("reqtype", "resetuserpswinfo");
											map.put("reqid", new Date().getTime()+"_"+ta.getTradeId());
											map.put("login", new Integer(ta.getTradeId()));
											map.put("type", 3); // 1为主交易密码  2为投资者密码 3 校验主密码
											map.put("oldpassword", request.getParameter("memberPsd_old2"));
											JSONObject jsonObj=new JSONObject(map);
											client.send(jsonObj.toString());
										 }
										
						 }catch(Exception e) {
							 response.getWriter().print(false);
						 }
								
		 					 response.getWriter().print(true);
				       }else {
				    	   response.getWriter().print(false);
				       }
				  
				
			 
			}else {
				response.getWriter().print(false);
			}
			
		}
		
		public  String createApplicant(String externalUserId, String levelName,String email,String phone) throws IOException, NoSuchAlgorithmException, InvalidKeyException {

	        Applicant applicant = new Applicant(externalUserId);
	        CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
	       
	        applicant.setSourceKey(companyInfo.getBackup2());
	        applicant.setEmail(email);
	        applicant.setPhone(phone);
	        Response response = sendPost(
	                "/resources/applicants?levelName=" + levelName,
	                RequestBody.create( MediaType.parse("application/json; charset=utf-8"),objectMapper.writeValueAsString(applicant)));

	        ResponseBody responseBody = response.body();

	        return responseBody != null ? objectMapper.readValue(responseBody.string(), Applicant.class).getId() : null;
	    }

	    public  String getAccessToken(String externalUserId, String levelName) throws NoSuchAlgorithmException, InvalidKeyException, IOException {

	        Response response = sendPost("/resources/accessTokens?userId=" + externalUserId + "&levelName=" + levelName, RequestBody.create(null,new byte[0]));

	        ResponseBody responseBody = response.body();
	        return responseBody != null ? responseBody.string() : null;
	    }
	    

	    public  String resetApplicants(String applicantId) throws NoSuchAlgorithmException, InvalidKeyException, IOException {

	        Response response = sendPost("/resources/applicants/"+applicantId+"/reset" , RequestBody.create(null,new byte[0]));

	        ResponseBody responseBody = response.body();
	        return responseBody != null ? responseBody.string() : null;
	    }

	    private static Response sendPost(String url, RequestBody requestBody) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
	        long ts = Instant.now().getEpochSecond();

	        Request request = new Request.Builder()
	                .url(SUMSUB_TEST_BASE_URL + url)
	                .header("X-App-Token", SUMSUB_APP_TOKEN)
	                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.POST, url, requestBodyToBytes(requestBody)))
	                .header("X-App-Access-Ts", String.valueOf(ts))
	                .post(requestBody)
	                .build();

	        Response response = new OkHttpClient().newCall(request).execute();

	        if (response.code() != 200 && response.code() != 201) {
	            // https://developers.sumsub.com/api-reference/#errors
	            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
	            // Then perhaps you should throw the exception. (depends on the logic of your code)
	        }
	        return response;
	    }

	    private static Response sendGet(String url) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
	        long ts = Instant.now().getEpochSecond();

	        Request request = new Request.Builder()
	                .url(SUMSUB_TEST_BASE_URL + url)
	                .header("X-App-Token", SUMSUB_APP_TOKEN)
	                .header("X-App-Access-Sig", createSignature(ts, HttpMethod.GET, url, null))
	                .header("X-App-Access-Ts", String.valueOf(ts))
	                .get()
	                .build();

	        Response response = new OkHttpClient().newCall(request).execute();

	        if (response.code() != 200 && response.code() != 201) {
	            // https://developers.sumsub.com/api-reference/#errors
	            // If an unsuccessful answer is received, please log the value of the "correlationId" parameter.
	            // Then perhaps you should throw the exception. (depends on the logic of your code)
	        }
	        return response;
	    }

	    private static String createSignature(long ts, HttpMethod httpMethod, String path, byte[] body) throws NoSuchAlgorithmException, InvalidKeyException {
	        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
	        hmacSha256.init(new SecretKeySpec(SUMSUB_SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
	        hmacSha256.update((ts + httpMethod.name() + path).getBytes(StandardCharsets.UTF_8));
	        byte[] bytes = body == null ? hmacSha256.doFinal() : hmacSha256.doFinal(body);
	        return Hex.encodeHexString(bytes);
	    }

	    public static byte[] requestBodyToBytes(RequestBody requestBody) throws IOException {
	        Buffer buffer = new Buffer();
	        requestBody.writeTo(buffer);
	        return buffer.readByteArray();
	    }
		
	    @RequestMapping("/getVerifyCodeImg")
	    public void getVerifyCodeImg(HttpServletResponse response, HttpSession session) {
	        ByteArrayOutputStream output = new ByteArrayOutputStream();
	        String code = VerifyCodeUtil.drawImage(output);
	        //将验证码文本直接存放到session中
	        session.setAttribute("verifyCode", code);
	        try {
	            ServletOutputStream out = response.getOutputStream();
	            output.writeTo(out);
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }
	    public  Map getSetting(Map<String, Object> map) {
	    	
	    	map.put("CRM_TITLE", Config.CRM_TITLE);
	    	map.put("CRM_LOGO1", Config.CRM_LOGO1);
	    	map.put("CRM_LOGO2", Config.CRM_LOGO2);
	    	map.put("HEADER_SCRIPT_STATUS", Config.HEADER_SCRIPT_STATUS);
	    	map.put("JS_ID1", Config.JS_ID1);
	    	map.put("JS_ID2", Config.JS_ID2);
	    	map.put("JS_ID3", Config.JS_ID3);
	    	map.put("JS_KEY", Config.JS_KEY);
	    	map.put("FOOTER_SCRIPT_STATUS", Config.FOOTER_SCRIPT_STATUS);
	    	map.put("WEB_TRADER_STATUS", Config.WEB_TRADER_STATUS);
	    	map.put("WEB_TRADER_URL", Config.WEB_TRADER_URL);
	    	map.put("SUPPORT_STATUS", Config.SUPPORT_STATUS);
	    	map.put("SUPPORT_URL", Config.SUPPORT_URL);
	    	map.put("BANNER_IMG", Config.BANNER_IMG);
	    	map.put("BANNER_URL", Config.BANNER_URL);
	    	map.put("CRM_VERSION", Config.CRM_VERSION);
	    	map.put("CRM_LICENSED", Config.CRM_LICENSED);
	    	map.put("APP_URL1", Config.APP_URL1);
	    	map.put("APP_URL2", Config.APP_URL2);
	    	map.put("APP_URL3", Config.APP_URL3);
	    	map.put("APP_URL4", Config.APP_URL4);
	    	map.put("APP_URL5", Config.APP_URL5);
	    	map.put("APP_URL6", Config.APP_URL6);
	    	map.put("APP_URL7", Config.APP_URL7);
	    	map.put("APP_URL8", Config.APP_URL8);
	    	map.put("APP3_STATUS", Config.APP3_STATUS);
	    	map.put("FOOTER2_SCRIPT_STATUS", Config.FOOTER2_SCRIPT_STATUS);
	    	map.put("JS2_KEY", Config.JS2_KEY);
	    	map.put("JS2_ID", Config.JS2_ID);
	    	
	    	return map;
	    }
}

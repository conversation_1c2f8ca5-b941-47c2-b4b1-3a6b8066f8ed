package com.ews.common;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.alibaba.fastjson.JSONObject;
import com.ews.HttpClient;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserGroup;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserGroupService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;
import com.wx.core.util.HttpClientUtil;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@EnableAsync 
public class SaticScheduleTask3 {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private UserGroupService userGroupService;
	
	
	@Autowired
	private ReckbackInfoService reckbackInfoService;
	//是否开启自动轮询功能 true 开启   false  关闭
	/*目前只用于生成返佣和返佣操作，出入金 */

	private boolean bl2=true;    //查询已生成的历史订单生成返佣    
	private boolean bl3=true;   //返佣操作                          
	private boolean bl6=true;   //出 金                             
	private boolean bl7=true;   //入 金                            
	
	//备用  
	private boolean bl13=false;   //补发佣金
	private boolean mainSwitch=true;    //总开关
	
    
   //查询历史订单进行返佣   每10分钟查询一次，延迟5分钟
	//@Async
    @Scheduled(fixedRate=600000,initialDelay=300000)
	public void configureTasks6() {
    	if(bl2&&mainSwitch) {
      
    		try {
      			OrderInfo order_query=new OrderInfo();
      			order_query.setStatus(2);
      			order_query.setIsRakeback(0);
      			List typeList=new ArrayList();
      			typeList.add(1);
      			typeList.add(0);
      			
      			order_query.setTypeList(typeList);
      			Page<OrderInfo> order_page=this.orderInfoService.findAll(0,1000, "id", "asc", order_query);
      			for(int i=0;i<order_page.getContent().size();i++) {
      				OrderInfo orderInfo=(OrderInfo)order_page.getContent().get(i);
      				if(orderInfo.getTradeId()!=null&&!orderInfo.getTradeId().toString().equals("")) {
      					TradeAccount ta=this.tradeAccountService.findById(orderInfo.getTradeId());
      					if(ta.getUserId()!=null) {
      		  				UserInfo userInfo=this.userInfoService.findById(ta.getUserId());
      		  				if(userInfo.getParentId()!=null) {
      		  				Long trade_id=orderInfo.getTradeId();//交易账号的CRM系统ID
      		  				String symbol=orderInfo.getSymbol();//交易品种
      		  				String loginID=orderInfo.getLoginId();//MT交易账号
      		  				Long crm_user_id=ta.getUserId();  //CRM用户ID
      		  				String group_name=ta.getGroupName();//用户组 
      		  				Long parent_id=userInfo.getParentId();// 代理用户ID
      		  				Double qty=orderInfo.getVolume();//手数
      		  				String order_id=orderInfo.getOrderNo();
      		  				//System.out.println(trade_id+" "+symbol+" "+loginID+" "+crm_user_id+" "+group_name+" "+parent_id);
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				//迭代进行记录待返佣记录
      		  				if(parent_id!=null) {
      		  				this.toDoRekeBack(order_id,trade_id, symbol, loginID, crm_user_id, group_name, parent_id, qty, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,1);
      		  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				}
      		  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      		  				}
      	  				}else {
      		  				orderInfo.setIsRakeback(1);
      		  				this.orderInfoService.saveOrUpdate(orderInfo);
      	  				}
      				}else {
      				    	orderInfo.setIsRakeback(1);
    		  				this.orderInfoService.saveOrUpdate(orderInfo);
      				}
      			}
      		}catch(Exception e) {
      			System.out.println(e);
      		}
    	}
    	
    }
    public void toDoRekeBack(String order_id,Long trade_id,String symbol,String loginID,Long crm_user_id,String group_name,Long parent_id,Double qty,Double a1,Double a2,Double a3,Double b1,Double b2,Double b3,Double b4,Double b5,Double b6,Double b7,Double b8,Double b9,Double b10,Double b11,Double b12,Double b13,Double b14,Double b15,Double b16,Double b17,Double b18,Double b19,Double b20,Double b21,Double b22,Double b23,Double b24,Double b25,Double b26,Double b27,Double b28,Double b29,Double b30,int level) {
  		try {
  			
  			//System.out.println("lsjl:"+order_id+" "+trade_id+" "+symbol+" "+loginID+" "+crm_user_id+" "+group_name+" "+parent_id+" "+qty+" "+level);
  			//System.out.println(parent_id);
  			Double aa1=a1;//总监
  			Double aa2=a2;//经理
  			Double aa3=a3;//销售
  			Double bb1=b1;//1级
  			Double bb2=b2;//2级
  			Double bb3=b3;//3级
  			Double bb4=b4;//4级
  			Double bb5=b5;//5级
  			Double bb6=b6;//6级
  			Double bb7=b7;//7级
  			Double bb8=b8;//8级
  			Double bb9=b9;//9级
  			Double bb10=b10;//10级
  			Double bb11=b11;//11级
  			Double bb12=b12;//12级
  			Double bb13=b13;//13级
  			Double bb14=b14;//14级
  			Double bb15=b15;//15级
  			Double bb16=b16;//16级
  			Double bb17=b17;//17级
  			Double bb18=b18;//18级
  			Double bb19=b19;//19级
  			Double bb20=b20;//20级
  			Double bb21=b21;//21级
  			Double bb22=b22;//22级
  			Double bb23=b23;//23级
  			Double bb24=b24;//24级
  			Double bb25=b25;//25级
  			Double bb26=b26;//26级
  			Double bb27=b27;//27级
  			Double bb28=b28;//28级
  			Double bb29=b29;//29级
  			Double bb30=b30;//30级
  			if(parent_id!=null) {
  			Long pparent_id=null;
  			User u=(User)this.userService.findUserById(parent_id);
				if(u.getReId()!=null&&u.getReId().intValue()!=1&&u.getReId().intValue()!=0) {
					pparent_id=u.getReId();
			    }
  			
			if(level==1) {
		  			List rekebackList=this.dataSqlService.queryRekeBackAmount(parent_id, group_name, symbol);
		  			
		  			if(rekebackList.size()>0) {
		  				
		  				Object obj[]=(Object[])rekebackList.get(0);
		  	  			
		  				if(aa1==null) {
			  	  			if(obj[0]!=null) {
			  	  				aa1=new Double(obj[0].toString());
			  	  			}else {
			  	  			aa1=0D;
			  	  			}
		  				}
		  				if(aa2==null) {
		  	  			if(obj[1]!=null) {
			  				aa2=new Double(obj[1].toString());
			  			}else {
			  	  			aa2=0D;
			  	  			}
		  				}
		  				if(aa3==null) {
				  	  	if(obj[2]!=null) {
								aa3=new Double(obj[2].toString());
							}else {
				  	  			aa3=0D;
			  	  			}
		  				}
		  				if(bb1==null) {
					  	  if(obj[3]!=null) {
					  		bb1=new Double(obj[3].toString());
								}else {
					  	  			bb1=0D;
				  	  			}
		  				}
		  				if(bb2==null) {
					  	if(obj[4]!=null) {
					  		bb2=new Double(obj[4].toString());
							}else {
				  	  			bb2=0D;
			  	  			}
		  				}
		  				if(bb3==null) {
					  	if(obj[5]!=null) {
					  		bb3=new Double(obj[5].toString());
							}else {
				  	  			bb3=0D;
			  	  			}
		  				}
		  				
		  				if(bb4==null) {
						  	if(obj[6]!=null) {
						  		bb4=new Double(obj[6].toString());
								}else {
					  	  			bb4=0D;
				  	  			}
			  				}
		  				
		  				if(bb5==null) {
						  	if(obj[7]!=null) {
						  		bb5=new Double(obj[7].toString());
								}else {
					  	  			bb5=0D;
				  	  			}
			  				}
		  				
		  				if(bb6==null) {
						  	if(obj[8]!=null) {
						  		bb6=new Double(obj[8].toString());
								}else {
					  	  			bb6=0D;
				  	  			}
			  				}
		  				
		  				if(bb7==null) {
						  	if(obj[9]!=null) {
						  		bb7=new Double(obj[9].toString());
								}else {
					  	  			bb7=0D;
				  	  			}
			  				}
		  				
		  				if(bb8==null) {
						  	if(obj[10]!=null) {
						  		bb8=new Double(obj[10].toString());
								}else {
					  	  			bb8=0D;
				  	  			}
			  				}
		  				
		  				if(bb9==null) {
						  	if(obj[11]!=null) {
						  		bb9=new Double(obj[11].toString());
								}else {
					  	  			bb9=0D;
				  	  			}
			  				}
		  				
		  				if(bb10==null) {
						  	if(obj[12]!=null) {
						  		bb10=new Double(obj[12].toString());
								}else {
					  	  			bb10=0D;
				  	  			}
			  				}
		  				
		  				if(bb11==null) {
						  	if(obj[13]!=null) {
						  		bb11=new Double(obj[13].toString());
								}else {
					  	  			bb11=0D;
				  	  			}
			  				}
		  				
		  				if(bb12==null) {
						  	if(obj[14]!=null) {
						  		bb12=new Double(obj[14].toString());
								}else {
					  	  			bb12=0D;
				  	  			}
			  				}
		  				
		  				if(bb13==null) {
						  	if(obj[15]!=null) {
						  		bb13=new Double(obj[15].toString());
								}else {
					  	  			bb13=0D;
				  	  			}
			  				}
		  				
		  				if(bb14==null) {
						  	if(obj[16]!=null) {
						  		bb14=new Double(obj[16].toString());
								}else {
					  	  			bb14=0D;
				  	  			}
			  				}
		  				
		  				if(bb15==null) {
						  	if(obj[17]!=null) {
						  		bb15=new Double(obj[17].toString());
								}else {
					  	  			bb15=0D;
				  	  			}
			  				}
		  				
		  				if(bb16==null) {
						  	if(obj[18]!=null) {
						  		bb16=new Double(obj[18].toString());
								}else {
					  	  			bb16=0D;
				  	  			}
			  				}
		  				
		  				if(bb17==null) {
						  	if(obj[19]!=null) {
						  		bb17=new Double(obj[19].toString());
								}else {
					  	  			bb17=0D;
				  	  			}
			  				}
		  				
		  				if(bb18==null) {
						  	if(obj[20]!=null) {
						  		bb18=new Double(obj[20].toString());
								}else {
					  	  			bb18=0D;
				  	  			}
			  				}
		  				
		  				if(bb19==null) {
						  	if(obj[21]!=null) {
						  		bb19=new Double(obj[21].toString());
								}else {
					  	  			bb19=0D;
				  	  			}
			  				}
		  				
		  				if(bb20==null) {
						  	if(obj[22]!=null) {
						  		bb20=new Double(obj[22].toString());
								}else {
					  	  			bb20=0D;
				  	  			}
			  				}
		  				
		  				if(bb21==null) {
						  	if(obj[23]!=null) {
						  		bb21=new Double(obj[23].toString());
								}else {
					  	  			bb21=0D;
				  	  			}
			  				}
		  				
		  				if(bb22==null) {
						  	if(obj[24]!=null) {
						  		bb22=new Double(obj[24].toString());
								}else {
					  	  			bb22=0D;
				  	  			}
			  				}
		  				
		  				if(bb23==null) {
						  	if(obj[25]!=null) {
						  		bb23=new Double(obj[25].toString());
								}else {
					  	  			bb23=0D;
				  	  			}
			  				}
		  				
		  				if(bb24==null) {
						  	if(obj[26]!=null) {
						  		bb24=new Double(obj[26].toString());
								}else {
					  	  			bb24=0D;
				  	  			}
			  				}
		  				
		  				if(bb25==null) {
						  	if(obj[27]!=null) {
						  		bb25=new Double(obj[27].toString());
								}else {
					  	  			bb25=0D;
				  	  			}
			  				}
		  				
		  				if(bb26==null) {
						  	if(obj[28]!=null) {
						  		bb26=new Double(obj[28].toString());
								}else {
					  	  			bb26=0D;
				  	  			}
			  				}
		  				
		  				if(bb27==null) {
						  	if(obj[29]!=null) {
						  		bb27=new Double(obj[29].toString());
								}else {
					  	  			bb27=0D;
				  	  			}
			  				}
		  				
		  				if(bb28==null) {
						  	if(obj[30]!=null) {
						  		bb28=new Double(obj[30].toString());
								}else {
					  	  			bb28=0D;
				  	  			}
			  				}
		  				
		  				if(bb29==null) {
						  	if(obj[31]!=null) {
						  		bb29=new Double(obj[31].toString());
								}else {
					  	  			bb29=0D;
				  	  			}
			  				}
		  				
		  				if(bb30==null) {
						  	if(obj[32]!=null) {
						  		bb30=new Double(obj[32].toString());
								}else {
									bb30=0D;
				  	  			}
			  				}
					  	  
		  			}else {
		  				
		  				
		  			}
  			
			}
  			
  			
  			    //begin
				  		    int reckback_type=0;
					  	    Double reckback_amount=0D;
					  	
					  	    if(u.getRoleType().intValue()==0) {//后台管理员
							}else if(u.getRoleType().intValue()==1) {//代理
								if(level==1) {//1级代理
									reckback_type=1;
									reckback_amount=bb1*qty;
				  	  			}else if(level==2) {//2级代理
				  	  			reckback_type=2;
				  	  		        reckback_amount=bb2*qty;
				  	  			}else if(level==3) {//3级代理
				  	  			reckback_type=3;
				  	  		        reckback_amount=bb3*qty;
				  	  			}else if(level==4) {//4级代理
				  	  			reckback_type=104;
				  	  		        reckback_amount=bb4*qty;
				  	  			}else if(level==5) {//5级代理
				  	  			reckback_type=105;
				  	  		        reckback_amount=bb5*qty;
				  	  			}else if(level==6) {//6级代理
				  	  			reckback_type=106;
				  	  		        reckback_amount=bb6*qty;
				  	  			}else if(level==7) {//7级代理
				  	  			reckback_type=107;
				  	  		        reckback_amount=bb7*qty;
				  	  			}else if(level==8) {//8级代理
				  	  			reckback_type=108;
				  	  		        reckback_amount=bb8*qty;
				  	  			}else if(level==9) {//9级代理
				  	  			reckback_type=109;
				  	  		        reckback_amount=bb9*qty;
				  	  			}else if(level==10) {//10级代理
				  	  			reckback_type=110;
				  	  		        reckback_amount=bb10*qty;
				  	  			}else if(level==11) {//11级代理
				  	  			reckback_type=111;
				  	  		        reckback_amount=bb11*qty;
				  	  			}else if(level==12) {//12级代理
				  	  			reckback_type=112;
				  	  		        reckback_amount=bb12*qty;
				  	  			}else if(level==13) {//13级代理
				  	  			reckback_type=113;
				  	  		        reckback_amount=bb13*qty;
				  	  			}else if(level==14) {//14级代理
				  	  			reckback_type=114;
				  	  		        reckback_amount=bb14*qty;
				  	  			}else if(level==15) {//15级代理
				  	  			reckback_type=115;
				  	  		        reckback_amount=bb15*qty;
				  	  			}else if(level==16) {//16级代理
				  	  			reckback_type=116;
				  	  		        reckback_amount=bb16*qty;
				  	  			}else if(level==17) {//17级代理
				  	  			reckback_type=117;
				  	  		        reckback_amount=bb17*qty;
				  	  			}else if(level==18) {//18级代理
				  	  			reckback_type=118;
				  	  		        reckback_amount=bb18*qty;
				  	  			}else if(level==19) {//19级代理
				  	  			reckback_type=119;
				  	  		        reckback_amount=bb19*qty;
				  	  			}else if(level==20) {//20级代理
				  	  			reckback_type=120;
				  	  		        reckback_amount=bb20*qty;
				  	  			}else if(level==21) {//21级代理
				  	  			reckback_type=121;
				  	  		        reckback_amount=bb21*qty;
				  	  			}else if(level==22) {//22级代理
				  	  			reckback_type=122;
				  	  		        reckback_amount=bb22*qty;
				  	  			}else if(level==23) {//23级代理
				  	  			reckback_type=123;
				  	  		        reckback_amount=bb23*qty;
				  	  			}else if(level==24) {//24级代理
				  	  			reckback_type=124;
				  	  		        reckback_amount=bb24*qty;
				  	  			}else if(level==25) {//25级代理
				  	  			reckback_type=125;
				  	  		        reckback_amount=bb25*qty;
				  	  			}else if(level==26) {//26级代理
				  	  			reckback_type=126;
				  	  		        reckback_amount=bb26*qty;
				  	  			}else if(level==27) {//27级代理
				  	  			reckback_type=127;
				  	  		        reckback_amount=bb27*qty;
				  	  			}else if(level==28) {//28级代理
				  	  			reckback_type=128;
				  	  		        reckback_amount=bb28*qty;
				  	  			}else if(level==29) {//29级代理
				  	  			reckback_type=129;
				  	  		        reckback_amount=bb29*qty;
				  	  			}else if(level==30) {//30级代理
				  	  			reckback_type=130;
				  	  		        reckback_amount=bb30*qty;
				  	  			}
							}else {
								if(u.getRoleType().intValue()==2) {//销售
									reckback_type=6;
									reckback_amount=aa3*qty;
								}else if(u.getRoleType().intValue()==3) {//经理
									reckback_type=7;
									reckback_amount=aa2*qty;
								}else if(u.getRoleType().intValue()==4) {//总监
									reckback_type=8;
									reckback_amount=aa1*qty;
								}
								
							}
					  	    
					  	    //存返佣记录
					  	     ReckbackInfo rbif=new ReckbackInfo();
					  	     rbif.setUserId(parent_id);
					  	     rbif.setCrmUserId(crm_user_id);
					  	     rbif.setCrmOrderId(order_id);
					  	     rbif.setCrmTradeId(loginID);
					  	     rbif.setTradeQty(qty);
					  	     rbif.setGroupName(group_name);
					  	     rbif.setSymbol(symbol);
					  	     rbif.setReckbackTradeId(u.getStoreId()!=null?u.getStoreId().toString():"");
					  	     rbif.setReckbackStatus(0);
					  	     rbif.setReckbackType(reckback_type);
					  	     rbif.setReckbackAmount(reckback_amount);
					  	     rbif.setBackup1(u.getNickName());
					  	     if(rbif.getReckbackAmount()>0&&!rbif.getReckbackTradeId().equals("")) {
					  	    	 
					  	    	ReckbackInfo ri_query=new ReckbackInfo();
					  	    	ri_query.setCrmOrderId(order_id);
					  	    	ri_query.setReckbackTradeId(u.getStoreId()!=null?u.getStoreId().toString():"");
					  	    	
					  	    	Page<ReckbackInfo> ri_page=this.reckbackInfoService.findAll(0,1, "id", "asc", ri_query);
					  	    	if(ri_page.getContent().size()<1) {
					  	    	this.reckbackInfoService.saveOrUpdate(rbif);
					  	    	}
					  	    	
					  	     }else {
					  	     }
  			    //end
  		       this.toDoRekeBack(order_id,trade_id, symbol, loginID, crm_user_id, group_name, pparent_id, qty, aa1, aa2, aa3, bb1, bb2, bb3,bb4,bb5,bb6,bb7,bb8,bb9,bb10,bb11,bb12,bb13,bb14,bb15,bb16,bb17,bb18,bb19,bb20,bb21,bb22,bb23,bb24,bb25,bb26,bb27,bb28,bb29,bb30,level+1);
  			}
  			
  			
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
    //发放佣金   每300秒执行一次，延迟45秒
   // @Async
    @Scheduled(fixedRate=300000,initialDelay=45000)
    public void configureTasks7() {
    	if(bl3&&mainSwitch) {
           
        ReckbackInfo rbif_query=new ReckbackInfo();
        rbif_query.setReckbackStatus(0);
        Page<ReckbackInfo> ti_page=this.reckbackInfoService.findAll(0,50, "id", "asc", rbif_query);
        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
        HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
		boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
		      
        
        if(ti_page.getContent().size()>0) {
		     
		  		try {
		  			if(blll) {
		 				for(int i=0;i<ti_page.getContent().size();i++) {
				    		ReckbackInfo entity  = ti_page.getContent().get(i);
				    		entity.setReckbackStatus(99);
				    		this.reckbackInfoService.saveOrUpdate(entity);
				    		
				    		
					 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(entity.getReckbackTradeId()),"2",String.valueOf(entity.getReckbackAmount()),"Co."+entity.getCrmTradeId()+"-"+entity.getCrmOrderId());
					 			   
					 			   System.out.println(String.valueOf(entity.getReckbackTradeId())+"  "+String.valueOf(entity.getReckbackAmount())+"    "+("Co."+entity.getCrmTradeId()+"-"+entity.getCrmOrderId())+"    "+json.toString());
									this.reckbackInfoService.saveOrUpdate(entity); 
					 			   
					 			   if(json.getString("retcode").equals("0 Done"))  {
					 				    entity.setReckbackStatus(1);
						            	entity.setReckbackOrderId(json.getJSONObject("answer").getString("ticket"));
										this.reckbackInfoService.saveOrUpdate(entity); 
					 			  }else {
						            	entity.setReckbackStatus(2);
						            	entity.setBackup4(json.toString());
										this.reckbackInfoService.saveOrUpdate(entity); 
						            }
				    		}
		 			 }
			    	
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
        }
    	}
    	
    }
    
    //查询转账出金   每1分钟查询一次，延迟5分钟
    //@Async
    @Scheduled(fixedRate=5000,initialDelay=370000)
    public void configureTasks4() {
    	if(bl6&&mainSwitch) {
    	TransferInfo ti_query=new TransferInfo();
    	
    	ti_query.setAuditStatus(1);
    	ti_query.setTransferStatus(0);
    	ti_query.setOutStatus(0);
    	
    	Page<TransferInfo> ti_page=this.transferInfoService.findAll(0,1, "id", "asc", ti_query);
    	
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
        HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
    	boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
    	if(ti_page.getContent().size()>0) {
		    	
		  		try {
		  			if(blll) {
		  			for(int i=0;i<ti_page.getContent().size();i++) {
			    	TransferInfo entity  = ti_page.getContent().get(i);
			    	entity.setTransferStatus(99);
		    		this.transferInfoService.saveOrUpdate(entity);
		    		
			 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(entity.getTransferOut()),"2",String.valueOf(entity.getTransferAmount()*-1),"Transfer Out");
			 			   if(json.getString("retcode").equals("0 Done"))  {
			 					entity.setOutStatus(1);
				            	entity.setTransferStatus(1);
				            	entity.setOutOrderId(json.getJSONObject("answer").getString("ticket"));
								this.transferInfoService.saveOrUpdate(entity); 
								
								    TradeAccount query=new TradeAccount();
	 		    					query.setTradeId(String.valueOf(entity.getTransferOut()));
	 		    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
	 		    					if(ta_page.getContent().size()>0) {
	 		    						TradeAccount tasss=(TradeAccount)ta_page.getContent().get(0);
	 		    						
	 		    						if(tasss!=null) {
	 		    							if(tasss.getBalance6()!=null) {
	 		    								if(tasss.getBalance6()>=entity.getTransferAmount()) {
	 		    									tasss.setBalance6(tasss.getBalance6()-entity.getTransferAmount());
	 		    								}else {
	 		    									tasss.setBalance6(0D);
	 		    								}
	 		    								this.tradeAccountService.saveOrUpdate(tasss);
	 		    							}
	 		    						}
	 		    						
	 		    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
	 		    						 if(json_ta.getString("retcode").equals("0 Done"))  {
	 		    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
		    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
		    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
		    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
		    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
		    			                	 this.tradeAccountService.saveOrUpdate(tasss);
	 		    							 
	 		    						 }
	 		    					}
	 		    					
				            }else {
				            	entity.setOutStatus(2);
				            	entity.setTransferStatus(3);
								this.transferInfoService.saveOrUpdate(entity); 
				            }
		  			}
		    		
		  			}
		  		}catch(Exception e) {
		  			System.out.println(e);
		  		}
		    	}
    	}
    	
    }
    
    //查询转账入金   每1分钟查询一次，延迟7分钟
   // @Async
    @Scheduled(fixedRate=5000,initialDelay=428000)
    public void configureTasks5() {
    	if(bl7&&mainSwitch) {
       TransferInfo ti_query=new TransferInfo();
    	
    	ti_query.setAuditStatus(1);
    	ti_query.setTransferStatus(1);
    	ti_query.setOutStatus(1);
        Page<TransferInfo> ti_page=this.transferInfoService.findAll(0,1, "id", "asc", ti_query);
        
        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
        HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
      	boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
        
        if(ti_page.getContent().size()>0) {
			      
			  		try {
			  			if(blll) {
			 				for(int i=0;i<ti_page.getContent().size();i++) {
			 					TransferInfo entity  = ti_page.getContent().get(i);
			 					entity.setTransferStatus(99);
					    		this.transferInfoService.saveOrUpdate(entity);
					    		
					    		
					    		String curr1="USD";
					    		String curr2="USD";
					    		
					    		TradeAccount query_out=new TradeAccount();
					    		query_out.setTradeId(String.valueOf(entity.getTransferOut()));
 		    					Page<TradeAccount>  ta_page_out=this.tradeAccountService.findAll(0,1,"id","asc", query_out);
 		    					if(ta_page_out.getContent().size()>0) {
 		    						 TradeAccount ta=(TradeAccount)ta_page_out.getContent().get(0);
 		    						  UserGroup ug_query=new UserGroup();
 									  ug_query.setGroupName(ta.getGroupName());
 									  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
 									  if(page.getContent().size()>0) {
 										UserGroup ug_t=(UserGroup)page.getContent().get(0);
 										curr1=ug_t.getBackup1();
 									  }
 		    					}
 		    					
 		    					TradeAccount query_in=new TradeAccount();
 		    					query_in.setTradeId(String.valueOf(entity.getTransferIn()));
 		    					Page<TradeAccount>  ta_page_in=this.tradeAccountService.findAll(0,1,"id","asc", query_in);
 		    					if(ta_page_in.getContent().size()>0) {
 		    						TradeAccount ta=(TradeAccount)ta_page_in.getContent().get(0);
		    						  UserGroup ug_query=new UserGroup();
									  ug_query.setGroupName(ta.getGroupName());
									  Page<UserGroup> page=this.userGroupService.findAll(0,10,"id","asc", ug_query);
									  if(page.getContent().size()>0) {
										UserGroup ug_t=(UserGroup)page.getContent().get(0);
										curr2=ug_t.getBackup1();
									  }
 		    					}
 		    					
 		    					double din=0d;
 		    					if(!curr1.equals(curr2)) {
 		    						
 		    						if(curr1.equals("USD")&&curr2.equals("USC")) {
 		    							din=entity.getTransferAmount()*100;
 		    						}
 		    						
 		    						if(curr1.equals("USC")&&curr2.equals("USD")) {
 		    							din=entity.getTransferAmount()/100;
 		    						}
 		    					}else {
 		    						din=entity.getTransferAmount();
 		    					}
					    		
						 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(entity.getTransferIn()),"2",String.valueOf(din),"Transfer In");
						 			  if(json.getString("retcode").equals("0 Done"))  {
						 					entity.setInStatus(1);
							            	entity.setTransferStatus(2);
							            	entity.setInOrderId(json.getJSONObject("answer").getString("ticket"));
											this.transferInfoService.saveOrUpdate(entity); 
											
											TradeAccount query=new TradeAccount();
			 		    					query.setTradeId(String.valueOf(entity.getTransferIn()));
			 		    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
			 		    					if(ta_page.getContent().size()>0) {
			 		    						TradeAccount tasss=(TradeAccount)ta_page.getContent().get(0);
			 		    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
			 		    						 if(json_ta.getString("retcode").equals("0 Done"))  {
			 		    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
				    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
				    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
				    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
				    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
				    			                	 this.tradeAccountService.saveOrUpdate(tasss);
			 		    							 
			 		    						 }
			 		    					}
						 			  }else {
						 					entity.setInStatus(2);
							            	entity.setTransferStatus(3);
											this.transferInfoService.saveOrUpdate(entity); 
						 			  }
			 				}
					    	}
			  		}catch(Exception e) {
			  			System.out.println(e);
			  		}
			        }
    	}
    	
    }
    
    
    //补发佣金   每30秒查询一次，延迟16分钟
    //@Async
    @Scheduled(fixedRate=60000,initialDelay=8000)
    public void configureTasks7_2() {
    	
           
        ReckbackInfo rbif_query=new ReckbackInfo();
        rbif_query.setReckbackStatus(2);
        Page<ReckbackInfo> ti_page=this.reckbackInfoService.findAll(0,1, "id", "asc", rbif_query);
        
        if(ti_page.getContent().size()>0) {
	        ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
	        HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
			boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
	        
	  		try {
	  			if(blll) {
	 				for(int i=0;i<ti_page.getContent().size();i++) {
			    		ReckbackInfo entity  = ti_page.getContent().get(i);
			    		entity.setReckbackStatus(99);
			    		this.reckbackInfoService.saveOrUpdate(entity);
			    		

			    		
				 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(entity.getReckbackTradeId()),"2",String.valueOf(entity.getReckbackAmount()), "Co."+entity.getCrmTradeId()+"-"+entity.getCrmOrderId());
				 			  if(json.getString("retcode").equals("0 Done"))  {
				 				 entity.setReckbackStatus(1);
				 				 entity.setReckbackOrderId(json.getJSONObject("answer").getString("ticket"));
								 this.reckbackInfoService.saveOrUpdate(entity); 
				 			  }else {
				 				 entity.setReckbackStatus(2);
								 this.reckbackInfoService.saveOrUpdate(entity); 
				 			  }
			    		}
	 			 }
		    	
	  		}catch(Exception e) {
	  			System.out.println(e);
	  		}
    }
    	
    	
    }
    
    
}
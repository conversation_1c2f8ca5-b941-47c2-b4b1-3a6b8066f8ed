<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.cardType" placeholder="卡类型" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.cardName" placeholder="卡片名称" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.cardRemark" placeholder="卡片说明" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卡类型" prop="cardType" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.cardType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卡片名称" prop="cardName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.cardName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卡片说明" prop="cardRemark" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.cardRemark }}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换起始日期" prop="changeBegin"   sortable="custom" min-width="120px"  align="center" >
      				<template slot-scope="scope">
      					<span>{{ scope.row.changeBegin | parseTime('{y}-{m}-{d}') }}</span>
      				</template>
      </el-table-column>

      <el-table-column label="兑换结束日期" prop="changeEnd"   sortable="custom" min-width="120px"  align="center" >
      				<template slot-scope="scope">
      					<span>{{ scope.row.changeEnd | parseTime('{y}-{m}-{d}') }}</span>
      				</template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="320" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="卡类型" prop="cardType">
          <el-input v-model="temp.cardType" />
        </el-form-item>
        <el-form-item label="卡片名称" prop="cardName">
          <el-input v-model="temp.cardName" />
        </el-form-item>
        <el-form-item label="卡片说明" prop="cardRemark">
          <el-input v-model="temp.cardRemark" />
        </el-form-item>
        <el-form-item label="兑换起始日期" prop="changeBegin">
          <el-date-picker
            v-model="temp.changeBegin"
            type="date"
            placeholder="选择日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="兑换结束日期" prop="changeEnd">
          <el-date-picker
            v-model="temp.changeEnd"
            type="date"
            placeholder="选择日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="兑换说明" prop="changeRemark">
          <el-input v-model="temp.changeRemark" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item label="卡类型" prop="cardType">
          <el-input v-model="temp.cardType" />
        </el-form-item>
        <el-form-item label="卡片名称" prop="cardName">
          <el-input v-model="temp.cardName" />
        </el-form-item>
        <el-form-item label="卡片说明" prop="cardRemark">
          <el-input v-model="temp.cardRemark" />
        </el-form-item>
        <el-form-item label="兑换起始日期" prop="changeBegin">
          <el-date-picker
            v-model="temp.changeBegin"
            type="date"
            placeholder="选择日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="兑换结束日期" prop="changeEnd">
          <el-date-picker
            v-model="temp.changeEnd"
            type="date"
            placeholder="选择日期"
            style="width:160px;"
          />
        </el-form-item>
        <el-form-item label="兑换说明" prop="changeRemark">
          <el-input v-model="temp.changeRemark" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList , fetchCardType , createCardType , updateCardType, updateIsAvailable , removeCardType } from '@/api/cardType'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'CardTypeTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        cardType: undefined,
        cardName: undefined,
        cardRemark: undefined
      },
      temp: {
        id: undefined,
        cardType: '',
        cardName: '',
        cardRemark: '',
        changeBegin:'',
        changeEnd:'',
        changeRemark:'',
      },
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      rules: {
        cardType: [
          { required: true, message: '卡类型不能为空', trigger: 'change' },,
        ],
        cardName: [
          { required: true, message: '卡片名称不能为空', trigger: 'change' },,
        ],
        cardRemark: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        cardType: '',
        cardName: '',
        cardRemark: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      	this.temp.changeBegin = parseTime(this.temp.changeBegin,'{y}-{m}-{d} {h}:{i}:{s}')
        	this.temp.changeEnd = parseTime(this.temp.changeEnd,'{y}-{m}-{d} {h}:{i}:{s}')
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createCardType(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success'          type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.temp.changeBegin = parseTime(this.temp.changeBegin,'{y}-{m}-{d} {h}:{i}:{s}')
      	this.temp.changeEnd = parseTime(this.temp.changeEnd,'{y}-{m}-{d} {h}:{i}:{s}')
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          updateCardType(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success'          type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeCardType(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: '删除成功',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.service.ReckbackInfoService;


public class MyWebsocketClient4RekeBack extends WebSocketClient {
	private ReckbackInfoService reckbackInfoService;
	private ReckbackInfo reckbackInfo;
	

	public ReckbackInfoService getReckbackInfoService() {
		return reckbackInfoService;
	}

	public void setReckbackInfoService(ReckbackInfoService reckbackInfoService) {
		this.reckbackInfoService = reckbackInfoService;
	}

	public ReckbackInfo getReckbackInfo() {
		return reckbackInfo;
	}

	public void setReckbackInfo(ReckbackInfo reckbackInfo) {
		this.reckbackInfo = reckbackInfo;
	}

	public MyWebsocketClient4RekeBack(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			
			
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
			
			ReckbackInfo reckbackInfo_temp=this.reckbackInfoService.findById(new Long(jsStr.get("reqid").toString().split("___")[1]));
               if(jsStr.get("error").toString().equals("")) {
				
            	   reckbackInfo_temp.setReckbackStatus(1);
            	   reckbackInfo_temp.setReckbackOrderId(jsStr.get("orderid").toString());
				this.reckbackInfoService.saveOrUpdate(reckbackInfo_temp);
				
				}else {
					reckbackInfo_temp.setReckbackStatus(2);
					reckbackInfo_temp.setReckbackOrderId(jsStr.get("orderid").toString());
					this.reckbackInfoService.saveOrUpdate(reckbackInfo_temp);
					
						 // close();
				}
			
		}
		
		new Thread(){
    		public void run(){
    			try {
    			Thread.sleep(55000L);
    			 close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}

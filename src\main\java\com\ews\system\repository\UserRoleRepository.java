package com.ews.system.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import com.ews.system.entity.UserRole;

public interface UserRoleRepository extends  JpaRepository<UserRole, Long> {
	/**
	 * 通过userid查询userrole关联信息
	 * @param userId
	 * @return
	 */
	public List<UserRole> findByUserId(Long userId);
	
	/**
	 * 通过userid和roleid查询userorle
	 * @param userId
	 * @param roleId
	 * @return
	 */
	public UserRole findByUserIdAndRoleId(Long userId,Long roleId);
	
	/**
	 * 通过role删除信息
	 * @param roleId
	 */
	@Modifying
	@Transactional
	public void removeByRoleId(Long roleId);
}

package com.ews.common;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Random;



public class SmsSample {


	public static void sendCode(String phone,String message,HttpServletRequest request,String username,String password,String label) throws UnsupportedEncodingException {


		String str = message;
		String testUsername = username;
		String testPassword = password;
		String testPhone = phone;
		String testContent ="【"+label+"】您的验证码为000000000";
		testContent = testContent.replace("000000000",str);

		String httpUrl = "http://api.smsbao.com/sms";
		StringBuffer httpArg = new StringBuffer();
		httpArg.append("u=").append(testUsername).append("&");
		httpArg.append("p=").append(md5(testPassword)).append("&");
		httpArg.append("m=").append(testPhone).append("&");
		httpArg.append("c=").append(encodeUrlString(testContent, "UTF-8"));
		//httpArg.append("time=").append(new Date());
		String result = request(httpUrl,httpArg.toString());
		request.getSession().setAttribute("mobile", phone);
		request.getSession().setAttribute("smsCode", str);
	}

	
	private static String request(String httpUrl,String param) {
		// TODO Auto-generated method stub
		String fh="";
		try {
			fh=new HttpUtilsGBK().sendHttpsPostRequest(httpUrl, param);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return fh;
	}
	private static Object encodeUrlString(String testContent, String string) {
		// TODO Auto-generated method stub
		try {
			testContent = URLEncoder.encode(testContent,string);
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return testContent;
	}
	public static String getRandomMath(){
		StringBuilder code = new StringBuilder();
		Random random = new Random();
		for (int i = 0; i < 4; i++) {
			code.append(String.valueOf(random.nextInt(10)));
		}
		return code.toString();
	}
	public static boolean compareCode(String send_code_user, String send_code_right){
		if(send_code_right.equals(md5(send_code_user)))return true;
		return false;
	}
	/*public static String request(String httpUrl, String httpArg) {
		BufferedReader reader = null;
		String result = null;
		StringBuffer sbf = new StringBuffer();
		httpUrl = httpUrl + "?" + httpArg;

		try {
			URL url = new URL(httpUrl);
			HttpURLConnection connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("GET");
			connection.connect();
			InputStream is = connection.getInputStream();
			reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
			String strRead = reader.readLine();
			if (strRead != null) {
				sbf.append(strRead);
				while ((strRead = reader.readLine()) != null) {
					sbf.append("\n");
					sbf.append(strRead);
				}
			}
			reader.close();
			result = sbf.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}*/

	public static String md5(String plainText) {
		StringBuffer buf = null;
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();
			int i;
			buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return buf.toString();
	}
	public static String createNo() {
		Date date =new Date();
		StringBuilder code = new StringBuilder();
		Random random = new Random();

		for (int i = 0; i < 6; i++) {
			code.append(String.valueOf(random.nextInt(10)));
		}
		return date.getTime()+code.toString();
	}

	/*public static String encodeUrlString(String str, String charset) {
		String strret = null;
		if (str == null)
			return str;
		try {
			strret = java.net.URLEncoder.encode(str, charset);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return strret;
	}*/
}

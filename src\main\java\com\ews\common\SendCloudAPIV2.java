package com.ews.common;


import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

class A{
	String code;
	String accountname;
	String password;
	String address;
	String realName;
	String password2;
	public A(String code, String accountname, String password,String address,String realName,String password2) {
		super();
		this.code = code;
		this.accountname = accountname;
		this.password = password;
		this.address=address;
		this.realName = realName;
		this.password2=password2;
	}
	
}

public class SendCloudAPIV2 {

	public static String convert(List<A> dataList) throws JSONException {
		JSONObject ret = new JSONObject();
		JSONArray to = new JSONArray();
		JSONArray codes = new JSONArray();
		JSONArray accountnames = new JSONArray();
		JSONArray passwords = new JSONArray();
		JSONArray realName = new JSONArray();
		JSONArray password2 = new JSONArray();
		for (A a : dataList) {
			to.put(a.address);
			codes.put(a.code);
			accountnames.put(a.accountname);
			passwords.put(a.password);
			realName.put(a.realName);
			password2.put(a.password2);
		}
		JSONObject sub = new JSONObject();
		sub.put("%code%", codes);
		sub.put("%accountname%", accountnames);
		sub.put("%password%", passwords);
		sub.put("%realName%", realName);
		sub.put("%password2%", password2);
		ret.put("to", to);
		ret.put("sub", sub);
		return ret.toString();
	}
	public static void send_template(String api_user,String api_key,String subject,String code,String accountName,String password,String templateName,String from,String fromName,String to,String realName,String password2) throws ClientProtocolException, IOException, JSONException {

		final String url = "http://api.sendcloud.net/apiv2/mail/sendtemplate";

		final String apiUser = api_user;
		final String apiKey = api_key;
		List<A> dataList = new ArrayList<A>();
		dataList.add(new A( code,accountName, password,to, realName, password2));
		final String xsmtpapi = convert(dataList);
		HttpClient httpClient = new DefaultHttpClient();
		HttpPost httpPost = new HttpPost(url);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		params.add(new BasicNameValuePair("apiUser", apiUser));
		params.add(new BasicNameValuePair("apiKey", apiKey));
		params.add(new BasicNameValuePair("xsmtpapi", xsmtpapi));
		params.add(new BasicNameValuePair("templateInvokeName", templateName));//模板
		params.add(new BasicNameValuePair("from", from));
		params.add(new BasicNameValuePair("fromName", fromName));
		params.add(new BasicNameValuePair("subject", subject));
		httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
		HttpResponse response = httpClient.execute(httpPost);
		if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) { 
			System.out.println(EntityUtils.toString(response.getEntity()));
		} else {
			System.err.println("error");
		}
		httpPost.releaseConnection();
	}
	
	
	public static void main(String[] args) throws Exception {
	//send_template("winhigh.vip","JVnFcgPqs5FIKWt6","WINHIGH验证码","5566","","","sendValidateCode","<EMAIL>","sys","<EMAIL>");
	//send_template("winhigh.vip","JVnFcgPqs5FIKWt6","WINHIGHT开户通知","","10086","123456","openAccount","<EMAIL>","sys","<EMAIL>");

	}
}

package com.ews.system.entity;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

@Entity
@Table(name = "sys_user")
public class User implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	*
	**/
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "user_id")
	protected Long userId;
	/**
	*
	**/
	@Column(name = "username")
	protected String username;
	/**
	*
	**/
	@Column(name = "password")
	protected String password;
	/**
	*
	**/
	@Column(name = "user_create")
	protected Long userCreate;
	/**
	*
	**/
	@Column(name = "gmt_create")
	protected Date gmtCreate;
	/**
	*
	**/
	@Column(name = "gmt_modified")
	protected Date gmtModified;
	/**
	 * 是否删除1是0否
	 **/
	@Column(name = "is_deleted")
	protected Integer isDeleted;
	/**
	 * 1正常，0禁用
	 **/
	@Column(name = "is_available")
	protected Integer isAvailable;
	/**
	 * 1系统管理员，2未定义
	 **/
	@Column(name = "user_type")
	protected Integer userType;
	/**
	 * 昵称
	 **/
	@Column(name = "nick_name")
	protected String nickName;
	/**
	 * 手机号
	 **/
	@Column(name = "mobile")
	protected String mobile;
	/**
	 * 电子邮件
	 **/
	@Column(name = "email")
	protected String email;

	/**
	 * 加密密码的盐
	 */
	@Column(name = "salt")
	protected String salt;

	/**
	 * 用户头像
	 */
	@Column(name = "user_icon")
	protected String userIcon;
	
	/*
	 * 出生日期
	 */
	@Column(name="birthday")
	protected Date birthday;
	/**
	 * 交易账号  
	 */
	@Column(name="store_id")
	protected Long storeId;
	/**
	 * CRMID  
	 */
	@Column(name="department_id")
	protected Long departmentId;
	
	/**
	 * gmtCreateBegin 的列表查询条件
	 **/
	@Transient
	protected String gmtCreateBegin;

	/**
	 * gmtCreateEnd 的列表查询条件
	 **/
	@Transient
	protected String gmtCreateEnd;
	
	/**
	 * 角色类型 0 管理员 1 总监 2 经理 3 销售 6 一级代理 7 二级代理 8 三级代理
	 */
	@Column(name = "role_type")
	protected Integer roleType;
	
	
	/**
	 * 上级ID
	 */
	@Column(name = "re_id")
	protected Long reId;
	
	
	/**
	 * 邀请码
	 */
	@Column(name = "sale_serial")
	protected String saleSerial;
	
	
	/**
	 * 本级排序字段
	 */
	@Column(name = "sort_str")
	protected String sortStr;
	
	/**
	 * 全部排序字段
	 */
	@Column(name = "order_str")
	protected String orderStr;
	
	
	@Transient
	protected  List ruleList;
	
	
	/**
	 * 代理数
	 */
	@Transient
	protected  Integer agentQty;
	
	/**
	 * CRM用户数
	 */
	@Transient
	protected  Integer userInfoQty;
	/**
	 * 交易用户数
	 */
	@Transient
	protected  Integer tradeAccountQty;
	
	/**
	 * 交易账号余额
	 */
	@Transient
	protected  Double tradeAccountAmount;
	
	
	
	

	public Integer getAgentQty() {
		return agentQty;
	}

	public void setAgentQty(Integer agentQty) {
		this.agentQty = agentQty;
	}

	public Integer getUserInfoQty() {
		return userInfoQty;
	}

	public void setUserInfoQty(Integer userInfoQty) {
		this.userInfoQty = userInfoQty;
	}

	public Integer getTradeAccountQty() {
		return tradeAccountQty;
	}

	public void setTradeAccountQty(Integer tradeAccountQty) {
		this.tradeAccountQty = tradeAccountQty;
	}

	public Double getTradeAccountAmount() {
		return tradeAccountAmount;
	}

	public void setTradeAccountAmount(Double tradeAccountAmount) {
		this.tradeAccountAmount = tradeAccountAmount;
	}

	public List getRuleList() {
		return ruleList;
	}

	public void setRuleList(List ruleList) {
		this.ruleList = ruleList;
	}

	public String getSortStr() {
		return sortStr;
	}

	public void setSortStr(String sortStr) {
		this.sortStr = sortStr;
	}

	public String getOrderStr() {
		return orderStr;
	}

	public void setOrderStr(String orderStr) {
		this.orderStr = orderStr;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public Long getUserCreate() {
		return userCreate;
	}

	public void setUserCreate(Long userCreate) {
		this.userCreate = userCreate;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Integer getIsAvailable() {
		return isAvailable;
	}

	public void setIsAvailable(Integer isAvailable) {
		this.isAvailable = isAvailable;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getNickName() {
		return nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSalt() {
		return salt;
	}

	public void setSalt(String salt) {
		this.salt = salt;
	}

	public String getGmtCreateBegin() {
		return gmtCreateBegin;
	}

	public void setGmtCreateBegin(String gmtCreateBegin) {
		this.gmtCreateBegin = gmtCreateBegin;
	}

	public String getGmtCreateEnd() {
		return gmtCreateEnd;
	}

	public void setGmtCreateEnd(String gmtCreateEnd) {
		this.gmtCreateEnd = gmtCreateEnd;
	}

	
	
	public String getUserIcon() {
		return userIcon;
	}

	public void setUserIcon(String userIcon) {
		this.userIcon = userIcon;
	}

	
	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Long getStoreId() {
		return storeId;
	}

	public void setStoreId(Long storeId) {
		this.storeId = storeId;
	}

	public Long getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Long departmentId) {
		this.departmentId = departmentId;
	}
	
	

	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public Long getReId() {
		return reId;
	}

	public void setReId(Long reId) {
		this.reId = reId;
	}
	
	

	public String getSaleSerial() {
		return saleSerial;
	}

	public void setSaleSerial(String saleSerial) {
		this.saleSerial = saleSerial;
	}

	/**
	 * 密码盐.
	 * 
	 * @return
	 * @throws ParseException
	 */
	public String getCredentialsSalt() throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		Date date = sdf.parse(sdf.format(this.getGmtCreate()));
		return this.username + this.salt + date.getTime();// 重新定义盐，防止被破解
	}

}

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.fullname" :placeholder="$t('userInfoAudit.label1')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tel" :placeholder="$t('userInfoAudit.label3')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfoAudit.label2')" prop="fullname" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}{{ scope.row.surname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfoAudit.label1')" prop="userName" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfoAudit.label3')" prop="tel" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tel }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfoAudit.label4')" prop="province" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.province }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfoAudit.label17')" prop="gmtModified" min-width="180px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}/{m}/{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.actions')" align="center" width="220" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">{{ $t('userInfoAudit.label18') }}</el-button>
          <el-button size="small" type="danger" @click="showbhyy(scope.row)">{{ $t('userInfoAudit.label16') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <el-dialog title="UserInfo" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('userInfoAudit.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label2')" prop="fullname">
          {{ temp.name }}	{{ temp.surname }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          {{ temp.birthday | parseTime('{y}/{m}/{d}') }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label3')" prop="tel">
          {{ temp.tel }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label4')" prop="tel">
          {{ temp.province }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label5')" prop="country">
        {{ temp.country }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          {{ temp.identityNum }}
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankName')" prop="city">
          {{ temp.city }}
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankAccount')" prop="address">
          {{ temp.adress }}
        </el-form-item>

        <el-form-item :label="$t('userInfoAudit.label7')" prop="imageFront">
          <el-image v-if="temp.imageFront" :src="temp.imageFront" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.imageBack" :src="temp.imageBack" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.backup3" :src="temp.backup3" style="height:150px;" :preview-src-list="srcList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" :disabled="isDisabled" @click="updateData()">{{ $t('userInfoAudit.label15') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="$t('userInfoAudit.label13')" :visible.sync="dialogFormAuditVisible" :close-on-click-modal="false">
      <el-form ref="dataBhForm" :model="bhTemp" label-position="right" label-width="100px" style="max-width: 700px; margin-left:10px;">
        <el-form-item :label="$t('userInfoAudit.label14')" prop="bhyy">
          <el-input v-model="bhTemp.bhyy" style="width:350px;" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAuditVisible = false">{{ $t('userInfoAudit.label19') }}</el-button>
        <el-button type="primary" @click="handleDelete()">{{ $t('userInfoAudit.label16') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchList2, updateUserInfo_audit, fetchUserInfo, createUserInfo, updateUserInfo, updateIsAvailable, bohuiUserInfo } from '@/api/userInfo'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
export default {
  name: 'UserInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        fullname: undefined,
        userName: undefined,
        email: undefined,
        tel: undefined,
        invitationCode: undefined,
        userType: undefined,
        parentId: undefined,
        pinyin: undefined,
        isAgent: undefined
      }, bhTemp: {
        id: undefined,
        bhyy: ''
      },
      temp: {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        imageBack: '',
        invitationCode: '',
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: ''
      },
      isDisabled: false,
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      dialogFormAuditVisible: false,
      srcList: [

      ],
      rules: {
        surname: [,
        ],
        name: [,
        ],
        fullname: [
        ],
        userName: [,
        ],
        email: [,
        ],
        tel: [,
        ],
        password: [
        ],
        country: [,
        ],
        province: [
        ],
        city: [
        ],
        gender: [
        ],
        adress: [
        ],
        nickname: [
        ],
        birthday: [
        ],
        identityNum: [
        ],
        imageFront: [
        ],
        imageBack: [
        ],
        invitationCode: [
        ],
        amountMain: [
        ],
        amountSub: [
        ],
        userType: [
        ],
        parentId: [
        ],
        pinyin: [
        ],
        remark: [
        ],
        rakeBackType: [
        ],
        rakeBackAmount: [
        ],
        isAgent: [
        ],
        auditId: [
        ],
        auditTime: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList2(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        imageBack: '',
        invitationCode: '',
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.isDisabled = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.birthday = parseTime(this.temp.birthday, '{y}-{m}-{d} {h}:{i}:{s}')
          createUserInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
              this.isDisabled = false
            } else {
              this.$message.error(response.msg)
              this.isDisabled = false
            }
          })
        }
      })
				 },
    updateData() {
      this.$confirm('Are you sure you want to approve this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await updateUserInfo_audit(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'audit success',
                type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })

      /*
					this.$refs['dataEditForm'].validate((valid) => {
						if (valid) {
							updateUserInfo_audit(this.temp).then(result => {
								if(result.code==20000){
									this.$notify({
										title: 'success',
										message: 'oper success'			,	type: 'success',
										duration: 2000
									})
									this.getList()
									this.dialogFormEditVisible = false
								}else{
									this.$message.error(response.msg);
								}
							})
						}
					})*/
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      const birthdayDate = new Date(row.birthday)
      this.temp.birthday = birthdayDate.getFullYear() + '-' + 
        String(birthdayDate.getMonth() + 1).padStart(2, '0') + '-' + 
        String(birthdayDate.getDate()).padStart(2, '0')
        
      this.srcList = [this.temp.imageFront, this.temp.imageBack]
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    }, showbhyy(row) {
      this.bhTemp.id = row.id
      this.bhTemp.bhyy = ''
      this.dialogFormAuditVisible = true
    },
				 handleDelete() {
      this.$confirm('Are you sure you want to reject and delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await bohuiUserInfo(this.bhTemp.id, this.bhTemp.bhyy).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'Rejected successfully',
                type: 'success',
                duration: 2000
              })
              this.dialogFormAuditVisible = false
              this.getList()
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

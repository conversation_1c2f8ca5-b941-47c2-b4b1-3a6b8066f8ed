package com.ews.config.result;

public class ResponseDataUtil {
	/**
	 * 带实体的统一返回
	 *
	 * @param data
	 *            实体
	 * @param <T>
	 *            实体类型
	 * @return
	 */
	/**
	 * 返回编码及信息，并附带数据
	 * @param data
	 * @return
	 */
	public static <T> ResponseData buildSuccess(T data) {
		return new ResponseData<T>(ResultEnums.SUCCESS, data);
	}

	/**
	 * 返回成功状态及默认提示信息
	 * @return
	 */
	public static ResponseData buildSuccess() {
		return new ResponseData(ResultEnums.SUCCESS);
	}

	/**
	 * 返回成功编码和自定义提示信息
	 * @param msg
	 * @return
	 */
	public static ResponseData buildSuccess(String msg) {
		return new ResponseData(ResultEnums.SUCCESS.getCode(), msg);
	}

	/**
	 * 返回自定义的编码及提示信息
	 * @param code
	 * @param msg
	 * @return
	 */
	public static ResponseData buildSuccess(String code, String msg) {
		return new ResponseData(code, msg);
	}

	/**
	 * 返回自定义的编码、提示信息和数据
	 * @param code
	 * @param msg
	 * @param data
	 * @return
	 */
	public static <T> ResponseData buildSuccess(String code, String msg, T data) {
		return new ResponseData<T>(code, msg, data);
	}
	public static ResponseData buildSuccess(ResultEnums resultEnums) {
		return new ResponseData(resultEnums);
	}

	public static <T> ResponseData buildError(T data) {
		return new ResponseData<T>(ResultEnums.ERROR, data);
	}

	public static ResponseData buildError() {
		return new ResponseData(ResultEnums.ERROR);
	}
	
	public static ResponseData buildLoginError() {
		return new ResponseData(ResultEnums.LOGIN_ERROR);
	}

	public static ResponseData buildError(String msg) {
		return new ResponseData(ResultEnums.ERROR.getCode(), msg);
	}

	public static ResponseData buildError(String code, String msg) {
		return new ResponseData(code, msg);
	}

	public static <T> ResponseData buildError(String code, String msg, T data) {
		return new ResponseData<T>(code, msg, data);
	}

	public static ResponseData buildError(ResultEnums resultEnums) {
		return new ResponseData(resultEnums);
	}
}

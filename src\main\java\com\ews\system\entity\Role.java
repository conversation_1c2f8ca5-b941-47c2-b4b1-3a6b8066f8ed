package com.ews.system.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.data.annotation.Transient;

import com.ews.system.model.IRolePermission;

@Entity
@Table(name = "sys_role")
public class Role implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *角色id
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "role_id")
	private  Long roleId;
    /**
    *角色名
    **/
	@Column(name = "role_name")
	private  String roleName;
    /**
    *角色标识,程序中判断使用,如"admin"
    **/
	@Column(name = "role_sign")
	private  String roleSign;
    /**
    *角色描述,UI界面显示使用
    **/
	@Column(name = "description")
	private  String description;
    /**
    *1正常，0禁用
    **/
	@Column(name = "is_available")
	private  Integer isAvailable;
    /**
    *创建时间
    **/
	@Column(name = "gmt_create")
	private  Date gmtCreate;
    /**
    *修改时间
    **/
	@Column(name = "gmt_modified")
	private  Date gmtModified;
	
	
	@Column(name="user_create")
	private  Long userCreate;
	
	@Column(name="is_deleted")
	private  Integer isDeleted;
	

	 
    public Long  getRoleId()
    {
        return roleId;
    }
    public void setRoleId(Long  roleId)
    {
        this.roleId = roleId;
    }
    public String  getRoleName()
    {
        return roleName;
    }
    public void setRoleName(String  roleName)
    {
        this.roleName = roleName;
    }
    public String  getRoleSign()
    {
        return roleSign;
    }
    public void setRoleSign(String  roleSign)
    {
        this.roleSign = roleSign;
    }
    public String  getDescription()
    {
        return description;
    }
    public void setDescription(String  description)
    {
        this.description = description;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
	public Long getUserCreate() {
		return userCreate;
	}
	public void setUserCreate(Long userCreate) {
		this.userCreate = userCreate;
	}
	public Integer getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}
    
    
}

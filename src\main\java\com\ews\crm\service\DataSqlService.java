package com.ews.crm.service;

import java.util.List;

import org.springframework.stereotype.Component;


@Component
public interface DataSqlService
{
	/**
	 * 根据代理ID、用户组、交易产品查询返佣金额
	 */
	public List queryRekeBackAmount(Long userID,String groupName,String prodName);	
	
	
	
	
	public List getMyTradeObj(Long user_id,String tradeID);
	
	
	public List getProfitList(Long user_id,String tradeID);
	
	
	public List getImportCrmUserList();
	
	public List getTradeAndCrmUserBindInfoList();

	public List getAgentAndCrmUserBindInfoList();
	
	
	public Double getFundTotal(Long crm_id,int type);
}

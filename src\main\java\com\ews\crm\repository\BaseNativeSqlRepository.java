package com.ews.crm.repository;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.PersistenceUnit;
import javax.persistence.Query;

public class BaseNativeSqlRepository {
	    @PersistenceUnit
	    private EntityManagerFactory emf;
	    
	    public List nativeSqlList(String sql){
	        EntityManager em = emf.createEntityManager();
	        Query query = em.createNativeQuery(sql);
	        List list = query.getResultList();
	        em.close();
	        return list;
	    }
	    
	  
}

package com.ews.system.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "sys_user_role")
public class UserRole implements Serializable
{
	private static final long serialVersionUID = 1L;
    /**
    *表id
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.AUTO)
	@Column(name = "user_role_id")
	private  Long userRoleId;
    /**
    *用户id
    **/
	@Column(name = "user_id")
	private  Long userId;
    /**
    *角色id
    **/
	@Column(name = "role_id")
	private  Long roleId;

    public Long  getUserRoleId()
    {
        return userRoleId;
    }
    public void setUserRoleId(Long  userRoleId)
    {
        this.userRoleId = userRoleId;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public Long  getRoleId()
    {
        return roleId;
    }
    public void setRoleId(Long  roleId)
    {
        this.roleId = roleId;
    }

}

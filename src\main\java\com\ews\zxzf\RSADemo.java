package com.ews.zxzf;

import java.net.URLEncoder;

public class RSADemo {

    public static void main(String[] args) throws Exception {
        String filepath = "C:\\work\\";

        System.out.println("---------------私钥签名过程------------------");
        String content = "merchantNumber=RHFS1009&orderNumber=20200207233655782&transMoney=100&transType=1";
        String signstr = RSAUtil.sign(content, RSAUtil.loadPrivateKeyByFile(filepath));
        System.out.println("签名原串：" + content);
        System.out.println("签名串：" + signstr);
        System.out.println();

        System.out.println("---------------公钥校验签名------------------");
        System.out.println("签名原串：" + content);
        System.out.println("签名串：" + signstr);
        System.out.println("验签结果：" + RSAUtil.doCheck(content, signstr, RSAUtil.loadPublicKeyByFile(filepath)));

        //使用签名信息发送请求
        String str = OKHttpUtils.newInstance().getSyncData("http://api.zhixiangpay.com/api/trade/apply?"+content+"&sign="+URLEncoder.encode(signstr));
        System.out.println(str);
         
        

    }
}
package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "withdrawal_setting")
public class WithdrawalSetting implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *最低出金
    **/
	@Column(name = "withdrawal_min")
	protected  Double withdrawalMin;

    /**
    *最高出金
    **/
	@Column(name = "withdrawal_max")
	protected  Double withdrawalMax;

    /**
    *手续费模式
    **/
	@Column(name = "fee_type")
	protected  Integer feeType;

    /**
    *手续费比例
    **/
	@Column(name = "fee_proportion")
	protected  Double feeProportion;

    /**
    *最低手续费
    **/
	@Column(name = "fee_min")
	protected  Double feeMin;

    /**
    *最高手续费
    **/
	@Column(name = "fee_max")
	protected  Double feeMax;

    /**
    *固定金额
    **/
	@Column(name = "fee_amount")
	protected  Double feeAmount;

    /**
    *通知形式
    **/
	@Column(name = "notice_type")
	protected  Integer noticeType;

    /**
    *通知邮箱
    **/
	@Column(name = "notice_email")
	protected  String noticeEmail;

    /**
    *通知手机号
    **/
	@Column(name = "notice_mobile")
	protected  String noticeMobile;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  String backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  String backup2;

    /**
    *backup3
    **/
	@Column(name = "backup3")
	protected  String backup3;

    /**
    *backup4
    **/
	@Column(name = "backup4")
	protected  String backup4;

	@Transient
	protected  Integer sortNum;

    public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Double  getWithdrawalMin()
    {
        return withdrawalMin;
    }
    public void setWithdrawalMin(Double  withdrawalMin)
    {
        this.withdrawalMin = withdrawalMin;
    }
    public Double  getWithdrawalMax()
    {
        return withdrawalMax;
    }
    public void setWithdrawalMax(Double  withdrawalMax)
    {
        this.withdrawalMax = withdrawalMax;
    }
    public Integer  getFeeType()
    {
        return feeType;
    }
    public void setFeeType(Integer  feeType)
    {
        this.feeType = feeType;
    }
    public Double  getFeeProportion()
    {
        return feeProportion;
    }
    public void setFeeProportion(Double  feeProportion)
    {
        this.feeProportion = feeProportion;
    }
    public Double  getFeeMin()
    {
        return feeMin;
    }
    public void setFeeMin(Double  feeMin)
    {
        this.feeMin = feeMin;
    }
    public Double  getFeeMax()
    {
        return feeMax;
    }
    public void setFeeMax(Double  feeMax)
    {
        this.feeMax = feeMax;
    }
    public Double  getFeeAmount()
    {
        return feeAmount;
    }
    public void setFeeAmount(Double  feeAmount)
    {
        this.feeAmount = feeAmount;
    }
    public Integer  getNoticeType()
    {
        return noticeType;
    }
    public void setNoticeType(Integer  noticeType)
    {
        this.noticeType = noticeType;
    }
    public String  getNoticeEmail()
    {
        return noticeEmail;
    }
    public void setNoticeEmail(String  noticeEmail)
    {
        this.noticeEmail = noticeEmail;
    }
    public String  getNoticeMobile()
    {
        return noticeMobile;
    }
    public void setNoticeMobile(String  noticeMobile)
    {
        this.noticeMobile = noticeMobile;
    }
    public String  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(String  backup1)
    {
        this.backup1 = backup1;
    }
    public String  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(String  backup2)
    {
        this.backup2 = backup2;
    }
    public String  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(String  backup3)
    {
        this.backup3 = backup3;
    }
    public String  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(String  backup4)
    {
        this.backup4 = backup4;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

package com.ews.crm.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
@Table(name = "fund_info")
public class FundInfo implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *主键
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	protected  Long id;

    /**
    *创建人
    **/
	@Column(name = "user_create")
	protected  Long userCreate;

    /**
    *创建时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtCreate;

    /**
    *修改时间
    **/
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
	protected  Date gmtModified;

    /**
    *是否删除 1是 0否
    **/
	@Column(name = "is_deleted")
	protected  Integer isDeleted;

    /**
    *是否可用 1是 0否
    **/
	@Column(name = "is_available")
	protected  Integer isAvailable;

    /**
    *CRM用户ID
    **/
	@Column(name = "user_id")
	protected  Long userId;

    /**
    *CRM用户名称
    **/
	@Column(name = "user_name")
	protected  String userName;

    /**
    *CRM账号
    **/
	@Column(name = "crm_account")
	protected  String crmAccount;

    /**
    *交易账号
    **/
	@Column(name = "trade_id")
	protected  String tradeId;

    /**
    *金额
    **/
	@Column(name = "amount")
	protected  Double amount;

    /**
    *入金银行ID
    **/
	@Column(name = "deposit_bank_id")
	protected  Long depositBankId;

    /**
    *出金银行ID
    **/
	@Column(name = "withdraw_bank_id")
	protected  Long withdrawBankId;

    /**
    *类型  1 入金  2 出金  3其他
    **/
	@Column(name = "type")
	protected  Integer type;

    /**
    *配资
    **/
	@Column(name = "allocation_amount")
	protected  Double allocationAmount;

    /**
    *资金  状态  0 待审核    1  审核通过   2 拒绝
    **/
	@Column(name = "audit_status")
	protected  Integer auditStatus;

    /**
    *处理结果  0 待处理    1 已处理    2 异常
    **/
	@Column(name = "oper_status")
	protected  Integer operStatus;

    /**
    *审核人
    **/
	@Column(name = "audit_id")
	protected  Long auditId;

    /**
    *订单号
    **/
	@Column(name = "order_id")
	protected  String orderId;

    /**
    *开户行
    **/
	@Column(name = "bank_name")
	protected  String bankName;

    /**
    *银行卡号
    **/
	@Column(name = "bank_num")
	protected  String bankNum;

    /**
    *开户姓名
    **/
	@Column(name = "account_name")
	protected  String accountName;

    /**
    *预留手机号
    **/
	@Column(name = "mobile")
	protected  String mobile;

    /**
    *银行地址-- 改成OTC支付信息（订单号）
    **/
	@Column(name = "bank_address")
	protected  String bankAddress;

    /**
    *备注
    **/
	@Column(name = "remark")
	protected  String remark;

    /**
    *附件
    **/
	@Column(name = "annex")
	protected  String annex;

    /**
    *手续费
    **/
	@Column(name = "fee")
	protected  Double fee;

    /**
    *实际金额
    **/
	@Column(name = "actual_amount")
	protected  Double actualAmount;

    /**
    *原金额
    **/
	@Column(name = "original_amount")
	protected  Double originalAmount;

    /**
    *变更后金额
    **/
	@Column(name = "changed_amount")
	protected  Double changedAmount;

    /**
    *backup1
    **/
	@Column(name = "backup1")
	protected  Double backup1;

    /**
    *backup2
    **/
	@Column(name = "backup2")
	protected  Double backup2;

    /**
    *backup3  --支付方式     NULL &  2  线下支付      1 三方支付  
    **/
	@Column(name = "backup3")
	protected  Integer backup3;

    /**
    *backup4   --是否支付成功   2待支付    1  成功    0 失败
    **/
	@Column(name = "backup4")
	protected  Integer backup4;

    /**
    *backup5  --审核备注信息
    **/
	@Column(name = "backup5")
	protected  String backup5;

    /**
    *backup6  --临时存放入金银行信息
    **/
	@Column(name = "backup6")
	protected  String backup6;

    /**
    *货币类型
    **/
	@Column(name = "currency_type")
	protected  Integer currencyType;

    /**
    *汇率
    **/
	@Column(name = "rate")
	protected  Double rate;

    /**
    *人民币
    **/
	@Column(name = "rate_rmb")
	protected  Double rateRmb;

	@Transient
	protected  Integer sortNum;
	
	@Transient
	protected  Double accountAmount;
	
	@Transient
	protected  List bankList;
	
	
	
	@Transient
	protected  List userInfoList;
	
	
	@Transient
	protected  String groupInfo;
	
	
	

    public String getGroupInfo() {
		return groupInfo;
	}
	public void setGroupInfo(String groupInfo) {
		this.groupInfo = groupInfo;
	}
	public List getUserInfoList() {
		return userInfoList;
	}
	public void setUserInfoList(List userInfoList) {
		this.userInfoList = userInfoList;
	}
	public Double getAccountAmount() {
		return accountAmount;
	}
	public void setAccountAmount(Double accountAmount) {
		this.accountAmount = accountAmount;
	}
	public List getBankList() {
		return bankList;
	}
	public void setBankList(List bankList) {
		this.bankList = bankList;
	}
	public Long  getId()
    {
        return id;
    }
    public void setId(Long  id)
    {
        this.id = id;
    }
    public Long  getUserCreate()
    {
        return userCreate;
    }
    public void setUserCreate(Long  userCreate)
    {
        this.userCreate = userCreate;
    }
    public Date  getGmtCreate()
    {
        return gmtCreate;
    }
    public void setGmtCreate(Date  gmtCreate)
    {
        this.gmtCreate = gmtCreate;
    }
    public Date  getGmtModified()
    {
        return gmtModified;
    }
    public void setGmtModified(Date  gmtModified)
    {
        this.gmtModified = gmtModified;
    }
    public Integer  getIsDeleted()
    {
        return isDeleted;
    }
    public void setIsDeleted(Integer  isDeleted)
    {
        this.isDeleted = isDeleted;
    }
    public Integer  getIsAvailable()
    {
        return isAvailable;
    }
    public void setIsAvailable(Integer  isAvailable)
    {
        this.isAvailable = isAvailable;
    }
    public Long  getUserId()
    {
        return userId;
    }
    public void setUserId(Long  userId)
    {
        this.userId = userId;
    }
    public String  getUserName()
    {
        return userName;
    }
    public void setUserName(String  userName)
    {
        this.userName = userName;
    }
    public String  getCrmAccount()
    {
        return crmAccount;
    }
    public void setCrmAccount(String  crmAccount)
    {
        this.crmAccount = crmAccount;
    }
    public String  getTradeId()
    {
        return tradeId;
    }
    public void setTradeId(String  tradeId)
    {
        this.tradeId = tradeId;
    }
    public Double  getAmount()
    {
        return amount;
    }
    public void setAmount(Double  amount)
    {
        this.amount = amount;
    }
    public Long  getDepositBankId()
    {
        return depositBankId;
    }
    public void setDepositBankId(Long  depositBankId)
    {
        this.depositBankId = depositBankId;
    }
    public Long  getWithdrawBankId()
    {
        return withdrawBankId;
    }
    public void setWithdrawBankId(Long  withdrawBankId)
    {
        this.withdrawBankId = withdrawBankId;
    }
    public Integer  getType()
    {
        return type;
    }
    public void setType(Integer  type)
    {
        this.type = type;
    }
    public Double  getAllocationAmount()
    {
        return allocationAmount;
    }
    public void setAllocationAmount(Double  allocationAmount)
    {
        this.allocationAmount = allocationAmount;
    }
    public Integer  getAuditStatus()
    {
        return auditStatus;
    }
    public void setAuditStatus(Integer  auditStatus)
    {
        this.auditStatus = auditStatus;
    }
    public Integer  getOperStatus()
    {
        return operStatus;
    }
    public void setOperStatus(Integer  operStatus)
    {
        this.operStatus = operStatus;
    }
    public Long  getAuditId()
    {
        return auditId;
    }
    public void setAuditId(Long  auditId)
    {
        this.auditId = auditId;
    }
    public String  getOrderId()
    {
        return orderId;
    }
    public void setOrderId(String  orderId)
    {
        this.orderId = orderId;
    }
    public String  getBankName()
    {
        return bankName;
    }
    public void setBankName(String  bankName)
    {
        this.bankName = bankName;
    }
    public String  getBankNum()
    {
        return bankNum;
    }
    public void setBankNum(String  bankNum)
    {
        this.bankNum = bankNum;
    }
    public String  getAccountName()
    {
        return accountName;
    }
    public void setAccountName(String  accountName)
    {
        this.accountName = accountName;
    }
    public String  getMobile()
    {
        return mobile;
    }
    public void setMobile(String  mobile)
    {
        this.mobile = mobile;
    }
    public String  getBankAddress()
    {
        return bankAddress;
    }
    public void setBankAddress(String  bankAddress)
    {
        this.bankAddress = bankAddress;
    }
    public String  getRemark()
    {
        return remark;
    }
    public void setRemark(String  remark)
    {
        this.remark = remark;
    }
    public String  getAnnex()
    {
        return annex;
    }
    public void setAnnex(String  annex)
    {
        this.annex = annex;
    }
    public Double  getFee()
    {
        return fee;
    }
    public void setFee(Double  fee)
    {
        this.fee = fee;
    }
    public Double  getActualAmount()
    {
        return actualAmount;
    }
    public void setActualAmount(Double  actualAmount)
    {
        this.actualAmount = actualAmount;
    }
    public Double  getOriginalAmount()
    {
        return originalAmount;
    }
    public void setOriginalAmount(Double  originalAmount)
    {
        this.originalAmount = originalAmount;
    }
    public Double  getChangedAmount()
    {
        return changedAmount;
    }
    public void setChangedAmount(Double  changedAmount)
    {
        this.changedAmount = changedAmount;
    }
    public Double  getBackup1()
    {
        return backup1;
    }
    public void setBackup1(Double  backup1)
    {
        this.backup1 = backup1;
    }
    public Double  getBackup2()
    {
        return backup2;
    }
    public void setBackup2(Double  backup2)
    {
        this.backup2 = backup2;
    }
    public Integer  getBackup3()
    {
        return backup3;
    }
    public void setBackup3(Integer  backup3)
    {
        this.backup3 = backup3;
    }
    public Integer  getBackup4()
    {
        return backup4;
    }
    public void setBackup4(Integer  backup4)
    {
        this.backup4 = backup4;
    }
    public String  getBackup5()
    {
        return backup5;
    }
    public void setBackup5(String  backup5)
    {
        this.backup5 = backup5;
    }
    public String  getBackup6()
    {
        return backup6;
    }
    public void setBackup6(String  backup6)
    {
        this.backup6 = backup6;
    }
    public Integer  getCurrencyType()
    {
        return currencyType;
    }
    public void setCurrencyType(Integer  currencyType)
    {
        this.currencyType = currencyType;
    }
    public Double  getRate()
    {
        return rate;
    }
    public void setRate(Double  rate)
    {
        this.rate = rate;
    }
    public Double  getRateRmb()
    {
        return rateRmb;
    }
    public void setRateRmb(Double  rateRmb)
    {
        this.rateRmb = rateRmb;
    }
    public Integer getSortNum() {
    	return sortNum;
    }
    public void setSortNum(Integer sortNum) {
    	this.sortNum = sortNum;
    }

}

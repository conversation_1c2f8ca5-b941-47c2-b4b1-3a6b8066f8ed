import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/user/info',
    method: 'get' })
}

export function fetchSaleMgn() {
  return request({
    url: '/user/getSaleMgn',
    method: 'get' })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

// 查询用户列表
export function fetchList(query) {
  return request({
    url: '/user/list',
    method: 'post',
    params: query
  })
}

// 查询用户列表
export function fetchList2(query) {
  return request({
    url: '/user/list2',
    method: 'post',
    params: query
  })
}
// 查询用户列表
export function fetchList3(query) {
  return request({
    url: '/user/list3',
    method: 'post',
    params: query
  })
}

// 查看用户详情
export function fetchUser(id) {
  return request({
    url: '/user/detail',
    method: 'get',
    params: { id }
  })
}

// 新建用户
export function createUser(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data
  })
}
// 更新用户信息
export function updateUser(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  })
}
// 更新用户信息
export function updateIsAvailable(userId, isAvailable) {
  return request({
    url: '/user/updateIsAvailable',
    method: 'get',
    params: { userId, isAvailable }
  })
}
// 重置用户密码
export function resetPassword(userId, resetPassword) {
  return request({
    url: '/user/resetPassword',
    method: 'post',
    params: { userId, resetPassword }
  })
}

// 删除用户
export function removeUser(userId) {
  return request({
    url: '/user/removeUser',
    method: 'get',
    params: { userId }
  })
}

// 获取所有权限

export function fetchRoleList() {
  return request({
    url: '/user/roles',
    method: 'post'
  })
}

// 获取所有权限

export function fetchUserRoleList(userId) {
  return request({
    url: '/user/userRoles',
    method: 'get',
    params: { userId }
  })
}

export function bindCrmUser(data) {
  return request({
    url: '/user/bindCrmUser',
    method: 'post',
    data
  })
}

// 获取代理树形结构数据

export function fetchUserTreeList(userName) {
  return request({
    url: '/user/fetchUserTreeList',
    method: 'get',
    params: { userName }
  })
}
export function indexInfo(query) {
  return request({
    url: '/user/indexInfo',
    method: 'post',
    params: query
  })
}
export function exportUserExcel(query) {
  return request({
    url: '/user/exportUserExcel',
    method: 'post',
    params: query
  })
}

export function exportAchievementExcel(query) {
  return request({
    url: '/user/exportAchievementExcel',
    method: 'post',
    params: query
  })
}

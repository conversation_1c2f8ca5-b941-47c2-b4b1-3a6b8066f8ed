package com.ews.system.service;

import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import com.ews.system.entity.LoginUser;
import com.ews.system.entity.User;
import com.ews.system.model.IPermission;
import com.ews.system.model.IUserRole;


@Component
public interface UserService
{
	
	
	

	/**
	 * 分页查询方法
	 * @param page
	 * @param size
	 * @param sortName
	 * @param sortOrder
	 * @param role
	 * @return
	 */
	Page<User> findAll(Integer page, Integer size, String sortName,String sortOrder,final User user);



	User findByUserName(String userName);

	/**
	 * 通过id获取未被逻辑删除的用户
	 * @param userId
	 * @return
	 */
    User findUserById(Long userId);

    User save(User user);


    List<IUserRole> findUserRoleByUserName(String userName);

    List<IUserRole> findAllUserRoleByUserId(Long userId);

    List<IPermission> findUserRolePermissionByUserName(String userName);

    Page<User> findAllByUserNameContains(String userName, Pageable pageable);

    void deleteAllUserByUserIdList(List<Long> userIdList);

    void deleteAllUserRoleByUserIdList(List<Long> userIdList);

    void deleteAllUserRoleByUserId(Long userId);

    void grantUserRole(Long userId, List<Long> roleIdList);
    
    /**
     * 查询用户状态是否可用
     * @param username
     * @return
     */
    boolean checkUserIsNormal(String username);
    
    /**
     * 保存或更新user方法
     * @param user
     * @param roleIdsStr
     * @return User
     */
    User saveOrUpdate(User user,String[] roleIdsStr);
    
    /**
     * 更改用户状态
     * @param userId
     * @param isAvailable
     * @return
     */
    boolean updateIsAvailableById(Long userId, Integer isAvailable) ;
    
    /**
     * 逻辑删除用户
     * @param userId
     * @return
     */
    boolean logicalDeleteByUserId(Long userId);
    
    /**
     * 更新用户密码
     * @param userId
     * @param password
     * @return
     */
    boolean updatePasswordByUserId(Long userId,String password);
    
    /**
     * 查询当前用户名是否存在 
     * @param userId
     * @param username
     * @return
     */
    boolean checkExistedByIdAndUserName(Long userId,String username);
    
    /**
     * 查询当前邮件地址是否存在
     * @param userId
     * @param email
     * @return
     */
    boolean checkExistedByIdAndEmail(Long userId,String email);
    
    /**
     * 查询用户名和邮件是否存在
     * @param userId
     * @param email
     * @param username
     * @return
     */
    Map<String,String> checkExistedByIdAndUserNameAndEmail(Long userId,String username,String email);
    
    /**
     * 通过用户名查询该用户的loginUser实体信息
     * @param username
     * @return
     */
    LoginUser findLoginUserByUserName(String username);
}

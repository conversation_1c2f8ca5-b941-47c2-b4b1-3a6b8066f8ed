package com.ews.system.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;


@Entity
@Table(name = "sys_permission")
public class Permission implements Serializable
{
	private static final long serialVersionUID = 1L;

    /**
    *权限id
    **/
	@Id
	@GeneratedValue(strategy=GenerationType.AUTO)
	@Column(name = "permission_id")
	private  Long permissionId;
    /**
    *权限名
    **/
	@Column(name = "permission_name")
	private  String permissionName;
    /**
    *权限标识,程序中判断使用,如"user:create"
    **/
	@Column(name = "permission_sign")
	private  String permissionSign;
    /**
    *权限描述,UI界面显示使用
    **/
	@Column(name = "description")
	private  String description;
    /**
    *权限分组，用来分组显示。
    **/
	@Column(name = "permission_group")
	private  String permissionGroup;
	
	
	/**
	 * vo属性，标记是否选中
	 */
	 @Transient
	private int isChecked;
	 
	 
	 
    public Long  getPermissionId()
    {
        return permissionId;
    }
    public void setPermissionId(Long  permissionId)
    {
        this.permissionId = permissionId;
    }
    public String  getPermissionName()
    {
        return permissionName;
    }
    public void setPermissionName(String  permissionName)
    {
        this.permissionName = permissionName;
    }
    public String  getPermissionSign()
    {
        return permissionSign;
    }
    public void setPermissionSign(String  permissionSign)
    {
        this.permissionSign = permissionSign;
    }
    public String  getDescription()
    {
        return description;
    }
    public void setDescription(String  description)
    {
        this.description = description;
    }
    public String  getPermissionGroup()
    {
        return permissionGroup;
    }
    public void setPermissionGroup(String  permissionGroup)
    {
        this.permissionGroup = permissionGroup;
    }
    
    
   
	public int getIsChecked() {
		return isChecked;
	}
	public void setIsChecked(int isChecked) {
		this.isChecked = isChecked;
	}
   
	

}

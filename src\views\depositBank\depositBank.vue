<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userTable.add') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label1')" prop="bankName" min-width="150px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label2')" prop="currencyType" min-width="120px" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.currencyType==1">GateWay</span>
          <span v-if="scope.row.currencyType==2">Bank</span>
          <span v-if="scope.row.currencyType==3">Wallet</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label4')" prop="bankAccount" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankAccount }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label10')" prop="bankAddress" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bankAddress }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label13')" prop="isShow" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.isShow==1?'Must Pass':'Need Not' }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label11')" prop="tel" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tel }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('depositBank.label12')" prop="swift" min-width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.swift }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('userTable.actions')" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)"> {{ $t('userTable.edit') }}</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />
    <el-dialog title="edit" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="160px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('depositBank.label1')" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label2')" prop="currencyType">
          <el-select v-model="temp.currencyType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="hb in bankTypes"
              :key="hb.value"
              :label="hb.label"
              :value="hb.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label3')" prop="accountName">
          <el-input v-model="temp.accountName" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label4')" prop="bankAccount">
          <el-select v-model="temp.bankAccount" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="hb in HBS"
              :key="hb.value"
              :label="hb.label"
              :value="hb.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label5')" prop="backup12">
          <el-input v-model="temp.backup12" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label6')" prop="backup13">
          <el-input v-model="temp.backup13" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" label="SWIFT CODE" prop="backup14">
          <el-input v-model="temp.backup14" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label7')" prop="backup15">
          <el-input v-model="temp.backup15" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label8')" prop="backup16">
          <el-input v-model="temp.backup16" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==3" :label="$t('depositBank.label9')" prop="backup17">
          <el-input v-model="temp.backup17" />
        </el-form-item>

        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label10')" prop="bankAddress">
          <el-input v-model="temp.bankAddress" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label11')" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label12')" prop="swift">
          <el-input v-model="temp.swift" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label13')" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label14')" prop="backup1">
          <el-select v-model="listBackup1" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label15')" prop="backup2">
          <el-select v-model="listBackup2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option2s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label16')" prop="backup3">
          <el-select v-model="listBackup3" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option3s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label17')" prop="backup11">
          <el-input v-model="temp.backup11" />
        </el-form-item>

        <el-form-item label="Display Notes (en)" prop="backup4">
          <el-input v-model="temp.backup4" />
        </el-form-item>
        <el-form-item label="Display Notes (zh-cn)" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
        <el-form-item label="Display Notes (zh-tw)" prop="backup6">
          <el-input v-model="temp.backup6" />
        </el-form-item>
        <el-form-item label="Display Notes (th)" prop="backup7">
          <el-input v-model="temp.backup7" />
        </el-form-item>
        <el-form-item label="Display Notes (ms)" prop="backup8">
          <el-input v-model="temp.backup8" />
        </el-form-item>
        <el-form-item label="Display Notes (vi)" prop="backup9">
          <el-input v-model="temp.backup9" />
        </el-form-item>
        <el-form-item label="Display Notes (id)" prop="backup10">
          <el-input v-model="temp.backup10" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="createData()">{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="edit" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="160px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('depositBank.label1')" prop="bankName">
          <el-input v-model="temp.bankName" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label2')" prop="currencyType">
          <el-select v-model="temp.currencyType" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="hb in bankTypes"
              :key="hb.value"
              :label="hb.label"
              :value="hb.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label3')" prop="accountName">
          <el-input v-model="temp.accountName" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label4')" prop="bankAccount">
          <el-select v-model="temp.bankAccount" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="hb in HBS"
              :key="hb.value"
              :label="hb.label"
              :value="hb.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label5')" prop="backup12">
          <el-input v-model="temp.backup12" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label6')" prop="backup13">
          <el-input v-model="temp.backup13" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" label="SWIFT CODE" prop="backup14">
          <el-input v-model="temp.backup14" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label7')" prop="backup15">
          <el-input v-model="temp.backup15" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==2" :label="$t('depositBank.label8')" prop="backup16">
          <el-input v-model="temp.backup16" />
        </el-form-item>
        <el-form-item v-if="temp.currencyType==3" :label="$t('depositBank.label9')" prop="backup17">
          <el-input v-model="temp.backup17" />
        </el-form-item>

        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label10')" prop="bankAddress">
          <el-input v-model="temp.bankAddress" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label11')" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item :label="$t('depositBank.label12')" prop="swift">
          <el-input v-model="temp.swift" />
        </el-form-item>

        <el-form-item :label="$t('depositBank.label13')" prop="isShow">
          <el-select v-model="temp.isShow" :placeholder="$t('userTable.placeholder3')">
            <el-option
              v-for="isShow in isShows"
              :key="isShow.value"
              :label="isShow.label"
              :value="isShow.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label14')" prop="backup1">
          <el-select v-model="listBackup1" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label15')" prop="backup2">
          <el-select v-model="listBackup2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option2s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label16')" prop="backup3">
          <el-select v-model="listBackup3" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option3s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="temp.currencyType==1" :label="$t('depositBank.label17')" prop="backup11">
          <el-input v-model="temp.backup11" />
        </el-form-item>

        <el-form-item label="Display Notes (en)" prop="backup4">
          <el-input v-model="temp.backup4" />
        </el-form-item>
        <el-form-item label="Display Notes (zh-cn)" prop="backup5">
          <el-input v-model="temp.backup5" />
        </el-form-item>
        <el-form-item label="Display Notes (zh-tw)" prop="backup6">
          <el-input v-model="temp.backup6" />
        </el-form-item>
        <el-form-item label="Display Notes (th)" prop="backup7">
          <el-input v-model="temp.backup7" />
        </el-form-item>
        <el-form-item label="Display Notes (ms)" prop="backup8">
          <el-input v-model="temp.backup8" />
        </el-form-item>
        <el-form-item label="Display Notes (vi)" prop="backup9">
          <el-input v-model="temp.backup9" />
        </el-form-item>
        <el-form-item label="Display Notes (id)" prop="backup10">
          <el-input v-model="temp.backup10" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false"> {{ $t('table.cancel') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import waves from '@/directive/waves' // waves directive
import { fetchCountriesList } from '@/api/countries'
import { fetchALLList } from '@/api/userTag'
import { fetchAccountTypeList } from '@/api/accountType'
import { fetchList, fetchDepositBank, createDepositBank, updateDepositBank, updateIsAvailable, removeDepositBank } from '@/api/depositBank'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'
import Setting from '@/settings'
export default {
  name: 'DepositBankTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10
      }, listQuery2: {
			 		page: 1,
        limit: 1000

      },
      temp: {
        id: undefined,
        bankName: '',
        bankAccount: '',
        bankAddress: '',
        accountName: '',
        tel: '',
        swift: '',
        currencyType: '',
        isShow: ''
      },
      option3s: [],
      listBackup3: [],
      option2s: [],
      listBackup2: [],
      option1s: [],
      listBackup1: [],
      dialogFormAddVisible: false,
      dialogFormEditVisible: false,
      currency_type: 'CASH',
				 currencyTypes: [

      ],
				 isShows: [
        {
          value: 1,
          label: 'Must Pass'
        },
        {
          value: 2,
          label: 'Need Not'
        }
      ],
      HBS: [
        {
          value: 'USD',
          label: 'USD'
        }, {
          value: 'USDT TRC20',
          label: 'USDT TRC20'
        }, {
          value: 'USDT ERC20',
          label: 'USDT ERC20'
        }, {
          value: 'USDT BEP20',
          label: 'USDT BEP20'
        },  {
          value: 'BTC',
          label: 'BTC'
        }

      ],
      bankTypes: [
         {
          value: 2,
          label: 'Bank'
        }, {
          value: 3,
          label: 'Wallet'
        }

      ],
      rules: {
        bankName: [
          { required: true, message: 'Channel name cannot be empty', trigger: 'change' },,
        ],
        bankAccount: [
          { required: true, message: 'Channel currency cannot be empty', trigger: 'change' },,
        ],
        bankAddress: [,

        ],
        accountName: [,

        ],
        tel: [,

        ],
        swift: [
        ],
        currencyType: [,

        ],
        isShow: [,

        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    this.getList()

    this.option3s = []
    this.option2s = []
    this.option1s = []

    fetchALLList(this.listQuery2).then(response => {
      var datas4 = response.data.items
             	for (var j = 0; j < datas4.length; j++) {
             		var id = '"' + datas4[j].id + '"'
             		var name = datas4[j].tagName
             		var per = []
             		per.value = id + ''
             		per.label = name
        this.option3s.push(per)
      }
    })

    fetchCountriesList(this.listQuery2).then(response => {
      var datas4 = response.data.items
             	for (var j = 0; j < datas4.length; j++) {
             		var id = '"' + datas4[j].countryCodeThree + '"'
             		var name = datas4[j].countryName
             		var per = []
             		per.value = id + ''
             		per.label = name
        this.option1s.push(per)
      }
    })

    fetchAccountTypeList(this.listQuery2).then(response => {
      var datas4 = response.data.items
             	for (var j = 0; j < datas4.length; j++) {
             		var id = '"' + datas4[j].id + '"'
             		var name = datas4[j].typeName
             		var per = []
             		per.value = id + ''
             		per.label = name
        this.option2s.push(per)
      }
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        bankName: '',
        bankAccount: '',
        bankAddress: '0',
        accountName: '',
        tel: '',
        swift: '',
        currencyType: '',
        isShow: ''
      }
    },
    formatCurrencyType(row, column) {
      if (row.currencyType == 1) {
        return 'USDT'
      } else if (row.currencyType == 2) {
        return 'CNY'
      } else {
        return 'USD'
      }
    },
    handleCreate() {
      this.resetTemp()
      this.listBackup1 = []
      this.listBackup2 = []
      this.listBackup3 = []
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
      this.temp.currencyType = 3
      this.temp.isShow = 1
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.backup1 = this.listBackup1
          this.temp.backup2 = this.listBackup2
          this.temp.backup3 = this.listBackup3
          createDepositBank(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataEditForm'].validate((valid) => {
        if (valid) {
          this.temp.backup1 = this.listBackup1
          this.temp.backup2 = this.listBackup2
          this.temp.backup3 = this.listBackup3
          updateDepositBank(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormEditVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)

      this.listBackup3 = []
      var datas3 = eval(row.backup3)

      for (var m = 0; m < datas3.length; m++) {
        this.listBackup3.push('"' + datas3[m] + '"')
      }

      this.listBackup2 = []
      var datas2 = eval(row.backup2)

      for (var m = 0; m < datas2.length; m++) {
        this.listBackup2.push('"' + datas2[m] + '"')
      }

      this.listBackup1 = []
      var datas1 = eval(row.backup1)

      for (var m = 0; m < datas1.length; m++) {
        this.listBackup1.push('"' + datas1[m] + '"')
      }

      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this data?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeDepositBank(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>

package com.ews;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.HttpResponse;

import com.alibaba.fastjson.JSONObject;
import com.wx.core.util.HttpClientUtil;

public class JsonRequestExample {
    public static void main(String[] args) {
        try {
        	
        	
        	//live.ctraderapi.com:5036 （用于使用 正式地址）
        	Map<String, String> headers = new HashMap<>();
            headers.put("Content-type", "application/json");
            Map<String, String> body = new HashMap<>();
            
            JSONObject paramMap = new JSONObject();
            paramMap.put("clientMsgId", "cm_id_2_"+new Date().getTime());
            paramMap.put("payloadType", 2100);
            
            JSONObject client = new JSONObject();
            client.put("clientId", "13807_AedzhM6TTB6rfzlyscnYeGKMfUAQLeLhut2cW2h5DQIlJNazYs");
            client.put("clientSecret", "JAsJse7bw1o6yuLL7phBmbf13C6Ud7H4Vuwmpsz071OlXzOX0j");
            paramMap.put("payload", client.toJSONString());
            
            
            body.put("data", paramMap.toJSONString());
            
            System.out.println(paramMap.toJSONString());
            
        	HttpResponse responses = HttpClientUtil.doPost("https://demo.ctraderapi.com:5036","", headers,null, body);
        	//System.out.println(responses);
           
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

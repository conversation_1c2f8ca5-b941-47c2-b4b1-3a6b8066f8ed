package com.ews.system.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.ews.system.entity.User;
import com.ews.system.model.IPermission;
import com.ews.system.model.IUserRole;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

	User findByUsername(String username);

	Page<User> findAllByUsernameContains(String username, Pageable pageable);

	// 排除现有用户的情况下，查询用户
	@Query(value = "select * from sys_user where user_id<> ?1 and username=?2 and is_deleted = 0", nativeQuery = true)
	User findUserExistByUserName(Long userId, String username);

	// 排除现有用户的情况下，查询用户
	@Query(value = "select * from sys_user where user_id<> ?1 and email=?2 and is_deleted = 0", nativeQuery = true)
	User findUserExistByEmail(Long userId, String email);

	// 排除现有用户的情况下，查询用户
	@Query(value = "select * from sys_user where user_id<> ?1 and (email=?2 or username=?3) and is_deleted = 0", nativeQuery = true)
	User checkExistedByIdAndUserNameAndEmail(Long userId, String email, String username);

	// 根据userid列表删除所有用户
	@Modifying
	@Transactional
	@Query(value = "delete from sys_user where user_id in (?1)", nativeQuery = true)
	void deleteAllUserByUserIdList(List<Long> userIdList);

	// 根据userid删除用户角色关联表里的记录
	@Modifying
	@Transactional
	@Query(value = "delete from sys_user_role where user_id in (?1)", nativeQuery = true)
	void deleteAllUserRoleByUserIdList(List<Long> userIdList);

	// 根据userid删除用户角色关联表里的记录
	@Modifying
	@Transactional
	@Query(value = "delete from sys_user_role where user_id = ?1", nativeQuery = true)
	void deleteAllUserRoleByUserId(Long userId);

	// 新增用户和角色关联记录
	@Modifying
	@Transactional
	@Query(value = "insert into sys_user_role(user_id,role_id) VALUES(?1,?2)", nativeQuery = true)
	void insertUserRole(Long userId, Long roleId);

	// 根据用户名获取用户所具备的角色列表
	@Query(value = "select a.user_id as userId,a.username as userName,c.role_id as roleId,c.role_name as role,c.description from sys_user a\n"
			+ "inner join sys_user_role b on a.user_id = b.user_id \n"
			+ "inner join sys_role c on b.role_id=c.role_id and c.is_available=1\n"
			+ "where a.username=?1 and a.is_deleted=0", countQuery = "select count(*) from user a\n"
					+ "inner join sys_user_role b on a.user_id = b.user_id \n"
					+ "inner join sys_role c on b.role_id=c.role_id and c.is_available=1\n"
					+ "where a.username=?1 and a.is_deleted=0   ", nativeQuery = true)
	List<IUserRole> findUserRoleByUsername(String username);

	// 根据用户id，列出所有角色，包括该用户不具备的角色，该用户不具备角色的时候，userid和username为null，可以做业务判断
	@Query(value = "select a.role_id as roleId,a.role_sign as roleSign,a.description,c.user_id as userId,c.username as userName,a.role_name as role from sys_role a\n"
			+ "left join sys_user_role b on a.role_id=b.role_id and a.is_available=1 and b.user_id=?1\n"
			+ "left join sys_user c on c.user_id=b.user_id;", countQuery = "select count(*) from sys_role a\n"
					+ "left join sys_user_role b on a.role_id=b.role_id and a.is_available=1 and b.user_id=?1\n"
					+ "left join sys_user c on c.user_id=b.user_id;", nativeQuery = true)
	List<IUserRole> findAllUserRoleByUserId(Long userId);

	// 根据用户名，获取用户具备的权限。
	@Query(value = "select a.user_id as userId,a.username as userName,d.permission_id as permissionId,d.permission_sign as permissionSign,d.permission_name as permissionName from sys_user a \n"
			+ "inner join sys_user_role b on a.user_id = b.user_id \n"
			+ "inner join sys_role_permission c on b.role_id = c.role_id\n"
			+ "inner join sys_permission d on c.permission_id=d.permission_id\n"
			+ "where a.username=?1", countQuery = "select a.user_id,a.username,d.permission_id,d.permission_sign,d.permission_name from sys_user a \n"
					+ "inner join sys_user_role b on a.user_id = b.user_id \n"
					+ "inner join sys_role_permission c on b.role_id = c.role_id \n"
					+ "inner join sys_permission d on c.permission_id = d.permission_id \n"
					+ "where a.username=?1", nativeQuery = true)
	List<IPermission> findUserRolePermissionByUsername(String username);

	/**
	 * 分页查询方法
	 * 
	 * @param start
	 * @param pageSize
	 * @param where
	 * @return
	 */
	@Query(value = "select sys_user.user_id,sys_user.username,sys_user.nick_name,sys_user.is_available,sys_user.gmt_create,IFNULL(sys_role.role_id,''),IFNULL(sys_role.role_name,''),IFNULL(sys_role.description,'') from \n"
			+ " sys_user left join sys_user_role on sys_user.user_id = sys_user_role.user_id \n"
			+ " left join sys_role on sys_role.role_id = sys_user_role.role_id  where 1=1 ?1  ?2 ", nativeQuery = true)
	List<Object[]> findUserPage(String where, String limit);

	/**
	 * 计数查询方法
	 * 
	 * @param where
	 * @return
	 */
	@Query(value = "select count(*) from \n"
			+ " sys_user left join sys_user_role on sys_user.user_id = sys_user_role.user_id \n"
			+ " left join sys_role on sys_role.role_id = sys_user_role.role_id where 1=1 ?1 ", nativeQuery = true)
	Integer counUser(String where);

	// 更新可用状态
	@Transactional
	@Modifying
	@Query(value = "update sys_user set is_available=?2 where user_id=?1", nativeQuery = true)
	int updateIsAvailableById(Long userId, Integer isAvailable);

	// 逻辑删除
	@Transactional
	@Modifying
	@Query(value = "update sys_user set is_deleted=1 where user_id=?1", nativeQuery = true)
	int logicalDeleteById(Long userId);

	/**
	 * 查询删除状态正常的用户
	 * 
	 * @param userId
	 * @return
	 */
	@Query(value = "select * from sys_user where user_id=?1 and is_deleted = 0 ", nativeQuery = true)
	User findNormalUserById(Long userId);
}

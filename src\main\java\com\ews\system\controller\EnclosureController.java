
package com.ews.system.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONObject;

import org.apache.poi.ss.usermodel.DateUtil;
import com.ews.common.Result;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;

import com.ews.system.entity.Enclosure;
import com.ews.system.service.EnclosureService;



@RestController
@RequestMapping("admin/enclosure")
public class EnclosureController {
	@Autowired
	private EnclosureService enclosureService;

	@Autowired
	private ConstantConfig constantConfig ;
	


   /**
	* 获取请求地址
	* @param request
	* @return
	*/
    @PostMapping("/getUploadUrl")
    public ResponseData getUploadUrl(HttpServletRequest request) {
    	try {
        	String url = constantConfig.getUploadServiceDomainName().concat(constantConfig.getUploadServiceAction());
        	System.out.println("上传服务器地址："+url);
        	return ResponseDataUtil.buildSuccess(url);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    

	/**
	 * 上传
	 * 
	 * @param request
	 * @param linkImg_temp
	 * @return
	 */
	@RequestMapping(value = "/upload")
	@ResponseBody
	public Object uploadUserIcon(HttpServletRequest request, MultipartFile file) {
		try {
			if (file != null) {
				Enclosure enclosure = new Enclosure();
				String fileName = new Date().getTime() + "."
						+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
				File reFile = new File(constantConfig.getFileStoreUrl());
				if (!reFile.exists()) {
					reFile.mkdirs();
				}
				File targetFile = new File(constantConfig.getFileStoreUrl() + fileName);
				
				file.transferTo(targetFile);// 保存文件
				enclosure.setEndixType(file.getContentType());
				enclosure.setEndixName(file.getOriginalFilename());
				enclosure.setEndixUrl(constantConfig.getUploadServiceDomainName().concat(constantConfig.getFileStoreUrlPrefix()).concat(fileName));
				
				Result result = enclosureService.saveOrUpdate(enclosure);
				if (result.getCode().equals(Result.CODEFAIL)) {
					return ResponseDataUtil.buildError("文件保存失败");
				}
				return ResponseDataUtil.buildSuccess(enclosure);
			} else {
				return ResponseDataUtil.buildError("缺少文件");
			}

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}
	
	
	
}

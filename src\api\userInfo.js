import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/userInfo/list',
    method: 'post',
    params: query
  })
}
export function fetchList2(query) {
  return request({
    url: '/userInfo/list2',
    method: 'post',
    params: query
  })
}
export function updateUserInfo_audit(data) {
  return request({
    url: '/userInfo/audit',
    method: 'post',
    data
  })
}
export function fetchUserInfo(id) {
  return request({
    url: '/userInfo/detail',
    method: 'get',
    params: { id }
  })
}

export function createUserInfo(data) {
  return request({
    url: '/userInfo/add',
    method: 'post',
    data
  })
}

export function updateUserInfo(data) {
  return request({
    url: '/userInfo/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/userInfo/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeUserInfo(id, bhyy) {
  return request({
    url: '/userInfo/remove',
    method: 'get',
    params: { id, bhyy }
  })
}
export function bohuiUserInfo(id, bhyy) {
  return request({
    url: '/userInfo/bohui',
    method: 'get',
    params: { id, bhyy }
  })
}

// 重置用户密码
export function resetPassword(id, resetPassword) {
  return request({
    url: '/userInfo/resetPassword',
    method: 'post',
    params: { id, resetPassword }
  })
}

export function exportUserInfoExcel(query) {
  return request({
    url: '/userInfo/exportUserInfoExcel',
    method: 'post',
    params: query
  })
}

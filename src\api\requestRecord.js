import request from '@/utils/request'
export function fetchList(query) {
  return request({
    url: '/requestRecord/list',
    method: 'post',
    params: query
  })
}

export function fetchRequestRecord(id) {
  return request({
    url: '/requestRecord/detail',
    method: 'get',
    params: { id }
  })
}

export function createRequestRecord(data) {
  return request({
    url: '/requestRecord/add',
    method: 'post',
    data
  })
}

export function updateRequestRecord(data) {
  return request({
    url: '/requestRecord/update',
    method: 'post',
    data
  })
}
export function updateIsAvailable(id, isAvailable) {
  return request({
    url: '/requestRecord/updateIsAvailable',
    method: 'get',
    params: { id, isAvailable }
  })
}
export function removeRequestRecord(id) {
  return request({
    url: '/requestRecord/remove',
    method: 'get',
    params: { id }
  })
}


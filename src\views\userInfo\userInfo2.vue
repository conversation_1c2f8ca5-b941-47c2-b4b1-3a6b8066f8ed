<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.fullname" :placeholder="$t('userInfo.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tel" :placeholder="$t('userInfo.label3')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.userName" :placeholder="$t('userInfo.label1')" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.parent_ID" :placeholder="$t('userInfo.label13')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.backup5" :placeholder="$t('userInfo.label14')" style="width: 180px" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label2')" prop="fullname" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}{{ scope.row.surname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label1')" prop="userName" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label3')" prop="tel" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tel }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('userInfo.label17')" min-width="180px" align="center" prop="gmtCreate" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label13')" prop="backup2" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label14')" prop="kycInfo" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.kycInfo }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="(this.dqryType=='999'||this.dqryType=='0')?true:false" :label="$t('userTable.actions')" align="center" width="420" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="handleUpdate(scope.row)">{{ $t('userInfo.label15') }}</el-button>
          <el-button type="warning" size="small" @click="handleResetPassword(scope.row)">
            {{ $t('userTable.resetPassword') }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="(this.dqryType!='999'&&this.dqryType!='0')?true:false" :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="handleUpdate(scope.row)">{{ $t('userInfo.label15') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <el-dialog title="UpdateInfo" :visible.sync="dialogFormAddVisible2" :close-on-click-modal="false">
      <el-form ref="dataForm2" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">

        <el-form-item :label="$t('userInfoAudit.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label2')" prop="fullname">
          {{ temp.name }}	{{ temp.surname }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label3')" prop="tel">
          {{ temp.tel }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label4')" prop="tel">
          {{ temp.province }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label5')" prop="country">
          {{ temp.adress }} {{ temp.city }} {{ temp.country }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          {{ temp.identityNum }}
        </el-form-item>

        <el-form-item :label="$t('userInfoAudit.label7')" prop="imageFront">
          <el-image v-if="temp.imageFront" :src="temp.imageFront" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.imageBack" :src="temp.imageBack" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.backup3" :src="temp.backup3" style="height:150px;" :preview-src-list="srcList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible2 = false">{{ $t('userInfoAudit.label19') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog title="UserInfo" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('userInfoAudit.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label2')" prop="fullname">
          {{ temp.name }}	{{ temp.surname }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label3')" prop="tel">
          {{ temp.tel }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label4')" prop="tel">
          {{ temp.province }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label5')" prop="country">
          {{ temp.adress }} {{ temp.city }} {{ temp.country }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          {{ temp.identityNum }}
        </el-form-item>

        <el-form-item :label="$t('userInfoAudit.label7')" prop="imageFront">
          <el-image v-if="temp.imageFront" :src="temp.imageFront" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.imageBack" :src="temp.imageBack" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.backup3" :src="temp.backup3" style="height:150px;" :preview-src-list="srcList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false">CLOSE</el-button>
      </div>
    </el-dialog>

    <!--重置密码弹窗-->
    <el-dialog :title="$t('userTable.resetPassword')" :visible.sync="dialogFormResetPasswordVisible" :close-on-click-modal="false">
      <el-form ref="dataPassForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 400px; margin-left:10px;">
        <el-form-item :label="$t('userInfo.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userTable.newPassword')" prop="password">
          <el-input v-model="temp.password" :placeholder="$t('userTable.newPassword')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormResetPasswordVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="resetPassword()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  	getUploadUrl
} from '@/api/upload'
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchUserInfo, createUserInfo, updateUserInfo, updateIsAvailable, removeUserInfo, resetPassword, exportUserInfoExcel } from '@/api/userInfo'
import { fetchALLList } from '@/api/userTag'
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth.js'
import Pagination from '@/components/Pagination'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'
export default {
  name: 'UserInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      uploadUrl: '',
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        fullname: undefined,
        userName: undefined,
        email: undefined,
        parent_ID: undefined,
        tel: undefined,
        gmtCreateSearchBegin: undefined,
        gmtCreateSearchEnd: undefined,
        invitationCode: undefined,
        userType: undefined,
        parentId: undefined,
        pinyin: undefined,
        isAgent: undefined,
        backup5: undefined

      },	listQuery2: {
			 		page: 1,
        limit: 1000

      },
      tagOptions: [],
      option1s: [],
      rekebackRule2: [],
      pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }

        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
					 }]				},
      uploadData: {
        id: undefined,
        type: 1
      },
      myHeaders: {
				 'X-Token': getToken()
      },
      temp: {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
          	password: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        imageBack: '',
        invitationCode: '',
        rekebackRule: [],
        roles: [],
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: '',
        contactInformations: [{
          value: ''
        }]

      }, dqryType: undefined, xbList: [

          					{
          						value: '1',
          						label: '男'
          					},
          					{
          						value: '2',
          						label: '女'
          					}
          					],

      srcList: [

      ],
      valueGmtCreate: undefined,
      dialogFormResetPasswordVisible: false,
      dialogFormAddVisible: false,
      dialogFormAddVisible2: false,
      dialogFormEditVisible: false,
      rules: {
        surname: [
          { required: true, message: '姓名不能为空', trigger: 'change' },,
        ],
        name: [
          { required: true, message: '姓名不能为空', trigger: 'change' },,
        ],
        fullname: [
        ],
        userName: [
          { required: true, message: '登陆邮箱不能为空', trigger: 'change' },,
        ],
        email: [,
        ],
        tel: [,
        ],
        password: [
          { required: true, message: '初始密码不能为空', trigger: 'change' }
        ],
        country: [,
        ],
        province: [
        ],
        city: [
        ],
        gender: [
        ],
        adress: [
        ],
        nickname: [
        ],
        birthday: [
        ],
        identityNum: [
        ],
        imageFront: [
        ],
        imageBack: [
        ],
        invitationCode: [
        ],
        amountMain: [
        ],
        amountSub: [
        ],
        userType: [
        ],
        parentId: [
        ],
        pinyin: [
        ],
        remark: [
        ],
        rakeBackType: [
        ],
        rakeBackAmount: [
        ],
        isAgent: [
        ],
        auditId: [
        ],
        auditTime: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        console.log('this.temp.dqryType' + this.temp.dqryType)
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })

    this.getUploadUrl()
    this.getList()

    this.option1s = []

    fetchALLList(this.listQuery2).then(response => {
      var datas4 = response.data.items
              	for (var j = 0; j < datas4.length; j++) {
              		var id = datas4[j].id + ''
              		var name = datas4[j].tagName
              		var per = []
              		per.value = id
              		per.label = name
        this.option1s.push(per)
        this.tagOptions.push(per)
      }
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    }, getUploadUrl() {
      getUploadUrl().then(res => {
        if (res.code != 20000) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
        this.uploadUrl = res.msg
      })
    },
    handleAvatarSuccess(res, file) {
      this.temp.imageFront = res.data.endixUrl
    },
    handleAvatarSuccess2(res, file) {
    	this.temp.imageBack = res.data.endixUrl
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    handleFilter() {
      if (this.valueGmtCreate) {
        this.listQuery.gmtCreateSearchBegin = parseTime(new Date(this.valueGmtCreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
        this.listQuery.gmtCreateSearchEnd = parseTime(new Date(this.valueGmtCreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        this.listQuery.gmtCreateSearchBegin = undefined
        this.listQuery.gmtCreateSearchEnd = undefined
      }
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    }, // 重置密码页面
    handleResetPassword(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.temp.password = ''
      this.dialogFormResetPasswordVisible = true
      this.$nextTick(() => {
			  this.$refs['dataPassForm'].clearValidate()
      })
    }, removeContactInformation(item) {
      var index = this.temp.contactInformations.indexOf(item)
      if (index !== -1) {
        this.temp.contactInformations.splice(index, 1)
      }
    },
    addContactInformation() {
      this.temp.contactInformations.push({
        value: '',
        key: Date.now()
      })
    },
    resetPassword() {
			    resetPassword(this.temp.id, this.temp.password).then(result => {
        if (result.code == 20000) {
							 this.$notify({
							  title: 'success',
							  message: 'Successfully modified password',
							  type: 'success',
							  duration: 2000
          })
          this.dialogFormResetPasswordVisible = false
        } else {
          this.$message.error(response.message)
        }
			    })
    }, handleExport() {
					 exportUserInfoExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        password: '',
        imageBack: '',
        invitationCode: '',
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: '',
        rekebackRule: [],
        roles: []
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.gender = '1'
      this.temp.password = 'abc123'
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createUserInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    updateData() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          this.temp.rekebackRule = this.rekebackRule2
          updateUserInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'oper success',	type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible2 = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)

      this.srcList = [this.temp.imageFront, this.temp.imageBack]
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },	handleEdit(row) {
      this.temp = Object.assign({}, row)

      this.rekebackRule2 = []
      var datas = eval(row.backup5)

      for (var m = 0; m < datas.length; m++) {
        this.rekebackRule2.push(datas[m])
      }
      this.temp.gender = row.gender + ''
      this.dialogFormAddVisible2 = true
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm('Are you sure you want to delete this CRM user?', 'INFO', {
				 		confirmButtonText: 'OK',
        cancelButtonText: 'CANCEL',
        type: 'warning'
      })
        .then(async() => {
				 		await removeUserInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: 'success',
                message: 'delete success',
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>
<style>
	.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>

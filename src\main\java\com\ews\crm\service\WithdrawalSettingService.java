package com.ews.crm.service;

import org.springframework.stereotype.Component;
import com.ews.common.Result;
import com.ews.system.entity.LoginUser; 
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import com.ews.crm.entity.WithdrawalSetting;


@Component
public interface WithdrawalSettingService
{

    /** 
     * 分页查询方法
     * @param page
     * @param size
     * @param sortName
     * @param sortOrder
     * @param withdrawalSetting
     * @return
     */
    Page<WithdrawalSetting> findAll(Integer page, Integer size, String sortName,String sortOrder,final WithdrawalSetting withdrawalSetting);

    /** 
     * 通过Id查询
     * @param id
     * @return
     */
    WithdrawalSetting findById(Long id);

    /**
     * 通过Id 删除实体 (逻辑删除)
     * @param Id
     * @return
     */
    Result removeEntityOfLogicalById(Long id);

    /**
     * 通过Id 删除实体 (物理删除)
     * @param Id
     * @return
     */
    Result removeEntityById(Long id);

    /**
     * 更新数据可用状态
     * @param Id
     * @param isAvailable
     * @return
     */
    Result updateIsAvailableById(Long id,Integer isAvailable);

    /**
     * 保存和更新方法
     * @param author
     * @return
     */
    Result saveOrUpdate(WithdrawalSetting withdrawalSetting);


}

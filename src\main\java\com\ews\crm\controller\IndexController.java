package com.ews.crm.controller;


import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

@RestController
public class IndexController {
	
	
	@RequestMapping("/")
	public void  defaut(Map<String, Object> map, HttpServletRequest request,HttpServletResponse response) throws IOException{
		String requestHeader = request.getHeader("user-agent");
        if(this.isMobileDevice(requestHeader)) {
        	response.sendRedirect("/trade/");//PC和手机端使用同一套页面，所以跳转相同地址	
        }
        response.sendRedirect("/trade/"); 
	}

	
  	/**
          * 判断请求来自手机端还是电脑端
     */
    public static boolean isMobileDevice(String requestHeader) {
        /*
         * android            :所有安卓设备
         * mas  os            :iphone
         * windows phone    :windows系统手机
         */
        String[] deviceArray = new String[] {"android","iphone","windows phone"};
        if( null == requestHeader) {
            return false;
        }
        requestHeader = requestHeader.toLowerCase();
        for(int i = 0;i<deviceArray.length;i++) {
            if(requestHeader.indexOf(deviceArray[i]) > 0) {
                return true;
            }
        }
        return false;    
    }
	
	
}
